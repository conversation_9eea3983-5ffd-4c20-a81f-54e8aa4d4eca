"use client";

import { PackageDetails } from '@/app/types/PackageDetails';
import { addDays, format, parseISO } from 'date-fns';

interface TripHighlightsProps {
  packageData?: PackageDetails;
  highlights?: { title: string; description: string; duration: string; image: string }[];
}

const TripHighlights: React.FC<TripHighlightsProps> = ({ packageData, highlights }) => {
  const activeTab = 'summary';

  if (highlights?.length) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {highlights.map((highlight, index) => (
          <div key={index} className="flex items-start gap-2">
            <span className="text-black text-base">•</span>
            <span className="text-base text-black font-normal">{highlight.title}</span>
          </div>
        ))}
      </div>
    );
  }

  if (!packageData || !packageData.days || !Array.isArray(packageData.days)) {
    console.warn('TripHighlights: No valid package data');
    return null;
  }

  return (
    <section id="trip-highlights" className="space-y-4 mb-8">
      <h2 className="text-xl font-bold text-black">Trip Highlights</h2>
      <ul className="space-y-3 list-disc list-inside">
        {packageData.days.map((day: any, index: number) => {
          const itineraryType = packageData?.itineraryType?.toLowerCase();
          const showDates = itineraryType === "final" || itineraryType === "quotation";
          const baseDate = showDates ? parseISO(packageData.packageStartDate as string) : null;
          const adjustedDate = baseDate ? addDays(baseDate, index) : null;

          const activities: string[] = [];

          day.cities?.forEach((city: any) => {
            if (city.itineraries?.length === 0) {
              activities.push(city?.title);
            } else {
              city.itineraries.forEach((itinerary: any) => {
                const type = itinerary.excursionType;

                const shouldInclude =
                  activeTab === "summary" ||
                  (activeTab === "flights" && type === "flights") ||
                  (activeTab === "hotels" && type?.includes("hotel")) ||
                  (activeTab === "activities" && type === "sightseeing") ||
                  (activeTab === "transfers" && ["intercity", "airport"].includes(type));

                if (shouldInclude) {
                  if (type.includes("hotel") && itinerary.selected_excursion?.hotelName) {
                    activities.push(itinerary.selected_excursion.hotelName);
                  } else if (type === "sightseeing" && itinerary.selected_excursion?.sightseeingName) {
                    activities.push(itinerary.selected_excursion.sightseeingName);
                  } else if (itinerary.subCodeDescriptor) {
                    activities.push(itinerary.subCodeDescriptor);
                  }
                }
              });
            }
          });

          const activityString = activities.filter(Boolean).join("; ");

          return (
            <li key={day.dayId ?? index} className="text-base text-black font-normal">
              <span className="mt-1">
                Day {day.day_number + 1}
                {showDates && adjustedDate ? ` - ${format(adjustedDate, 'EEEE, MMM dd')}` : ''}: {activityString}
              </span>
            </li>
          );
        })}
      </ul>
    </section>
  );
};

export default TripHighlights;
