'use client'
import React from "react";
import Link from "next/link";
import { useQueryStore } from "../store/queryStore";

const Footer = () => {
  const setQuery = useQueryStore((state) => state.setQuery);
  return (
    <footer className="w-full bg-gray-900 text-white py-12">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="flex flex-col lg:flex-row lg:justify-between items-start gap-12 lg:gap-16">
          {/* Left Column - Logo and Description */}
          <div className="w-full lg:w-2/5 lg:max-w-md">
            <div className="flex flex-col items-start">
              {/* Logo */}
              <img
                src="/assets/logotwo.png"
                alt="Logo"
                className="h-12 mb-4"
              />
              {/* Description */}
              <p className="text-sm text-gray-300 leading-relaxed mb-8 lg:mb-12">
                Making friends one traveller at a time! At the moment, offering expert crafted trips to 30+ European countries at unbelievable prices.
              </p>
            </div>

            {/* Social Media Logos */}
            <div className="flex space-x-4 mb-8">
              <a href="#" className="w-10 h-10 bg-blue-600 hover:bg-blue-700 rounded-full flex justify-center items-center transition-colors">
                <img src="/assets/fb_svg.svg" alt="Facebook" className="w-5 h-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-blue-600 hover:bg-blue-700 rounded-full flex justify-center items-center transition-colors">
                <img src="/assets/twitter_svg.svg" alt="Twitter" className="w-5 h-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-blue-600 hover:bg-blue-700 rounded-full flex justify-center items-center transition-colors">
                <img src="/assets/insta_svg.svg" alt="Instagram" className="w-5 h-5" />
              </a>
            </div>

            {/* Copyright */}
            <div className="text-sm text-gray-400">
              © 2025 WiseYatra. All Rights Reserved
            </div>
          </div>

          {/* Right Section - Navigation Links */}
          <div className="w-full lg:w-2/5 grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-6">
            {/* About Section */}
            <div>
              <h3 className="text-lg font-semibold mb-6 text-white">About</h3>
              <ul className="space-y-4">
                <li>
                  <Link href="/pages/about-us" className="text-sm text-gray-300 hover:text-white transition-colors">
                    About WiseYatra
                  </Link>
                </li>
                <li>
                  <Link href="/pages/refund-policy" className="text-sm text-gray-300 hover:text-white transition-colors">
                    Refund Policy
                  </Link>
                </li>
                <li>
                  <Link href="/pages/privacy-policy" className="text-sm text-gray-300 hover:text-white transition-colors">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="/pages/terms-conditions" className="text-sm text-gray-300 hover:text-white transition-colors">
                    Terms & Condition
                  </Link>
                </li>
              </ul>
            </div>

            {/* Tours Section */}
            <div>
              <h3 className="text-lg font-semibold mb-6 text-white">Tours</h3>
              <ul className="space-y-4">
                <li>
                  <Link href="/vacation/tours?query=europe" onClick={()=>{
                      setQuery('europe');
                  }} className="text-sm text-gray-300 hover:text-white transition-colors">
                    Europe
                  </Link>
                </li>
                <li>
                  <Link href="/vacation/tours?query=turkey" onClick={()=>{
                       setQuery('turkey');
                  }} className="text-sm text-gray-300 hover:text-white transition-colors">
                    Turkey
                  </Link>
                </li>
                <li>
                  <Link href="/vacation/tours?query=world" onClick={()=>{
                       setQuery('world');
                  }} className="text-sm text-gray-300 hover:text-white transition-colors">
                    World
                  </Link>
                </li>
              </ul>
            </div>

            {/* Help & Support Section */}
            <div>
              <h3 className="text-lg font-semibold mb-6 text-white">Help & Support</h3>
              <ul className="space-y-4">
                <li className="text-sm text-gray-300">+91 9717559499</li>
                <li className="text-sm text-gray-300"><EMAIL></li>
                <li>
                  <Link href="/pages/faqs" className="text-sm text-gray-300 hover:text-white transition-colors">
                    FAQs
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;