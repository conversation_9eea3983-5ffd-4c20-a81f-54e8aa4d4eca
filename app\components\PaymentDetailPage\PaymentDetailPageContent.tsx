
"use client";
// Page to show for next payment to show payment details and new payment
import { useContext, useEffect, useState } from 'react';
import { addDays, format } from 'date-fns';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import crypto from 'crypto';
import { AppContext } from '@/app/context/useAppContext';
import { useDataStore } from '@/app/store/dataStore';

interface TravellerInfo {
	firstName: string;
	lastName: string;
	dateOfBirth: string;
	passportNumber: string;
	passportIssueDate: string;
	passportExpiryDate: string;
	nationality: string;
	// passportIssueCity: string;
	mealPreference: 'veg' | 'non-veg';
	 gender: 'Male',
	pancard_no: '',
	// mealPreference: 'veg',
	pancard :"",
	passport :""
}

interface EditableSections {
	travelDate: boolean;
	travellers: boolean[];
}

interface TravelDates {
	startDate: string;
	endDate: string;
	duration: string;
}

interface PayUPaymentData {
	key: string;
	txnid: string;
	amount: number;
	productinfo: string;
	firstname: string;
	lastname: string;
	email: string;
	phone: string;
	surl: string;
	furl: string;
	hash?: string;
}
const PaymentDetailRender = ({bookingId}:any) => {
	// const data_from_store = useDataStore((state) => state.data);
	// console.log("data_from_store",data_from_store);
	const router = useRouter();
	const [paymentId,setPaymentId] = useState(0);
	// const [paymentType, setPaymentType] = useState<'full' | 'installments'>('full');
	const [paymentType, setPaymentType] = useState<'full' | 'installments'>('installments');
	const [isProcessing, setIsProcessing] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'payu' | 'paysharp' | null>(null);
	const [bookingData,setBookingData] = useState<any>({});
	const [travellers,setTravellers] = useState([]);
	const [travelDates, setTravelDates] = useState<TravelDates>({
		startDate: '2025-06-04',
		endDate: '2025-06-08',
		duration: '5 days'
	});
	// const {bookingData, setBookingData} = useContext(AppContext);
	
	const [editIndex, setEditIndex] = useState<number|any>(null);
	const [editableSections, setEditableSections] = useState<EditableSections>({
		travelDate: false,
		travellers: Array(travellers.length).fill(false)
	});
	const [packageData, setPackageData] = useState<any>(null);
 
	

	// ============ for getting installment on api call ==========
	const [hasInitiatedPayment, setHasInitiatedPayment] = useState(false);
	const [installmentList, setInstallmentList] = useState<any>([]);
	const [isPaymentButtonShowed, setIsPaymentButtonShowed] = useState(false);
	const [currentPaymentPendingId, setCurrentPaymentPendingId] = useState(null);
	const [price_to_show,set_price_to_show]= useState<any>(0); 

useEffect(()=>{
		const fetchPackageData = async () => {
			try {
				const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/booking/${bookingId}`);
				const data = await response.json();
				debugger;
				// setPackageData(data?.packageInfo);
				setPackageData(data?.packageInfo);
				const bookingData:any = {
						travellers:[
							...data?.passengersInfo
						],
						date:data?.bookingDate,
						rule:null,
						packageCode: data?.packageInfo?.packageCode,
						packageData: data?.packageInfo,
						bookingInstallmentsInfo:data?.bookingInstallmentsInfo
					};
					const {travellers} = bookingData;
					setTravellers(travellers);
					setBookingData(bookingData);
			} catch (error) {
				console.error('Error fetching package data:', error);
			}
		};
	
	fetchPackageData();
},[])
useEffect(() => {
	const is_custom = typeof window !== "undefined" && new URLSearchParams(window.location.search).get('custom') === 'true';
		if(bookingData.isPromoApplied){
			if(!bookingData?.rule){
				if(!is_custom){
					let total = (packageData?.priceSummary?.netSellingPrice)*(travellers.length);
					set_price_to_show(total-30000);
				} else{
					const total = (packageData?.priceSummary?.netSellingPrice / packageData?.noOfAdults)*travellers.length;
					set_price_to_show(total-30000);
				}
			} else{
				if(!is_custom){
					let total = (packageData?.priceSummary?.netSellingPrice)*(travellers.length);
					total = total -(total*(bookingData?.rule?.discountPercentage/100))
					set_price_to_show(total-30000);
				} else{
					let total = (packageData?.priceSummary?.netSellingPrice / packageData?.noOfAdults)*travellers.length;
					total = total -(total*(bookingData?.rule?.discountPercentage/100));
					set_price_to_show(total-30000);
				}
			}
		} else{
			if(!bookingData?.rule){
				if(!is_custom){
					let total = (packageData?.priceSummary?.netSellingPrice)*(travellers.length);
					set_price_to_show(total);
				} else{
					const total = (packageData?.priceSummary?.netSellingPrice / packageData?.noOfAdults)*travellers.length;
					set_price_to_show(total);
				}
			} else{
				if(!is_custom){
					let total = (packageData?.priceSummary?.netSellingPrice)*(travellers.length);
					total = total -(total*(bookingData?.rule?.discountPercentage/100))
					set_price_to_show(total);
				} else{
					let total = (packageData?.priceSummary?.netSellingPrice / packageData?.noOfAdults)*travellers.length;
					total = total -(total*(bookingData?.rule?.discountPercentage/100));
					set_price_to_show(total);
				}
			}
		}
	if (packageData && !hasInitiatedPayment) {
		handlePayment();
		setHasInitiatedPayment(true);
	}

}, [packageData, hasInitiatedPayment]);

	const handlePayment:any = async (fromPaymenClick=false,installment_id=0) => {
		// const amount = paymentType === 'installments' 
		//   ? ((packageData?.priceSummary?.netSellingPrice || 82500) * travellers.length) / 3 
		//   : (packageData?.priceSummary?.netSellingPrice || 82500) * travellers.length;
		let amount = paymentType === 'installments' 
			? ((packageData?.priceSummary?.netSellingPrice || 82500) * travellers.length) / 3 
			: (packageData?.priceSummary?.netSellingPrice || 82500) * travellers.length;
		if(fromPaymenClick && installmentList.length>0){
				amount = (installmentList.find((element: any) => element.id == currentPaymentPendingId) as any).amount;
				// amount = bookingData?.rule ? Math.round(amount-(amount*(bookingData?.rule?.discountPercentage/100))) : amount;
				amount = amount;
		}
		let total_amount_before_discount =(packageData?.priceSummary?.netSellingPrice || 82500) * travellers.length;
		const displayAmount = amount.toLocaleString();
		let after_discount = total_amount_before_discount;
		const originalAmount = (after_discount).toLocaleString();
		
		// Encode the title to handle special characters in URL
		// const encodedTitle = encodeURIComponent("Best of Italy in one week (5 Days in Italy - Attractions of Venice & Rome)");
		const encodedTitle = encodeURIComponent(packageData?.packageTitle);
		
		// Construct URL with proper query parameter formatting
		try {
			let data_ins_test = bookingData.bookingInstallmentsInfo;
			setInstallmentList(data_ins_test);
			setCurrentPaymentPendingId((prev: any)=>{
				let index = data_ins_test.findIndex((element: any) => element.paymentStatus == "PENDING");
				return data_ins_test[index].id
			});
			// First submit the booking
			// if(!fromPaymenClick){
			// 	await submitBooking();
			// }

			// Then proceed with payment
			const queryParams = new URLSearchParams({
				bookingId: bookingId,
        installmentId: `${installment_id}`
		});
		if(fromPaymenClick){
			// queryParams.append('installmentId', currentPaymentPendingId);
			router.push(`/checkout/payment?${queryParams.toString()}`);
		}
	} catch (error) {
		console.error('Payment processing error:', error);
		setError('Failed to process payment. Please try again.');
		setIsProcessing(false);
		return;
	} finally {
		setIsProcessing(false);
	}
	};

	useEffect(()=>{
		
		setTravelDates(()=>{
			//  
			if(bookingData?.packageData?.itineraryType?.toLowerCase() =="final" || bookingData?.packageData?.itineraryType?.toLowerCase() =="quotation" ){
			return {
				startDate:new Date(bookingData?.packageData?.packageStartDate),
				// endDate: '2025-06-08',
				// endDate: format(addDays(bookingData?.date, 5), 'yyyy-MM-dd'),
				endDate: format(addDays(new Date(bookingData?.packageData?.packageStartDate), bookingData?.packageData?.noOfDays), 'yyyy-MM-dd'),
				duration: bookingData.packageData.noOfDays
			}
			}
			if(!bookingData?.date || bookingData?.date==""){
			return {
		startDate: '2025-06-04',
		endDate: '2025-06-08',
		duration: '5 days'
	};
		}
			return {
				startDate: bookingData?.date,
				// endDate: '2025-06-08',
				// endDate: format(addDays(bookingData?.date, 5), 'yyyy-MM-dd'),
				endDate: format(addDays(bookingData?.date, bookingData?.packageData?.noOfDays), 'yyyy-MM-dd'),
				duration: bookingData.packageData.noOfDays
			}
		})
	},[bookingData?.date])

	return (
		<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-[0]">
			<div className="flex flex-col lg:flex-row gap-8">
				{/* Left Column */}
				<div className="flex-1 space-y-4">
					{/* Title and Features */}
					<div className="mb-8">
						<a href={`/tours/package-details?query=${packageData?.packageCode}`} target='_blank'>
						<h1 className="text-[30px] font-bold text-[#1E1E1E] mb-3">
							{packageData?.packageTitle || 'Best of Italy in one week (5 Days in Italy - Attractions of Venice & Rome)'}
						</h1>
						</a>
						<div className="flex flex-wrap mb-4 gap-[40px]">
							{packageData?.hotelIncluded &&
							<div className="flex items-center gap-2">
								<img src="/assets/hotels_included.svg" alt="Hotels" className="h-[19.10262680053711px] w-[21px]" />
								<span className="font-[700] text-[14px] text-[#000000] font-[Manrope] ">Hotels included</span>
							</div>}

							{packageData?.transferIncluded &&
							<div className="flex items-center gap-2">
								<img src="/assets/transfer_included.svg" alt="Transfers" className="h-[28px] w-[28px]" />
								<span className="font-[700] text-[14px] text-[#000000] font-[Manrope] ">Transfers included</span>
							</div>}

							{packageData?.activityIncluded && 
							<div className="flex items-center gap-2">
								<img src="/assets/activites_included.svg" alt="Activities" className="h-[28px] w-[28px]" />
								<span className="font-[700] text-[14px] text-[#000000] font-[Manrope] ">Activities included</span>
							</div>}
							{packageData?.flightIncluded && 
							<div className="flex items-center gap-2">
								{/* <img src="/assets/activites_included.svg" alt="Activities" className="h-[28px] w-[28px]" /> */}
								<img src="/assets/transfer_included.svg" alt="Transfers" className="h-[28px] w-[28px]" />
								<span className="font-[700] text-[14px] text-[#000000] font-[Manrope] ">Flights included</span>
							</div>}
						</div>
						<div className="flex flex-wrap gap-2">
							<span className="px-3 py-1 bg-gray-100 rounded-full text-[12px] text-[#344054] font-medium">Adventure</span>
							<span className="px-3 py-1 bg-gray-100 rounded-full text-[12px] text-[#344054] font-medium">Culture</span>
							<span className="px-3 py-1 bg-gray-100 rounded-full text-[12px] text-[#344054] font-medium">Honeymoon</span>
							<span className="px-3 py-1 bg-gray-100 rounded-full text-[12px] text-[#344054] font-medium">Western Europe</span>
						</div>
					</div>

					{/* Travelling Dates Section */}
					<div className="mb-8" style={{marginTop:"48px"}}>
						<div className="bg-blue-50 rounded-xl p-6 relative">
							<div className="absolute -top-3 left-4">
								<span className="text-base font-bold text-[#000000] bg-blue-100 px-3 py-1 rounded-md">
									Travelling Dates
									</span>
							</div>
							{/* <div className="absolute -top-3 right-4">
								{editIndex == -1 ?
										<button
										onClick={() => toggleEdit('travellers', -2)}
										className="text-blue-600 hover:text-blue-700 p-1.5 rounded-full"
											>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
													<path strokeLinecap="round" strokeLinejoin="round" d="M3 13.5V12a9 9 0 019-9m0 0v3.75M12 3L9.75 5.25M21 10.5v1.5a9 9 0 01-9 9m0 0v-3.75M12 21l2.25-2.25" />
												</svg>
		
											</button> :
										<button
									onClick={() => toggleEdit('travelDate')}
									className="text-blue-600 hover:text-blue-700 p-1.5 rounded-full"
										>
											<img src="/assets/edit_icon_new.svg"/>
										</button>
										
									}
							</div> */}
							<div className="mt-3 grid grid-cols-3 gap-8">
								{
									editIndex == -1 ?
									<div className="flex gap-2 flex-col items-start">
										<div className="text-sm text-gray-700">
										Start Date
									</div>
										<input
											type="date"
											className="border rounded-lg px-2.5 py-1 text-xs text-gray-700 bg-white w-48"
											placeholder="When are you planning to travel"
											value ={bookingData.date}
											// onChange ={(e)=>setBookingData((prev:any)=>{
											// 	return {
											// 		...prev,
											// 		date:e.target.value
											// 	}
											// })}
										/>
										<span className="text-sm text-gray-500">Prices may differ based on your travel dates.</span>
									</div> :
								<div>
									<div className="text-sm text-gray-500">Start Date</div>
									<div className="text-sm font-medium text-gray-900">{travelDates.startDate && travelDates.startDate!="" &&format(new Date(travelDates.startDate), 'dd MMM yyyy')}</div>
								</div>
								}
								
								<div>
									<div className="text-sm text-gray-500">End Date</div>
									<div className="text-sm font-medium text-gray-900">{travelDates.endDate && travelDates.endDate !="" && format(new Date(travelDates.endDate), 'dd MMM yyyy')}</div>
									</div>
								<div>
									<div className="text-sm text-gray-500">Duration</div>
									<div className="text-sm font-medium text-gray-900">{travelDates.duration}</div>
								</div>
							</div>
						</div>
					</div>

					{/* Traveller Information */}
						{travellers.map((traveller:any, index:number) => {
							return (
						<div key={index} className="mb-8 bg-blue-50 rounded-xl p-6 relative" style={{marginTop:"48px"}}>
								<div className="absolute -top-3 left-4">
								<span className="text-base font-bold text-[#000000] rounded-md  bg-blue-100 px-3 py-1">
										Traveller {index + 1}
									</span>
								</div>
								{
									editIndex == index ? 
									<div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-3">
									{/* First Name */}
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											*First Name
										</label>
										<input
											type="text"
											value={traveller?.name?.split(' ')[0]}
											className="w-full border rounded-lg px-3 py-2"
											required
										/>
									</div>

									{/* Last Name */}
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											*Last Name
										</label>
										<input
											type="text"
											value={traveller?.name?.split(' ')[1]?traveller.name?.split(' ')[1] : ''}
											className="w-full border rounded-lg px-3 py-2"
											required
										/>
									</div>

									{/* Date of Birth */}
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											*Date of Birth
										</label>
										<input
											type="date"
											value={traveller.dateOfBirth}
											className="w-full border rounded-lg px-3 py-2"
											required
										/>
									</div>

									{/* Passport Number */}
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											*Passport Number
										</label>
										<input
											type="text"
											value={traveller.passportNo}
											className="w-full border rounded-lg px-3 py-2"
											required
										/>
									</div>

									{/* Passport Issue Date */}
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											*Passport Issue Date
										</label>
										<input
											type="date"
											value={traveller.issueDate}
											className="w-full border rounded-lg px-3 py-2"
											required
										/>
									</div>

									{/* Passport Expiry Date */}
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											*Passport Expiry Date
										</label>
										<input
											type="date"
											value={traveller.expiryDate}
											className="w-full border rounded-lg px-3 py-2"
											required
										/>
									</div>

									{/* Nationality */}
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											*Nationality
										</label>
										<select
											value={traveller.issueCountry}
											className="w-full border rounded-lg px-3 py-2"
											required
										>
											<option value="India">India</option>
										</select>
									</div>
								</div> : 
								<div className="mt-3 grid grid-cols-3 gap-8">
									<div>
									{/* traveller?.name?.split(' ')[0]
									traveller?.name?.split(' ')[1]?traveller.name?.split(' ')[1] : ''
									traveller.passportNo
									traveller.issueDate
									traveller.expiryDate
									traveller.issueCountry */}
										<div className="text-sm text-gray-500">First Name</div>
										<div className="text-sm font-medium text-gray-900">{traveller?.name?.split(' ')[0]}</div>
									</div>
									<div>
										<div className="text-sm text-gray-500">Last Name</div>
										<div className="text-sm font-medium text-gray-900">{traveller?.name?.split(' ')[1]}</div>
									</div>
									<div>
										<div className="text-sm text-gray-500">Date of Birth</div>
										<div className="text-sm font-medium text-gray-900">{traveller.dateOfBirth && traveller.dateOfBirth!=="" && format(new Date(traveller.dateOfBirth), 'dd MMM yyyy')}</div>
									</div>
									<div>
										<div className="text-sm text-gray-500">Passport Number</div>
										<div className="text-sm font-medium text-gray-900">{traveller.passportNo}</div>
									</div>
									<div>
										<div className="text-sm text-gray-500">Passport Issue Date</div>
										<div className="text-sm font-medium text-gray-900">{traveller?.issueDate && traveller?.issueDate!="" && format(new Date(traveller.issueDate), 'dd MMM yyyy')}</div>
									</div>
									<div>
										<div className="text-sm text-gray-500">Passport Expiry Date</div>
										<div className="text-sm font-medium text-gray-900">{traveller?.expiryDate && traveller?.expiryDate!=="" && format(new Date(traveller.expiryDate), 'dd MMM yyyy')}</div>
									</div>
									<div>
										<div className="text-sm text-gray-500">Nationality</div>
										<div className="text-sm font-medium text-gray-900">{traveller.issueCountry}</div>
									</div>
									<div>
										<div className="text-sm text-gray-500">Gender</div>
										<div className="text-sm font-medium text-gray-900">{traveller.gender}</div>
									</div>
									{
									traveller.panNo && traveller.panNo!="" && traveller.issueCountry.toLowerCase() ==="india" && <div>
										<div className="text-sm text-gray-500">Pancard Number</div>
										<div className="text-sm font-medium text-gray-900">{traveller.panNo}</div>
									</div>
									}
								</div>
								}
								</div>
						)
						}
						)}
				</div>

				{/* Right Column - Payment Section */}
				<div className="lg:w-96">
					<div className="border-gray-200 border rounded-xl p-6">
						<div className="bg-yellow-50 -mx-6 -mt-6 p-6 rounded-t-xl">
							<div className="mb-1">
								<span className="text-sm text-gray-600">Amount Payable</span>
							</div>
							<div className="flex items-baseline gap-2">
								{/* <span className="text-2xl font-bold text-gray-900">₹{Math.round(price_to_show).toLocaleString()}</span> */}
								<span className="text-2xl font-bold text-gray-900">₹{bookingData?.bookingInstallmentsInfo&& bookingData.bookingInstallmentsInfo.reduce((acc:any,ele:any)=>acc+ele.amount,0)}</span>
								{bookingData.isPromoApplied && (
									<span className="text-sm text-gray-500 line-through">₹{(Math.round(((typeof window !== "undefined" && new URLSearchParams(window.location.search).get('custom') !== 'true')? packageData?.priceSummary?.grossSellingPrice:(packageData?.priceSummary?.grossSellingPrice)/packageData?.noOfAdults) * travellers.length)).toLocaleString()}</span>
								)}
							</div>
							<div className="text-xs text-gray-500">inclusive of all taxes</div>
						</div>

						<div className="mt-6 space-y-4">
							{/* <div className="flex gap-4">
								<label className="flex items-center gap-2">
									<input
										type="radio"
										checked={paymentType === 'full'}
										onChange={() => setPaymentType('full')}
										className="text-blue-600"
									/>
									<span className="text-sm text-gray-700">Pay in full</span>
								</label>
								<label className="flex items-center gap-2">
									<input
										type="radio"
										checked={paymentType === 'installments'}
										onChange={() => setPaymentType('installments')}
										className="text-blue-600"
									/>
									<span className="text-sm text-gray-700">Pay in installments</span>
								</label>
							</div> */}

							{paymentType === 'installments' && (
								<>
									<div>Payment Installments</div>
									{
										installmentList.map((installment: any, index: number) =>{
											const firstInstallmentDate = new Date(installmentList[0].paymentDate);
											let paymentDate = new Date(installment.paymentDate);

											if (index !== 0 && paymentDate < new Date()) {
												// Set to first installment + 1 day
												const updatedDate = new Date(firstInstallmentDate);
												updatedDate.setDate(updatedDate.getDate() + 1);
												paymentDate = updatedDate;
											}
											return (
											<div key={index} className='flex justify-between items-center'>
											<div key={index}>
												{/* <div className="text-sm text-gray-600 mb-1">Installment {index + 1}</div> */}
												<div className="text-lg font-medium text-gray-900">₹{Math.round(installment.amount)}</div>
												<div className="text-sm text-gray-600 mb-1">Pay on: <span className="text-sm text-gray-600 mb-1">{paymentDate.toLocaleDateString('en-GB').replace(/\//g, '-')}</span></div>
											</div>
											{
												installment.paymentStatus.toLowerCase() === 'paid' &&
												<div className=" text-sm bg-green-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors pointer-events-none">
														Paid
												</div>
											}
											{
											installment.paymentStatus.toLowerCase() === 'pending' && (installment.id ==currentPaymentPendingId ||  installmentList[0].paymentStatus.toLowerCase() === 'paid') && (
												<button
													className=" text-sm bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
													// onClick={()=>{}}
													onClick={()=>{
														setPaymentId(installment.id);
														handlePayment(true,installment.id);
													}}
												>
													{isProcessing ? (
															<>
																<svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
																	<circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
																	<path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
																</svg>
																Processing...
															</>
														) : ( 
																			"Pay Now"
														)}
												</button>
											)}
											</div>
											)
										})

									}
								</>
								// <div className="space-y-4">
								//   <div className="flex items-center gap-4">
								//     <label className="text-sm text-gray-600 whitespace-nowrap">Choose Tenure</label>
								//     <select className="border rounded-lg px-3 py-2 text-sm text-gray-700 bg-white flex-1">
								//       <option value="3">3 months</option>
								//       <option value="6">6 months</option>
								//       <option value="9">9 months</option>
								//       <option value="12">12 months</option>
								//     </select>
								//   </div>
								//   <div>
								//     <div className="text-sm text-gray-600 mb-1">Today youll pay</div>
								//     <div className="text-lg font-medium text-gray-900">₹{(((bookingData.isPromoApplied ? (Math.round((packageData?.priceSummary?.netSellingPrice) * travellers.length - 30000)) : ((packageData?.priceSummary?.netSellingPrice) * travellers.length)) / 3)).toLocaleString()}</div>
								//   </div>
								//   <button 
								//     className="text-blue-600 text-sm hover:text-blue-700 hover:underline"
								//   >
								//     View Payment Schedule
								//   </button>
								// </div>
							)}

							{error && (
								<div className="mb-4 p-3 bg-red-50 text-red-700 rounded-lg text-sm">
									{error}
								</div>
							)}

							{/* Comment this later.. */}
							{/* <button
								className={`w-full ${
									isProcessing 
										? 'bg-blue-400 cursor-not-allowed' 
										: 'bg-blue-600 hover:bg-blue-700'
								} text-white py-3 rounded-lg font-medium flex items-center justify-center`}
								onClick={handlePayment}
								disabled={isProcessing}
							>
								{isProcessing ? (
									<>
										<svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
											<circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
											<path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
										</svg>
										Processing...
									</>
								) : (
									`Pay ${paymentType === 'installments' ? 
										`₹${(((bookingData.isPromoApplied ? (Math.round((packageData?.priceSummary?.netSellingPrice) * travellers.length - 30000)) : ((packageData?.priceSummary?.netSellingPrice) * travellers.length)) / 3)).toLocaleString()}` : 
										`₹${(bookingData.isPromoApplied ? (Math.round((packageData?.priceSummary?.netSellingPrice) * travellers.length - 30000)) : ((packageData?.priceSummary?.netSellingPrice) * travellers.length)).toLocaleString()}`}`
								)}
							</button> */}

							<div className="flex items-center justify-center gap-2 text-sm text-gray-600">
								<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
									<path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
								</svg>
								<span>100% Safe and Secure Payment</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default PaymentDetailRender;
