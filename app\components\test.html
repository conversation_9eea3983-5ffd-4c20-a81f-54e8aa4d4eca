<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Sticky Sidebar with Scrollspy</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    html {
      scroll-behavior: smooth;
    }
  </style>
</head>

<body class="bg-gray-50">

  <div class="max-w-7xl mx-auto p-4">

    <div class="flex">

      <!-- Sidebar -->
      <div class="w-64 mr-4">
        <div class="sticky top-4">
          <nav class="bg-white p-4 rounded shadow">
            <ul id="sidebar-links" class="space-y-2 text-blue-600">
              <li><a href="#section1" class="block px-2 py-1 rounded hover:bg-blue-100">Section 1</a></li>
              <li><a href="#section2" class="block px-2 py-1 rounded hover:bg-blue-100">Section 2</a></li>
              <li><a href="#section3" class="block px-2 py-1 rounded hover:bg-blue-100">Section 3</a></li>
            </ul>
          </nav>
        </div>
      </div>

      <!-- Main content -->
      <div class="flex-1 space-y-16">

        <section id="section1" class="bg-white p-8 rounded shadow min-h-[100vh]">
          <h2 class="text-2xl font-bold mb-4">Section 1</h2>
          <p class="text-gray-700">Lots of content in section 1. Scroll down to see effect.</p>
        </section>

        <section id="section2" class="bg-white p-8 rounded shadow min-h-[100vh]">
          <h2 class="text-2xl font-bold mb-4">Section 2</h2>
          <p class="text-gray-700">Lots of content in section 2. Scroll down to see effect.</p>
        </section>

        <section id="section3" class="bg-white p-8 rounded shadow min-h-[100vh]">
          <h2 class="text-2xl font-bold mb-4">Section 3</h2>
          <p class="text-gray-700">Lots of content in section 3. Scroll down to see effect.</p>
        </section>

      </div>
    </div>

    <!-- Below the section -->
    <div class="mt-16 bg-white p-8 rounded shadow">
      <h2 class="text-2xl font-bold mb-4">After Sidebar Area</h2>
      <p class="text-gray-700">This is normal page content after the sidebar area.</p>
    </div>

  </div>

  <script>
    // Scrollspy logic
    const sections = document.querySelectorAll('section');
    const links = document.querySelectorAll('#sidebar-links a');

    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        const id = entry.target.getAttribute('id');
        const link = document.querySelector(`#sidebar-links a[href="#${id}"]`);

        if (entry.isIntersecting) {
          links.forEach(link => link.classList.remove('bg-blue-100', 'font-bold'));
          link.classList.add('bg-blue-100', 'font-bold');
        }
      });
    }, {
      rootMargin: '-50% 0px -50% 0px',
      threshold: 0
    });

    sections.forEach(section => {
      observer.observe(section);
    });
  </script>

</body>

</html>