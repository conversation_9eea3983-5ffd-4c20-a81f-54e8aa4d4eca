"use client";

import { useState } from "react";
import Image from "next/image";
//import tourDetails from "../data/tourDetails";

const TourDetailsPage = () => {

    const tourDetails = {
        id: 1,
        title: "Swiss Alps Adventure",
        location: "Switzerland",
        duration: "7 Days, 6 Nights",
        price: "$2,200",
        rating: 4.8,
        totalReviews: 152,
        stayDetails: "6 nights in 4-star hotels",
        images: [
          "/assets/tours/imagefour.png",
          "/assets/tours/imageone.png",
          "/assets/tours/imagethree.png",
        ],
        highlights: [
          "Experience the breathtaking beauty of the Swiss Alps",
          "Stay in luxurious mountain resorts",
          "Explore Interlaken, Lucerne, and Zermatt",
          "Cable car ride to Matterhorn Glacier",
        ],
        itinerary: [
          {
            day: 1,
            title: "Arrival in Zurich",
            description: "Meet and greet at the airport, transfer to the hotel, and explore the city.",
          },
          {
            day: 2,
            title: "Interlaken Adventure",
            description: "Scenic train ride to Interlaken and visit Jungfraujoch - Top of Europe.",
          },
          {
            day: 3,
            title: "Explore Lucerne",
            description: "Visit Chapel Bridge, Mount Pilatus, and enjoy a lake cruise.",
          },
          // Add more days as needed
        ],
        inclusions: [
          "Accommodation in 4-star hotels",
          "Daily breakfast and dinner",
          "All airport and hotel transfers",
          "Guided sightseeing tours",
          "Travel insurance",
        ],
        exclusions: [
          "International airfare",
          "Personal expenses",
          "Lunch & snacks",
          "Entry fees for optional activities",
        ],
        customerReviews: [
          {
            name: "John Doe",
            location: "New York, USA",
            rating: 5,
            review: "Absolutely breathtaking experience! The tour was well-organized, and the guides were amazing.",
          },
          {
            name: "Emily Smith",
            location: "London, UK",
            rating: 4.5,
            review: "Loved every moment of the Swiss Alps adventure. Highly recommended!",
          },
        ],
      };
      
        
  const [currentImage, setCurrentImage] = useState(0);
  

  const nextSlide = () => {
    setCurrentImage((prev) => (prev + 1) % tourDetails.images.length);
  };

  const prevSlide = () => {
    setCurrentImage((prev) =>
      prev === 0 ? tourDetails.images.length - 1 : prev - 1
    );
  };

  return (
    <div className="container mx-auto px-4 py-8 grid grid-cols-1 md:grid-cols-2 gap-8">
      {/* Image Carousel */}
      <div className="relative w-full max-w-xl mx-auto">
        <Image
          src={tourDetails.images[currentImage]}
          alt="Tour Image"
          width={800}
          height={500}
          className="rounded-lg"
        />
        {/* Arrows */}
        <button className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white p-2 rounded-full" onClick={prevSlide}>❮</button>
        <button className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white p-2 rounded-full" onClick={nextSlide}>❯</button>
      </div>

      {/* Package Details */}
      <div className="space-y-4">
        <h1 className="text-3xl font-bold">{tourDetails.title}</h1>
        <p className="text-gray-500 text-lg">{tourDetails.location}</p>
        <p className="text-xl font-semibold">Stay: {tourDetails.stayDetails}</p>
        <p className="text-gray-600">Tour ID: {tourDetails.id}</p>
        <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
          Book Now
        </button>
      </div>

      {/* Highlights */}
      <div className="col-span-2">
        <h2 className="text-xl font-semibold mt-6">Tour Highlights</h2>
        <ul className="list-disc ml-6 text-gray-700">
          {tourDetails.highlights.map((highlight, index) => (
            <li key={index}>{highlight}</li>
          ))}
        </ul>
      </div>

      {/* Itinerary */}
      <div className="col-span-2 bg-gray-100 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mt-6">Itinerary</h2>
        {tourDetails.itinerary.map((day) => (
          <div key={day.day} className="mb-4">
            <h3 className="font-bold">Day {day.day}: {day.title}</h3>
            <p>{day.description}</p>
          </div>
        ))}
      </div>

      {/* Inclusions & Exclusions */}
      <div className="col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h2 className="text-xl font-semibold">Inclusions</h2>
          <ul className="list-disc ml-6 text-gray-700">
            {tourDetails.inclusions.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        </div>
        <div>
          <h2 className="text-xl font-semibold">Exclusions</h2>
          <ul className="list-disc ml-6 text-gray-700">
            {tourDetails.exclusions.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        </div>
      </div>

      {/* Customer Reviews */}
      <div className="col-span-2">
        <h2 className="text-xl font-semibold mt-6">Customer Reviews</h2>
        <div className="space-y-4">
          {tourDetails.customerReviews.map((review, index) => (
            <div key={index} className="border p-4 rounded-lg">
              <h3 className="font-bold">{review.name} - {review.location}</h3>
              <p className="text-yellow-500">{"★".repeat(review.rating)}</p>
              <p>{review.review}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TourDetailsPage;
