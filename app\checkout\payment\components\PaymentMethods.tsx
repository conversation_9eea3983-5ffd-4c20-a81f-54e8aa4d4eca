import { useState } from 'react';
import { CardPaymentForm } from './CardPaymentForm';
import { UPIPaymentForm } from './UPIPaymentForm';
import { NetBankingForm } from './NetBankingForm';
import { PaymentSummary } from './PaymentSummary';
import { CardDetails } from '../types/payment.types';

interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
  disabled?: boolean;
}

const PAYMENT_METHODS: PaymentMethod[] = [
  { id: 'card', name: 'Credit / Debit Card', icon: '💳', disabled: false },
  { id: 'upi', name: 'UPI', icon: '📱', disabled: false },
  { id: 'netbanking', name: 'Net Banking', icon: '🏦', disabled: false },
  // { id: 'wallet', name: 'Wallets', icon: '💰', disabled: true },
];

interface PaymentMethodsProps {
  amount: string;
  packageId: string;
  bookingId: string;
  installmentId: string;
  title: string;
  duration: string;
  onPaymentSuccess?: () => void;
  onPaymentError?: (error: Error) => void;
}

export const PaymentMethods = ({
  amount,
  packageId,
  bookingId,
  installmentId,
  title,
  duration,
  onPaymentSuccess,
  onPaymentError,
}: PaymentMethodsProps) => {
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleMethodChange = (method: string) => {
    if (isSubmitting) return;
    setSelectedMethod(selectedMethod === method ? null : method);
    setError('');
  };

  const handleCardPayment = async (cardDetails: CardDetails) => {
    try {
      setIsSubmitting(true);
      setError('');
      // Implement card payment logic here
      console.log('Processing card payment:', cardDetails);
      // Call your payment API
      // await processCardPayment({ cardDetails, amount, packageId, bookingId, installmentId });
      onPaymentSuccess?.();
    } catch (err) {
      const error = err as Error;
      setError(error.message || 'Failed to process card payment');
      onPaymentError?.(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUPIPayment = async (upiId: string) => {
    try {
      setIsSubmitting(true);
      setError('');
      // Implement UPI payment logic here
      console.log('Processing UPI payment:', upiId);
      // await processUPIPayment({ upiId, amount, packageId, bookingId, installmentId });
      onPaymentSuccess?.();
    } catch (err) {
      const error = err as Error;
      setError(error.message || 'Failed to process UPI payment');
      onPaymentError?.(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNetBankingPayment = async (bankCode: string) => {
    try {
      setIsSubmitting(true);
      setError('');
      // Implement Net Banking payment logic here
      console.log('Processing Net Banking payment:', bankCode);
      // await processNetBankingPayment({ bankCode, amount, packageId, bookingId, installmentId });
      onPaymentSuccess?.();
    } catch (err) {
      const error = err as Error;
      setError(error.message || 'Failed to process Net Banking payment');
      onPaymentError?.(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getPlatformCharges = () => {
    switch (selectedMethod) {
      case 'card':
        return 3; // 3% for cards
      case 'netbanking':
        return 2; // 2% for net banking
      case 'upi':
      default:
        return 0; // 0% for UPI
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <div className="lg:col-span-2 space-y-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">
            Select Payment Method
          </h2>
          
          <div className="space-y-4">
            {PAYMENT_METHODS.map((method) => (
              <div key={method.id} className="border rounded-lg overflow-hidden">
                <button
                  type="button"
                  onClick={() => handleMethodChange(method.id)}
                  disabled={isSubmitting || method.disabled}
                  className={`w-full p-4 text-left flex items-center justify-between ${
                    selectedMethod === method.id 
                      ? 'bg-blue-50 border-l-4 border-blue-500' 
                      : 'hover:bg-gray-50'
                  } ${method.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <div className="flex items-center">
                    <span className="text-xl mr-3">{method.icon}</span>
                    <span className="font-medium">{method.name}</span>
                  </div>
                  <span className="text-gray-400">
                    {selectedMethod === method.id ? '▼' : '▶'}
                  </span>
                </button>
                
                {selectedMethod === method.id && (
                  <div className="p-4 border-t bg-gray-50">
                    {method.id === 'card' && (
                      <CardPaymentForm 
                        onSubmit={handleCardPayment} 
                        isSubmitting={isSubmitting} 
                      />
                    )}
                    {method.id === 'upi' && (
                      <UPIPaymentForm 
                        onSubmit={handleUPIPayment} 
                        isSubmitting={isSubmitting} 
                      />
                    )}
                    {method.id === 'netbanking' && (
                      <NetBankingForm 
                        onSubmit={handleNetBankingPayment} 
                        isSubmitting={isSubmitting} 
                      />
                    )}
                    {method.id === 'wallet' && (
                      <div className="text-center py-4 text-gray-500">
                        Coming soon
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
          
          {error && (
            <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
              {error}
            </div>
          )}
        </div>
        
        <div className="text-xs text-gray-500">
          <p>Your payment is secured with 256-bit SSL encryption.</p>
          <div className="flex items-center mt-2 space-x-4">
            <span>We accept:</span>
            <div className="flex space-x-2">
              <span>Visa</span>
              <span>•</span>
              <span>Mastercard</span>
              <span>•</span>
              <span>Rupay</span>
              <span>•</span>
              <span>UPI</span>
              <span>•</span>
              <span>Net Banking</span>
            </div>
          </div>
        </div>
      </div>
      
      <div>
        <PaymentSummary
          title={title}
          duration={duration}
          amount={amount}
          paymentMethod={selectedMethod || ''}
          platformCharges={getPlatformCharges()}
        />
      </div>
    </div>
  );
};
