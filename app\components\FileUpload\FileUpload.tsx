import React, { useState } from 'react'
import useFileUpload from './hooks/useFileUpload';

const FileUploadComponent = ({uploadedFileName, setUploadedFileName,fileUpload, setFileUpload,picturetorender,file_extensions_allowed=[".svg", ".png", ".jpg", ".jpeg", ".gif"],showPictureBeside=false,showNameBelow=false,className,id,upload_msg=null,upload_document,loading,uploaded}:any) => {
	 const {
	// fileUpload,
	isDragging,
	// uploadedFileName,
	handleDragOver,
	handleDragLeave,
	handleDrop,
	handleFileUpload,
	handleFileInputChange
	 } = useFileUpload(uploadedFileName, setUploadedFileName,fileUpload, setFileUpload,file_extensions_allowed);
	return (
		<div className='w-full'>
			<div className={`col-span-2 flex items-start gap-4 ${className}`}>
			{showPictureBeside && <img
				src={fileUpload ? URL.createObjectURL(fileUpload) : picturetorender || "/assets/profile_pic_placeholder.jpg"}
				alt="Profile"
				className="w-16 h-16 rounded-full object-cover"
				onError={(e) => {
					e.currentTarget.onerror = null;
					e.currentTarget.src = "/assets/profile_pic_placeholder.jpg";
				}}
			/>}
			<div className={`w-full border ${fileUpload?"border-[#82d690]":"border-[#b7bac0]"} rounded-xl aspect-[748/126] flex flex-col justify-center items-center py-2`}
			onDragOver={handleDragOver}
			onDragLeave={handleDragLeave}
			onDrop={handleDrop}
			>
					<img
						src={"/assets/Featured_Icon.svg"}
						alt="upload_icon"
						// className="w-10 h-10 mb-2 rounded-full object-cover"
						className="w-16 h-16 mb-2 rounded-full object-cover"
					/>
				<div>
					<p className='text-sm font-normal text-[#475467] text-center'>
						<button 
						className='text-sm font-semibold text-[#175CD3] cursor-pointer' 
						onClick={() => document.getElementById(`${id}`)?.click()}>
							Click to upload
						</button> 
						{" "}or drag and drop 
						{
							""
						}
					</p> 
					<p className='text-xs font-normal text-[#475467] text-center'>{file_extensions_allowed.join(",")} file</p> 
				</div>
				
				<input
					type="file"
					// accept=".svg,.png,.jpg,.jpeg,.gif"
					accept={file_extensions_allowed.join(",")}
					onChange={handleFileInputChange}
					className="hidden"
					id={id}
				/>
			</div>
		</div>
		{showNameBelow && uploadedFileName && uploadedFileName !="" &&
			<>
			<div className='text-sm font-semibold text-green-500 py-2 text-center'>
				<span className='w-[20%] text-ellipsis'>{uploadedFileName.split(".")[0].length > 5 ? `${uploadedFileName.split('.')[0].slice(0, 5)}.. .${uploadedFileName.split('.').pop()}` : uploadedFileName}</span> selected.
			</div>
			{
				uploaded &&
				<div className='text-sm font-semibold text-green-500 text-center'>
					<span className='w-[20%] text-ellipsis'>Uploaded..</span>
				</div>
			}
			</>
			}
		{fileUpload&& <div>
			{/* <button onClick={upload_document} className='hover:opacity-85 active:bg-green-500 text-center mx-auto block text-sm mt-2 bg-green-400 px-4 py-2 rounded-md font-medium' >Upload </button> */}
			<button disabled={loading} onClick={upload_document} className={`${loading && "pointer-events-none"} hover:opacity-85 active:bg-green-500 text-center mx-auto block text-sm mt-2 bg-green-400 px-4 py-2 rounded-md font-medium`} >
			{loading ? (
              <span className="flex items-center gap-2 justify-center">
               <svg
								className="animate-spin h-4 w-4 text-gray-700"
								xmlns="http://www.w3.org/2000/svg"
								viewBox="0 0 50 50"
							>
								<circle
									cx="25"
									cy="25"
									r="20"
									fill="none"
									stroke="currentColor"
									strokeWidth="5"
									strokeLinecap="round"
									strokeDasharray="90"
									strokeDashoffset="60"
								/>
							</svg>


                Uploading...
              </span>
            ) : (
              "Upload"
            )}	
			</button>
		</div>}
		</div>
	)
}

export default FileUploadComponent