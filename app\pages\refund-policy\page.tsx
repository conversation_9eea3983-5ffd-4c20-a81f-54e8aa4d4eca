"use client";

import React, { useEffect, useState } from 'react';
import NavBar from '@/app/components/NavBar';
import Footer from '@/app/components/Footer';
import Breadcrumb from '@/app/components/BreadCrumbs';
import axios from 'axios';

const RefundPolicy = () => {
  const [policies, setPolicies] = useState<{
      paymentPolicy: string;
      refundPolicy: string;
      termsAndConditions: string;
    }>({
      paymentPolicy: '',
      refundPolicy: '',
      termsAndConditions: ''
    });
  useEffect(()=>{
    const fetchPolicies = async () => {
      try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/web-images`);

        if (response.data) {
          const policyData = response.data;

          const paymentPolicy = policyData.find((policy: any) =>
            // policy.imageText === 'Payment_Policy'
            policy.imageText === 'Payment Policy'
          )?.content || 'Payment policy information not available.';


          const refundPolicy = policyData.find((policy: any) =>
            // policy.imageText === 'Refund_Policy'
            policy.imageText === 'Cancellation & Rescheduling Terms'
          )?.content || 'Refund policy information not available.';

          const termsAndConditions = policyData.find((policy: any) =>
            // policy.imageText === 'Terms_And_Condition'
            policy.imageText === 'Terms and Conditions'
          )?.content || 'Terms and conditions not available.';

          setPolicies({
            paymentPolicy,
            refundPolicy,
            termsAndConditions
          });
        }
      } catch (err) {
        console.error('Error fetching policies:', err);
        // setError('Failed to load policies. Please try again later.');
      } finally {
        // setLoading(false);
      }
    };

    fetchPolicies();
  },[])
  return (
    <main className="min-h-screen">
      <NavBar />
      <Breadcrumb />
      <div className='itinerary-content max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12' dangerouslySetInnerHTML={{ __html: policies.refundPolicy }} />
      {/* <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">Refund Policy</h1>
        
        <div className="space-y-8">
          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">General Refund Policy</h2>
            <p className="text-gray-600 mb-4">
              At WiseYatra, we understand that plans can change. Our refund policy is designed to be fair and transparent while protecting both our customers and our business.
            </p>
          </section>
          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Cancellation and Refund Terms</h2>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-medium text-gray-900 mb-2">1. Cancellation Period</h3>
                <p className="text-gray-600">
                  - Cancellations made 30 days or more before departure: 90% refund
                  <br />
                  - Cancellations made 15-29 days before departure: 75% refund
                  <br />
                  - Cancellations made 7-14 days before departure: 50% refund
                  <br />
                  - Cancellations made less than 7 days before departure: No refund
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-medium text-gray-900 mb-2">2. Processing Time</h3>
                <p className="text-gray-600">
                  Refunds will be processed within 7-10 business days from the date of cancellation approval. The refund amount will be credited to the original payment method used for the booking.
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-medium text-gray-900 mb-2">3. Non-Refundable Items</h3>
                <p className="text-gray-600">
                  The following items are non-refundable:
                  <br />
                  - Airline tickets and related fees
                  <br />
                  - Visa application fees
                  <br />
                  - Insurance premiums
                  <br />
                  - Processing fees
                </p>
              </div>
            </div>
          </section>
          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Special Circumstances</h2>
            <div className="bg-white p-4 rounded-lg shadow">
              <p className="text-gray-600">
                In case of force majeure events (natural disasters, political unrest, etc.), we will work with our partners to provide the best possible solution, which may include:
                <br />
                - Rescheduling the tour
                <br />
                - Providing travel credits
                <br />
                - Partial or full refunds (subject to partner policies)
              </p>
            </div>
          </section>
          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Need Help?</h2>
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-gray-600 mb-4">
                If you have any questions about our refund policy or need assistance with a cancellation, please contact our customer support team.
              </p>
              <button className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition">
                Contact Support
              </button>
            </div>
          </section>
        </div>
      </div> */}

      <Footer />
    </main>
  );
};

export default RefundPolicy; 