export interface PackageDetails {
  itineraryType?:string;
  packageCode: string;
  packageTitle: string;
  shortDescription: string;
  description: string;
  packageMainImage: string;
  packageType: string;
  packageTheme: string;
  otherTags: string;
  destinationId: number;
  continent: string;
  region: string;
  country: string;
  state: string;
  city: string;
  bestDeal: boolean;
  featuredTour: boolean;
  flightIncluded: boolean;
  hotelIncluded: boolean;
  activityIncluded: boolean;
  transferIncluded: boolean;
  builtFrom: string;
  customerCanCustomize: boolean;
  status: string;
  publishForCustomizedTours: string;
  noOfDays: number;
  noOfNights: number;
  parentPackageCode: string;
  priceOnWebsite?:string;
  packageStartDate?:string;
  noOfAdults:number;
  price: number;
  days: Array<{
    dayId: number;
    day_number: number;
    cityId: number | null;
    itineraryDate: string | null;
    cities: Array<{
      id: number;
      dayId: number;
      cityId: number;
      title: string;
      description: string;
      itineraries: Array<{
        itineraryId: number;
        sortNo: number;
        code: string;
        subCode: string;
        subCodeDescriptor: string;
        excursionId: string;
        excursionType: string;
        price: string;
        noOfNightsBooked: number;
        desc: string;
        customerNote: string | null;
        agentNote: string | null;
        adminNote: string | null;
        itineraryCityId: number;
        selected_excursion: {
          id: string;
          title?: string;
          cityId: number;
          status: string;
          cityName: string;
          airportId?: number;
          airportName?: string;
          destinationId: number;
          destinationName: string;
          titleDescriptor?: string;
          starRatings?: number;
          transportDetails?: Array<{
            status: string;
            carType: string;
            partner: string;
            currency: string;
            finalPrice: string;
            pickupTime: string;
            actualPrice: number;
            description: string;
            meetingPoint: string;
            transferFrom: string;
            transferMode: string;
            companyMarkup: number;
            partnerNumber: string;
            companyMarkupType: string;
            pickupTimeDisplayIn: string[];
            meetingPointDisplayIn: string[];
            meetingPointGoogleLink: string;
            partnerNumberDisplayIn: string[];
          }>;
          images?: Array<{
            filePath: string;
            DisplayIn?: string[];
          }>;
          currency?: string;
          adminNote?: string;
          agentNote?: string;
          hotelCode?: string;
          hotelDesc?: string;
          hotelName?: string;
          inclusions?: string;
          starRating?: number | string;
          roomDetails?: Array<{
            id: string;
            images?: Array<{ filePath: string }>;
            status: string;
            roomType: string;
            finalPrice: string;
            actualPrice: number;
            companyMarkup: number;
            companyMarkupType: string;
          }>;
          customerNote?: string;
          hotelAddress?: string;
          subAdminNote?: string;
          contactNumber?: string;
          locationRating?: number | string;
          externalRatings?: Array<{
            link?: string;
            name: string;
            rank: number;
            rankError: boolean;
          }>;
          hotelLocationLatitude?: number | string;
          publicTransportNearBy?: string;
          contactNumberDisplayIn?: string[];
          hotelLocationLongitude?: string;
          publicTransportDisplayIn?: string[];
          publicTransportLocationLatitude?: string;
          publicTransportLocationDisplayIn?: string[];
          publicTransportLocationLongitude?: string;
          others?: string;
          startTime?: string;
          description?: string;
          youTubeLink?: string;
          meetingPoint?: string;
          howToGetThere?: string;
          partnerNumber?: string;
          packageDetails?: Array<{
            id: string;
            status: string;
            partner: string;
            currency: string;
            tourType: string;
            adminNote: string;
            agentNote: string;
            finalPrice: string;
            inclusions: string;
            actualPrice: number;
            description: string;
            packageName: string;
            customerNote: string;
            subAdminNote: string;
            tourDuration: string;
            companyMarkup: number;
            companyMarkupType: string;
          }>;
          othersDisplayIn?: string[];
          sightseeingCode?: string;
          sightseeingName?: string;
          startTimeShowIn?: string[];
          shortDescription?: string;
          detailsOfActivity?: string;
          redemptionOfVoucher?: string;
          youTubeLinkDisplayIn?: string[];
          meetingPointDisplayIn?: string[];
          howToGetThereDisplayIn?: string[];
          partnerNumberDisplayIn?: string[];
          shortDescriptionDisplayIn?: string[];
          detailsOfActivityDisplayIn?: string[];
          meetingPointLocationLatitude?: number;
          redemptionOfVoucherDisplayIn?: string[];
          meetingPointLocationLongitude?: number;
          destinationCityId?: number;
          destinationCityName?: string;
        };
      }>;
    }>;
  }>;
  inclusions: Array<{
    id: number;
    name: string;
    desc: string;
    included: boolean;
    excluded: boolean;
  }>;
  priceSummary: {
    summaryId: number;
    actualPrice: number;
    miscCost: number;
    agentMarkup: number;
    agentDiscount: number;
    grossSellingPrice: number;
    taxGst: number;
    netSellingPrice: number;
    netCommission: number;
    netMarkup: number;
    netCost: number;
    grossMargin: number;
    taxGstMargin: number;
    taxGstPaid: number;
    netMargin: number;
  };
  applicableDiscountRules?:any;
}

export interface TripHighlight {
  title: string;
  description: string;
  duration: string;
  image: string;
}

export interface ItineraryDay {
  day: number;
  title: string;
  description: string;
  meals: {
    breakfast: boolean;
    lunch: boolean;
    dinner: boolean;
  };
  activities: string[];
}

export interface HotelDetail {
  name: string;
  rating: number;
  location: string;
  nights: string;
  roomType: string;
  amenities: string[];
  contact: {
    phone: string;
    email: string;
  };
  description: string;
}

export interface TourTag {
  icon: string;
  text: string;
}
