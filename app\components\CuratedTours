// components/CuratedTours.tsx
import React from "react";
//import { ArrowRight } from "lucide-react";

const tours = [
  {
    id: 1,
    title: "Luxury Escape in the Maldives",
    image: "https://via.placeholder.com/150",
    description: "Experience unparalleled luxury in an overwater villa surrounded by turquoise waters and pristine beaches.",
  },
  {
    id: 2,
    title: "Exploring the Wonders of Egypt",
    image: "https://via.placeholder.com/150",
    description: "Discover ancient pyramids, cruise along the Nile, and dive into the history of one of the world's oldest civilizations.",
  },
  {
    id: 3,
    title: "Romantic Getaway in Santorini",
    image: "https://via.placeholder.com/150",
    description: "Enjoy breathtaking sunsets, whitewashed architecture, and the best Mediterranean cuisine in Santorini.",
  },
  {
    id: 4,
    title: "Adventure in Patagonia",
    image: "https://via.placeholder.com/150",
    description: "Trek through glaciers, explore the Andes mountains, and witness the stunning beauty of Patagonia’s landscapes.",
  },
  {
    id: 5,
    title: "Tokyo: A Fusion of Tradition & Modernity",
    image: "https://via.placeholder.com/150",
    description: "Immerse yourself in the neon-lit streets, historic temples, and world-famous cuisine of Tokyo.",
  },
  {
    id: 6,
    title: "Serenity in Bali",
    image: "https://via.placeholder.com/150",
    description: "Unwind in lush rainforests, explore ancient temples, and experience the vibrant Balinese culture.",
  },
  {
    id: 7,
    title: "The Majestic Swiss Alps",
    image: "https://via.placeholder.com/150",
    description: "Ski, hike, and take in the breathtaking views of Switzerland’s world-famous alpine scenery.",
  },
  {
    id: 8,
    title: "Safari Adventure in Kenya",
    image: "https://via.placeholder.com/150",
    description: "Spot the Big Five, witness the Great Migration, and explore the rich wildlife of the African savannah.",
  },
];

const CuratedTours: React.FC = () => {
  return (
    <section className="container mx-auto px-4 py-10">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
        <div>
          <p className="text-gray-500 text-lg">Curated Tours</p>
          <h2 className="text-3xl font-bold text-gray-800">Crafted Just for You</h2>
        </div>
        <button className="bg-blue-100 text-blue-700 font-bold px-5 py-2 rounded-full flex items-center hover:bg-blue-200 transition">
          See All <ArrowRight className="ml-2 h-5 w-5" />
        </button>
      </div>

      {/* Grid Layout */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {tours.map((tour) => (
          <div key={tour.id} className="bg-white shadow-md rounded-lg overflow-hidden p-4">
            <img src={tour.image} alt={tour.title} className="w-full h-40 object-cover rounded-md" />
            <h3 className="text-lg font-semibold mt-4 text-black">{tour.title}</h3>
            <p className="text-sm text-gray-600 mt-2 line-clamp-3">{tour.description}</p>
          </div>
        ))}
      </div>

      {/* Know More */}
      <div className="mt-6">
        <button className="flex items-center text-blue-700 font-semibold hover:underline">
          Know More <ArrowRight className="ml-2 h-5 w-5" />
        </button>
      </div>
    </section>
  );
};

export default CuratedTours;
