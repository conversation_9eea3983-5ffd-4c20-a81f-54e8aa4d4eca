"use client";

import { useRouter } from 'next/navigation';
import type { PackageDetails } from '@/app/types/PackageDetails';
import crypto from 'crypto';

interface PaymentModalProps {
  showPaymentModal: boolean;
  setShowPaymentModal: (show: boolean) => void;
  packageData: PackageDetails;
  paymentError: string;
  setPaymentError: (error: string) => void;
  paymentProcessing: boolean;
  setPaymentProcessing: (processing: boolean) => void;
  paymentSuccess: boolean;
  setPaymentSuccess: (success: boolean) => void;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  showPaymentModal,
  setShowPaymentModal,
  packageData,
  paymentError,
  setPaymentError,
  paymentProcessing,
  setPaymentProcessing,
  paymentSuccess,
  setPaymentSuccess
}) => {
  const router = useRouter();

  // PayU Configuration
  const PAYU_KEY = process.env.NEXT_PUBLIC_PAYU_MERCHANT_KEY;
  const PAYU_SALT = process.env.NEXT_PUBLIC_PAYU_MERCHANT_SALT;
  const PAYU_BASE_URL = process.env.NEXT_PUBLIC_PAYU_API_URL;
  const PAYMENT_SUCCESS_URL = process.env.NEXT_PUBLIC_APP_BASE_URL + "/payment-success";
  const PAYMENT_FAILURE_URL = process.env.NEXT_PUBLIC_APP_BASE_URL + "/payment-failure";

  const generateHash = (data: any) => {
    // Hash format: sha512(key|txnid|amount|productinfo|firstname|email|udf1|udf2|udf3|udf4|udf5||||||SALT)
    const hashString = `${PAYU_KEY}|${data.txnid}|${data.amount}|${data.productinfo}|${data.firstname}|${data.email}|${data.udf1 || ''}|${data.udf2 || ''}|${data.udf3 || ''}|${data.udf4 || ''}|${data.udf5 || ''}||||||${PAYU_SALT}`;
    return crypto.createHash('sha512').update(hashString).digest('hex');
  };

  const initiatePayment = async () => {
    try {
      setPaymentProcessing(true);

      // Generate unique transaction ID with timestamp and random string
      const txnid = `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Payment data
      const paymentData = {
        key: PAYU_KEY,
        txnid: txnid,
        amount: packageData?.priceSummary.netSellingPrice.toString() || "0",
        productinfo: `${packageData?.packageCode} - European Tour Package`,
        firstname: localStorage.getItem('userName') || 'Guest',
        email: localStorage.getItem('userEmail') || '<EMAIL>',
        phone: localStorage.getItem('userMobile') || '',
        surl: PAYMENT_SUCCESS_URL,
        furl: PAYMENT_FAILURE_URL,
        udf1: '',
        udf2: '',
        udf3: '',
        udf4: '',
        udf5: 'BOLT_KIT_NODE_JS',
        hash: ''
      };

      // Generate hash
      paymentData.hash = generateHash(paymentData);

      // Create form and submit
      const form = document.createElement('form');
      form.method = 'POST';
      if (!PAYU_BASE_URL) {
        console.warn('PAYU_BASE_URL is undefined. Please check your environment variables.');
      }
      form.action = PAYU_BASE_URL || "";

      // Add all fields to the form
      Object.entries(paymentData).forEach(([key, value]) => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value?.toString() || "";
        form.appendChild(input);
      });

      document.body.appendChild(form);
      form.submit();
      document.body.removeChild(form);

    } catch (error: any) {
      setPaymentError(error.message || 'Error initiating payment');
      setPaymentProcessing(false);
    }
  };

  if (!showPaymentModal && !paymentSuccess) {
    return null;
  }

  return (
    <>
      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Complete Payment</h3>
              <button
                onClick={() => !paymentProcessing && setShowPaymentModal(false)}
                className="text-gray-400 hover:text-gray-500 disabled:opacity-50"
                disabled={paymentProcessing}
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="mb-6">
              <p className="text-gray-600 mb-4">Package: {packageData?.packageCode} - European Tour Package</p>
              <p className="text-gray-600 mb-4">Amount: ₹{packageData?.priceSummary.netSellingPrice.toLocaleString('en-IN')}</p>
            </div>

            {paymentError && (
              <div className="mb-4 p-3 bg-red-50 text-red-600 rounded-md">
                {paymentError}
              </div>
            )}

            {paymentProcessing && (
              <div className="mb-4 p-3 bg-blue-50 text-blue-600 rounded-md flex items-center">
                <svg className="animate-spin h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
                Processing payment...
              </div>
            )}

            <button
              onClick={initiatePayment}
              disabled={paymentProcessing}
              className="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-300"
            >
              {paymentProcessing ? 'Processing...' : 'Pay Now'}
            </button>
          </div>
        </div>
      )}

      {/* Success Modal */}
      {paymentSuccess && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4 text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
              <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Payment Successful!</h3>
            <p className="text-sm text-gray-500 mb-6">Your booking has been confirmed. Redirecting to bookings...</p>
            <button
              onClick={() => {
                setPaymentSuccess(false);
                router.push('/my-bookings');
              }}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              View My Bookings
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default PaymentModal;
