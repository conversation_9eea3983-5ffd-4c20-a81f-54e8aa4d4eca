"use client";

import { useEffect, useState } from 'react';
import ActivityCarouselComponent from './ActivityCarouselComponent/ActivityCarouselComponent';
import { findDisplayInFromObject, getValueFromPath } from '@/app/utility/display_in_tracker';

interface ActivityDetailsProps {
	itinerary: any; // Should be properly typed in production
	isExpanded?: boolean;
	itineraryType?:string;
	from_download_pdf?:boolean;
}

const InformationDetails: React.FC<ActivityDetailsProps> = ({ itinerary ,isExpanded=true,itineraryType="website",from_download_pdf=false }) => {
		const display_in_tracker  = findDisplayInFromObject(itinerary);
		const youtube_link_list =display_in_tracker.length >0 ?
				display_in_tracker.filter((element, index)=>{
					const key = element.path.split(".").at(-1).replace(/displayin$/i, "");
					return (key.toLowerCase().includes("link") && element.value.map((itr:any)=>{
							itr.toLowerCase();
							return itr.split(" ")[0];
						}).includes(itineraryType.toLowerCase()));
				}).map((element)=>{
					const key = element.path.split(".").at(-1).replace(/displayin$/i, "");
					const value = getValueFromPath(itinerary, element.path);
					return {
						youtube_link_embedded:true,
						display:element.value,
						link_id :value.split("v=")[1]
					};
				}) : [];
	const [showFullDesc, setShowFullDesc] = useState(false);
	const desc = itinerary.selected_excursion.description || '';
	const truncated = desc.length > 120 && !showFullDesc;
	const displayDesc = (truncated&&!from_download_pdf) ? desc.slice(0, 120) + '...' : desc;
	itinerary.selected_excursion.images = itinerary?.selected_excursion?.images?.filter((img:any)=>img.filePath!=="" && img.filePath!==null) || [];
	itinerary.selected_excursion.images = itinerary.selected_excursion.images.filter((element:any)=>{
		if(element.displayIn || element.DisplayIn ){
			if(element.displayIn){
				return element.displayIn.includes(itineraryType?.toLowerCase());
			} else if(element.DisplayIn){
				return element.DisplayIn.map((e:any)=>{
					let data= e.split(" ")[0];
					return data.toLowerCase();
				}).includes(itineraryType?.toLowerCase());

			}
		}
		return true;
	})
	const imageUrl = itinerary.selected_excursion.images && itinerary.selected_excursion.images[0]?.filePath || null;
	// Find the correct package details using subCode
	const packageDetailsArr = itinerary.selected_excursion.packageDetails || [];
	const matchedPackage = packageDetailsArr.find((pkg: any) => pkg.subCode === itinerary.selected_excursion.subCode);
	const tourDuration = matchedPackage?.tourDuration || itinerary.selected_excursion.duration;
	const inclusions = matchedPackage?.inclusions || '';
	const inclusionList = inclusions
		? inclusions.split('\n').map((item: string) => item.trim()).filter(Boolean)
		: [];
		
	return (
		<>
		<div className="flex gap-4 items-start w-full">
			{imageUrl ? (
				// <div className="w-24 h-24 rounded-xl overflow-hidden flex-shrink-0">
				<div className="w-[120px] h-[120px]  rounded-xl overflow-hidden flex-shrink-0">
					
					<img
						src={imageUrl}
						alt={itinerary.selected_excursion.informationName || 'Activity image'}
						width={96}
						height={96}
						className="object-cover w-full h-full"
						onError={(e) => {
							const target = e.target as HTMLImageElement;
							target.onerror = null; // Prevent infinite loop in case fallback also fails
							target.src = '/assets/tours/imagetwo.png';
						}}
					/>
				</div>
			) : (
				<div className="w-[120px] h-[120px] rounded-xl bg-gray-200 flex items-center justify-center text-gray-400 flex-shrink-0">
					<svg width="32" height="32" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" className="w-8 h-8">
						<path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5V7a2 2 0 012-2h14a2 2 0 012 2v9.5M3 16.5l4.5-4.5a2 2 0 012.828 0L21 16.5M3 16.5V19a2 2 0 002 2h14a2 2 0 002-2v-2.5" />
					</svg>
				</div>
			)}
			<div className="flex min-h-[120px] flex-1 flex-col justify-between">
				<div>
				<div className="font-bold text-[16px] text-black mb-1">
					{itinerary.selected_excursion.informationName}
				</div>
				{tourDuration && (
					<div className="flex items-center gap-2 text-[#344054] text-sm font-bold my-3">
						
						<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M11.9982 22.0527C17.52 22.0527 21.9964 17.5756 21.9964 12.0527C21.9964 6.52989 17.52 2.05273 11.9982 2.05273C6.47634 2.05273 2 6.52989 2 12.0527C2 17.5756 6.47634 22.0527 11.9982 22.0527Z" stroke="#667085" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M11.998 6.05273V12.0527L15.998 14.0527" stroke="#667085" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>

						<span className='font-bold text-[14px] text-[#344054]'>{tourDuration}</span>
					</div>
				)}
				</div>
				<div className="text-[12px] mt-1 font-bold text-black">
					<div dangerouslySetInnerHTML={{ __html: displayDesc }} className='itinerary-content text-[12px] mt-1 font-bold text-black'>
						
					</div>
					{truncated&&!from_download_pdf && (
						<button
							className="text-[#175CD3] ml-1 font-medium hover:underline text-[14px]"
							onClick={() => setShowFullDesc(true)}
						>
							Read more
						</button>
					)}
				</div>
				{/* {inclusionList.length > 0 && isExpanded && (
					<>
						<p className="text-xs text-[#667085] font-semibold mb-2 mt-4">Inclusion</p>
						<div className="text-[#344054] text-[14px] font-normal flex flex-wrap gap-x-2 gap-y-1">
							{inclusionList.map((item: string, idx: number) => (
								<span key={idx}>
									{item}
									{idx < inclusionList.length - 1 && <span className="mx-1">&bull;</span>}
								</span>
							))}
						</div>
					</>
				)} */}
			</div>
		</div>
		{isExpanded && 
		<div>
			{itinerary?.selected_excursion?.images && itinerary?.selected_excursion?.images?.length>0 && 
				<ActivityCarouselComponent images={itinerary.selected_excursion.images} youtubelink={youtube_link_list}/>
			}
		 {/* Inclusion Section */}
			{/* Inclusion Section */}
				{inclusionList.length > 0 && (
					<div className="bg-white border border-gray-200 rounded-lg shadow-md p-4 mb-4 mt-6">
						<h3 className="text-sm font-semibold text-gray-800 mb-2">Inclusion</h3>
						<div className="text-sm text-gray-700 leading-relaxed flex flex-wrap gap-x-2 gap-y-1">
							{inclusionList.map((item: string, idx: number) => (
								<span
									key={idx}
									className="text-sm"
									dangerouslySetInnerHTML={{ __html: item }}
								/>
							))}
						</div>
					</div>
				)}
			{/* Dynamic Display Section */}
			{display_in_tracker.length > 0 &&
				display_in_tracker.map((element, index) => {
					const key = element.path.split(".").at(-1).replace(/displayin$/i, "");
					const value = getValueFromPath(itinerary, element.path);
					if(key.toLowerCase() !== "meetingpoint" && !value){
						return;
					} else if(key.toLowerCase() == "meetingpoint"){
						const basePath = element.path.split(".").slice(0, -1).join(".");
						const lat = getValueFromPath(itinerary, `${basePath}.meetingPointLocationLatitude`);
						const lng = getValueFromPath(itinerary, `${basePath}.meetingPointLocationLongitude`);
						if(!lat || !lng ){
							return;
						}
					}
					const shouldShow =
						element.key.toLowerCase() !== "displayin" &&
						!key.toLowerCase().includes("youtube") &&
						element.value.map((itr: any) => itr.toLowerCase().split(" ")[0])
							.includes(itineraryType.toLowerCase());

					if (!shouldShow) return null;

					const label = key
						.replace(/([A-Z])/g, " $1")
						.replace(/^./, (str: any) => str.toUpperCase());

					const showMapLink =
						key.toLowerCase() === "meetingpoint" &&
						itineraryType.toLowerCase() === "final";

					const basePath = element.path.split(".").slice(0, -1).join(".");
					const lat = getValueFromPath(itinerary, `${basePath}.meetingPointLocationLatitude`);
					const lng = getValueFromPath(itinerary, `${basePath}.meetingPointLocationLongitude`);

					return (
						<div
							key={index}
							className="bg-white border border-gray-200 rounded-lg shadow-md p-4 mb-4"
						>
							<div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-2">
								<h3 className="text-sm font-semibold text-gray-800">
									{label}
								</h3>

								{showMapLink && lat && lng && (
									<a
										href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(`${lat},${lng}`)}`}
										target="_blank"
										rel="noopener noreferrer"
										className="text-xs text-blue-600 hover:text-blue-800 underline font-medium mt-2 sm:mt-0"
									>
										Get Directions
									</a>
								)}
							</div>

							{key.toLowerCase().includes("link") ? (
								key.toLowerCase().includes("youtube") ? (
									<div className="w-full aspect-video">
										<iframe
											className="w-full h-full rounded-md"
											src={`https://www.youtube.com/embed/${value.split("v=")[1]}`}
											frameBorder="0"
											allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
											allowFullScreen
										/>
									</div>
								) : (
									<a
										href={value}
										target="_blank"
										rel="noopener noreferrer"
										className="text-sm text-blue-600 underline break-words"
									>
										{value}
									</a>
								)
							) : (
								<p
									className="text-sm text-gray-700 leading-relaxed"
									dangerouslySetInnerHTML={{ __html: value }}
								/>
							)}
						</div>
					);
				})}

          {typeof window !== 'undefined' && localStorage.getItem('wy_user_data') && itineraryType!=='website' &&
            (() => {
              try {
                const role = JSON.parse(localStorage.getItem('wy_user_data') || '{}')?.role?.toLowerCase();

                // Normalize the role to category
                const isAdmin = role === 'admin' || role === 'subadmin';
                const isCustomer = role === 'customer';
                const isAgent = !isCustomer && !isAdmin; // All others treated as agent

                // Collect keys to show
                const noteKeys: (keyof typeof itinerary)[] = [];
                if (isCustomer) {
                  noteKeys.push('customerNote');
                }
                if (isAgent) {
                  noteKeys.push('customerNote', 'agentNote');
                }
                if (isAdmin) {
                  noteKeys.push('customerNote', 'agentNote', 'adminNote', 'subAdminNote');
                }

                // Render notes
                return (
                  <>
                    {noteKeys.map((key:any) =>
                      itinerary?.[key] ? (
                        <div key={key} className="bg-white border border-gray-200 rounded-lg shadow-md p-4 mb-4 mt-6">
                          <p className="text-sm font-semibold text-gray-800 mb-2">Note ({key})</p>
                          <div
                            className="itinerary-content text-sm text-gray-700 leading-relaxed"
                            dangerouslySetInnerHTML={{ __html: itinerary[key] as string }}
                          />
                        </div>
                      ) : null
                    )}
                  </>
                );
              } catch {
                return null;
              }
            })()
          }
		</div>}
		</>
	);
};

export default InformationDetails;
