import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type CurrencyStore = {
  selectedCurrency: string;
  rates: { [key: string]: number };
  symbols:any;
  setSelectedCurrency: (currency: string) => void;
  setRates: (rates: { [key: string]: number }) => void;
  convertFromINR: (inrPrice: number) => number;
};
export const useCurrencyStore = create<CurrencyStore>()(
  persist(
    (set, get) => ({
      selectedCurrency: 'INR',
      rates: {},
		symbols: {
			AED: "د.إ",
			AFN: "؋",
			ALL: "L",
			AMD: "֏",
			ANG: "ƒ",
			AOA: "Kz",
			ARS: "$",
			AUD: "A$",
			AWG: "ƒ",
			AZN: "₼",
			BAM: "KM",
			BBD: "$",
			BDT: "৳",
			BGN: "лв",
			BHD: ".د.ب",
			BIF: "FBu",
			BMD: "$",
			BND: "$",
			BOB: "Bs.",
			BRL: "R$",
			BSD: "$",
			BTN: "Nu.",
			BWP: "P",
			BYN: "Br",
			BZD: "$",
			CAD: "C$",
			CDF: "FC",
			CHF: "CHF",
			CLP: "$",
			CNY: "¥",
			COP: "$",
			CRC: "₡",
			CUP: "$",
			CVE: "$",
			CZK: "Kč",
			DJF: "Fdj",
			DKK: "kr",
			DOP: "RD$",
			DZD: "دج",
			EGP: "£",
			ERN: "Nfk",
			ETB: "Br",
			EUR: "€",
			FJD: "$",
			FKP: "£",
			GBP: "£",
			GEL: "₾",
			GHS: "₵",
			GIP: "£",
			GMD: "D",
			GNF: "FG",
			GTQ: "Q",
			GYD: "$",
			HKD: "HK$",
			HNL: "L",
			HRK: "kn",
			HTG: "G",
			HUF: "Ft",
			IDR: "Rp",
			ILS: "₪",
			INR: "₹",
			IQD: "ع.د",
			IRR: "﷼",
			ISK: "kr",
			JMD: "J$",
			JOD: "د.ا",
			JPY: "¥",
			KES: "KSh",
			KGS: "лв",
			KHR: "៛",
			KMF: "CF",
			KRW: "₩",
			KWD: "د.ك",
			KYD: "$",
			KZT: "₸",
			LAK: "₭",
			LBP: "ل.ل",
			LKR: "Rs",
			LRD: "$",
			LSL: "L",
			LYD: "ل.د",
			MAD: "د.م.",
			MDL: "L",
			MGA: "Ar",
			MKD: "ден",
			MMK: "K",
			MNT: "₮",
			MOP: "MOP$",
			MRU: "UM",
			MUR: "₨",
			MVR: "Rf",
			MWK: "MK",
			MXN: "$",
			MYR: "RM",
			MZN: "MT",
			NAD: "$",
			NGN: "₦",
			NIO: "C$",
			NOK: "kr",
			NPR: "₨",
			NZD: "NZ$",
			OMR: "ر.ع.",
			PAB: "B/.",
			PEN: "S/",
			PGK: "K",
			PHP: "₱",
			PKR: "₨",
			PLN: "zł",
			PYG: "₲",
			QAR: "ر.ق",
			RON: "lei",
			RSD: "дин.",
			RUB: "₽",
			RWF: "FRw",
			SAR: "﷼",
			SBD: "$",
			SCR: "₨",
			SDG: "ج.س.",
			SEK: "kr",
			SGD: "S$",
			SHP: "£",
			SLL: "Le",
			SOS: "S",
			SRD: "$",
			SSP: "£",
			STD: "Db",
			SYP: "£",
			SZL: "L",
			THB: "฿",
			TJS: "ЅМ",
			TMT: "m",
			TND: "د.ت",
			TOP: "T$",
			TRY: "₺",
			TTD: "TT$",
			TWD: "NT$",
			TZS: "TSh",
			UAH: "₴",
			UGX: "USh",
			USD: "$",
			UYU: "$U",
			UZS: "лв",
			VEF: "Bs",
			VND: "₫",
			VUV: "VT",
			WST: "WS$",
			XAF: "FCFA",
			XCD: "EC$",
			XOF: "CFA",
			XPF: "₣",
			YER: "﷼",
			ZAR: "R",
			ZMW: "ZK"
	},
	setSelectedCurrency: (currency) => set({ selectedCurrency: currency }),
		setRates: (rates) => set({ rates }),
		convertFromINR: (inrPrice) => {
			const { selectedCurrency, rates } = get();
			const inrRate = rates['INR'];
			const targetRate = rates[selectedCurrency];
			if (!inrRate || !targetRate) return inrPrice;
			return (targetRate / inrRate) * inrPrice;
		}
	}),
	{
		name: 'currency-store',
		partialize: (state) => ({
			selectedCurrency: state.selectedCurrency,
			rates: state.rates
		})
	}
  )
);