import React, { useEffect, useRef } from 'react';

const CustomCheckbox = ({ checked, onChange, className, label_text }: any) => {
  const boxRef:any = useRef(null);
  const svgRef:any = useRef(null);

  useEffect(() => {
    if (boxRef.current && svgRef.current) {
      if (checked) {
        boxRef.current.style.backgroundColor = '#dbeafe'; // light blue
        boxRef.current.style.borderColor = '#2563eb';     // blue border
        svgRef.current.style.opacity = '1';
      } else {
        boxRef.current.style.backgroundColor = 'white';
        boxRef.current.style.borderColor = '#D0D5DD';
        svgRef.current.style.opacity = '0';
      }
    }
  }, [checked]);

  return (
    <div>
      <label style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer' }}>
        <input
          type="checkbox"
          checked={checked}
          onChange={onChange}
          style={{ display: 'none' }}
        />
        <span
          ref={boxRef}
          style={{
            width: '20px',
            height: '20px',
            border: '2px solid #D0D5DD',
            borderRadius: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: '0.2s ease',
            backgroundColor: 'white',
          }}
        >
          <svg
            ref={svgRef}
            style={{
              width: '16px',
              height: '16px',
              color: '#2563eb',
              opacity: 0,
              transition: 'opacity 0.2s ease',
            }}
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path d="M16.707 5.293a1 1 0 0 0-1.414 0L8 12.586 4.707 9.293a1 1 0 1 0-1.414 1.414l4 4a1 1 0 0 0 1.414 0l8-8a1 1 0 0 0 0-1.414z" />
          </svg>
        </span>
        <span style={{ marginLeft: '8px', color: '#000' }}>{label_text}</span>
      </label>
    </div>
  );
};

export default CustomCheckbox;
