"use client";

import React, { useEffect, useState } from 'react';
import NavBar from '@/app/components/NavBar';
import Footer from '@/app/components/Footer';
import Breadcrumb from '@/app/components/BreadCrumbs';
import axios from 'axios';

const AboutUs = () => {
  const [policies, setPolicies] = useState<{
    paymentPolicy: string;
    refundPolicy: string;
    termsAndConditions: string;
    aboutUs: string;
  }>({
    paymentPolicy: '',
    refundPolicy: '',
    termsAndConditions: '',
    aboutUs: ''
  });
useEffect(()=>{
  const fetchPolicies = async () => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/web-images`);

      if (response.data) {
        const policyData = response.data;

        const paymentPolicy = policyData.find((policy: any) =>
          // policy.imageText === 'Payment_Policy'
          policy.imageText === 'Payment Policy'
        )?.content || 'Payment policy information not available.';


        const refundPolicy = policyData.find((policy: any) =>
          // policy.imageText === 'Refund_Policy'
          policy.imageText === 'Cancellation & Rescheduling Terms'
        )?.content || 'Refund policy information not available.';

        const termsAndConditions = policyData.find((policy: any) =>
          // policy.imageText === 'Terms_And_Condition'
          policy.imageText === 'Terms and Conditions'
        )?.content || 'Terms and conditions not available.';
        const aboutUs = policyData.find((policy: any) =>
          // policy.imageText === 'Terms_And_Condition'
          policy.imageText === 'About Us'
        )?.content || 'Terms and conditions not available.';

        setPolicies({
          paymentPolicy,
          refundPolicy,
          termsAndConditions,
          aboutUs
        });
      }
    } catch (err) {
      console.error('Error fetching policies:', err);
      // setError('Failed to load policies. Please try again later.');
    } finally {
      // setLoading(false);
    }
  };

  fetchPolicies();
},[])
  return (
    <main className="min-h-screen">
      <NavBar />
      <Breadcrumb />
      <div className='itinerary-content max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12' dangerouslySetInnerHTML={{ __html: policies.aboutUs}} />
      <Footer />
    </main>
  );
};

export default AboutUs; 