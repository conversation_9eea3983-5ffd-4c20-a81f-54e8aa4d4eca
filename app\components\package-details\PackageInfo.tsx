"use client";

import { PackageDetails as PackageDetailsType } from '@/app/types/PackageDetails';

interface PackageInfoProps {
  packageData: PackageDetailsType;
}

const PackageInfo: React.FC<PackageInfoProps> = ({ packageData }) => {
  return (
    <div className="space-y-4">
      <p className="text-3xl sm:text-2xl font-bold text-[#1E1E1E]">
        {packageData.packageTitle}
      </p>
      <div className="flex flex-wrap gap-8">
        {packageData.inclusions
          .filter(
            (inc: any) =>
              inc.included &&
              ['hotel', 'sightseeing', 'intercity'].includes(inc.name.toLowerCase())
          )
          .map((item: any, index: number) => (
            <div key={index} className="flex items-center gap-4">
              <div className="text-blue-600 w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6">
                {item.name.toLowerCase() === 'hotel' ? (
                  <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22.1654 10.5H5.83203V7.58333C5.83203 7.27391 5.95447 6.97717 6.17326 6.75838C6.39205 6.53958 6.6888 6.41667 6.99822 6.41667H21.0004C21.3098 6.41667 21.6065 6.53958 21.8253 6.75838C22.0441 6.97717 22.1665 7.27391 22.1665 7.58333L22.1654 10.5Z" stroke="#175CD3" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M5.83203 21.5833V17.5H22.1654V21.5833" stroke="#175CD3" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M5.83203 17.5V10.5" stroke="#175CD3" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M22.1654 17.5V10.5" stroke="#175CD3" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M9.33203 14H11.6654" stroke="#175CD3" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M16.332 14H18.6654" stroke="#175CD3" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                ) : item.name.toLowerCase() === 'sightseeing' ? (
                  <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.4188 17.8267C6.54724 17.8252 6.66474 17.7537 6.72537 17.6405C6.78584 17.5272 6.7799 17.3898 6.70974 17.2822C6.53333 17.018 2.45294 10.7598 7.75426 6.03258C8.80566 5.29102 10.0607 4.89274 11.3474 4.89242C12.6339 4.89196 13.8893 5.28961 14.9411 6.03054C14.9743 6.05617 18.2279 8.60866 17.5743 11.8181H17.5744C17.5358 12.0074 17.6582 12.1921 17.8475 12.2306C18.0368 12.2691 18.2214 12.1469 18.26 11.9575C19.0017 8.31642 15.5167 5.5899 15.3517 5.4639C11.1781 2.62702 7.36934 5.44546 7.31014 5.49218C1.52854 10.6458 6.08138 17.6018 6.12794 17.6714C6.19279 17.7684 6.30201 17.8265 6.41872 17.8265L6.4188 17.8267Z" fill="#175CD3" />
                    <path d="M21.5812 17.8267C21.4528 17.8252 21.3353 17.7537 21.2746 17.6405C21.2142 17.5272 21.2201 17.3898 21.2903 17.2822C21.4667 17.018 25.5471 10.7598 20.2458 6.03258C19.1944 5.29102 17.9393 4.89274 16.6526 4.89242C15.3661 4.89196 14.1107 5.28961 13.0589 6.03054C13.0257 6.05617 9.77213 8.60866 10.4257 11.8181H10.4256C10.4642 12.0074 10.3418 12.1921 10.1525 12.2306C9.96318 12.2691 9.77861 12.1469 9.74002 11.9575C8.99834 8.31642 12.4833 5.5899 12.6483 5.4639C16.8219 2.62702 20.6307 5.44546 20.6899 5.49218C26.4715 10.6458 21.9186 17.6018 21.8721 17.6714C21.8072 17.7684 21.698 17.8265 21.5813 17.8265L21.5812 17.8267Z" fill="#175CD3" />
                    <path d="M14.0003 23.3333C14.3097 23.3333 14.6065 23.2104 14.8253 22.9916C15.0441 22.7728 15.167 22.4761 15.167 22.1667V17.5C15.167 17.1906 15.0441 16.8938 14.8253 16.675C14.6065 16.4562 14.3097 16.3333 14.0003 16.3333C13.6909 16.3333 13.3942 16.4562 13.1754 16.675C12.9566 16.8938 12.8337 17.1906 12.8337 17.5V22.1667C12.8337 22.4761 12.9566 22.7728 13.1754 22.9916C13.3942 23.2104 13.6909 23.3333 14.0003 23.3333Z" fill="#175CD3" />
                    <path d="M14.0003 14C14.7732 14 15.4003 13.3729 15.4003 12.6C15.4003 11.8271 14.7732 11.2 14.0003 11.2C13.2274 11.2 12.6003 11.8271 12.6003 12.6C12.6003 13.3729 13.2274 14 14.0003 14Z" fill="#175CD3" />
                  </svg>
                ) : item.name.toLowerCase() === 'intercity' ? (
                  <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20.9987 19.8333H24.4987C24.8081 19.8333 25.1049 19.7104 25.3237 19.4916C25.5425 19.2728 25.6654 18.9761 25.6654 18.6666V14.7361C25.6654 14.5011 25.5945 14.2716 25.4619 14.0776C25.3293 13.8836 25.1412 13.7341 24.9222 13.6488L19.832 11.6666L16.332 5.83331H6.9987L4.66536 11.6666H3.4987C3.18928 11.6666 2.89253 11.7896 2.67374 12.0084C2.45495 12.2271 2.33203 12.5239 2.33203 12.8333V18.6666C2.33203 18.9761 2.45495 19.2728 2.67374 19.4916C2.89253 19.7104 3.18928 19.8333 3.4987 19.8333H6.9987" stroke="#175CD3" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M18.6654 22.1667C19.2842 22.1667 19.8777 21.9208 20.3153 21.4832C20.7529 21.0457 20.9987 20.4522 20.9987 19.8333C20.9987 19.2145 20.7529 18.621 20.3153 18.1834C19.8777 17.7458 19.2842 17.5 18.6654 17.5C18.0465 17.5 17.453 17.7458 17.0154 18.1834C16.5779 18.621 16.332 19.2145 16.332 19.8333C16.332 20.4522 16.5779 21.0457 17.0154 21.4832C17.453 21.9208 18.0465 22.1667 18.6654 22.1667Z" stroke="#175CD3" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M9.33333 22.1667C8.71449 22.1667 8.121 21.9208 7.68342 21.4832C7.24583 21.0457 7 20.4522 7 19.8333C7 19.2145 7.24583 18.621 7.68342 18.1834C8.121 17.7458 8.71449 17.5 9.33333 17.5C9.95217 17.5 10.5457 17.7458 10.9832 18.1834C11.4208 18.621 11.6667 19.2145 11.6667 19.8333C11.6667 20.4522 11.4208 21.0457 10.9832 21.4832C10.5457 21.9208 9.95217 22.1667 9.33333 22.1667Z" stroke="#175CD3" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M16.3346 19.8333H11.668" stroke="#175CD3" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                ) : null}
              </div>
              <span className="text-sm mt-1 font-bold text-black capitalize">
                {item.name}
              </span>
            </div>
          ))}
      </div>
      <div className="flex flex-wrap gap-2 mt-32">
        <span className="px-2.5 sm:px-3 py-1 sm:py-1.5 bg-[#F2F4F7] rounded-full text-xs sm:text-sm flex items-center gap-1 sm:gap-1.5">
          <span className="text-[#344054] text-xs capitalize font-medium">{packageData.packageType || 'Standard'}</span>
        </span>
      </div>
    </div>
  );
};

export default PackageInfo;
