
"use client";
// app/payment-details/[id]/page.tsx
import dynamic from 'next/dynamic';

const PaymentDetailRender = dynamic(() => import('./PaymentDetailPageContent'), { ssr: false });
import { useEffect, useState, useContext } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import NavigationBar from '@/app/components/NavBar';
import Breadcrumb from '@/app/components/BreadCrumbs';
import Footer from '@/app/components/Footer';
import Checkout from '@/app/components/Checkout';
import { AppContext } from '@/app/context/useAppContext';

import { Suspense } from 'react';
// import PaymentDetailRender from './PaymentDetailPageContent';
// import CheckoutPage from './CheckoutPage'; // move logic to this file

function PaymentDetailPageRender() {
  const params = useParams();
  const bookingId = params?.id as string;
	const { bookingData } = useContext(AppContext);
	const searchParams = useSearchParams();
	const [breadcrumbs, setBreadcrumbs] = useState<any>([]);

	useEffect(() => {
		const packageCode = bookingData?.packageData?.packageCode || "";
		const custom = searchParams.get('custom');

		const url_packagedetail = custom === "true"
			? `/tours/package-details?custom=true&query=${packageCode}`
			: `/tours/package-details?query=${packageCode}`;

		const url_traveller = custom === "true"
			? `/tours/package-details/travellers?custom=true`
			: `/tours/package-details/travellers`;

		const breadCrumb = [
			 {
        label: "My Trips",
        link: "/mytrips"
      },
      {
        label: "Payment Details",
        link: "/last"
      }
		];

		setBreadcrumbs(breadCrumb);
	}, [searchParams, bookingData]);

	return (
		<main className="min-h-screen">
			<NavigationBar />
			<Breadcrumb breadCrumb={breadcrumbs} />
			<PaymentDetailRender bookingId={bookingId}/>
			<Footer />
		</main>
	);
}

export default function PaymentDetailPage() {

  return (
    <Suspense fallback={<div></div>}>
      <PaymentDetailPageRender />
    </Suspense>
  );
}
 
