"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useQueryStore } from "../store/queryStore";

interface Slide {
  id: number;
  image: string;
}

interface BannerCarouselProps {
  slides: Slide[];
}

const BannerCarousel: React.FC<BannerCarouselProps> = ({ slides = [] }) => {
  const setQuery = useQueryStore((state) => state.setQuery);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const router = useRouter();

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [slides.length]);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setQuery(searchQuery);
      router.push(`/vacation/tours?query=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  useEffect(() => {
    console.log("slides", slides);
  }, []);

  return (
    <div className="px-4 sm:px-6 lg:px-[30px]">
      <div 
        className="relative w-full overflow-hidden mt-6 mx-auto"
        style={{
          width: '100%',
          height: 'clamp(350px, 55vw, 622px)',
          minHeight: '350px',
          position: 'relative',
          overflow: 'hidden',
          borderRadius: 'clamp(16px, 3.5vw, 52px)'
        }}
      >
        {/* First overlay image with gradient */}
        <img 
          className="absolute left-0 top-0"
          style={{
            width: '100%',
            height: '100%',
            left: '0px',
            top: '0px',
            position: 'absolute',
            background: 'linear-gradient(270deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.50) 100%)',
            borderRadius: 'clamp(16px, 3.5vw, 52px)'
          }}
          src="https://placehold.co/1392x622" 
          alt="Overlay"
        />

        {/* Main banner image with complex gradients */}
        {slides.length > 0 && (
          <div
            className="absolute object-cover"
            style={{
              width: '100%',
              height: '130%',
              left: '0px',
              top: 'clamp(-25%, -150px, -20%)',
              position: 'absolute',
              background: `linear-gradient(258deg, rgba(0, 0, 0, 0.00) 24.01%, rgba(1, 36, 66, 0.70) 68.88%), linear-gradient(270deg, rgba(255, 255, 255, 0.50) -3.48%, rgba(13, 110, 253, 0.50) 95.11%), url(${slides[currentSlide].image}) lightgray 50% / cover no-repeat`,
              backgroundBlendMode: 'normal, multiply, normal',
              borderRadius: 'clamp(16px, 3.5vw, 52px)'
            }}
          />
        )}

        {/* Content Overlay */}
        <div className="relative z-10 flex flex-col justify-center text-white px-3 sm:px-6 md:px-10 lg:px-[115px] py-4 sm:py-6 md:py-8 lg:py-10 h-full">
          <div className="max-w-3xl w-full">
            <div className="mb-2 sm:mb-3 md:mb-4 lg:mb-6">
              <h2 className="font-manrope font-medium text-xl sm:text-2xl md:text-3xl lg:text-5xl xl:text-[64px] leading-tight tracking-tight">
                From India
                <br />
                <span className="font-extrabold text-2xl sm:text-3xl md:text-4xl lg:text-6xl xl:text-[84px] leading-tight block">
                  to the World.
                </span>
              </h2>
              <p className="text-xs sm:text-sm md:text-base lg:text-lg font-medium opacity-90 leading-snug max-w-sm sm:max-w-md pt-2 sm:pt-3 md:pt-4 lg:pt-5 pb-4 sm:pb-6 md:pb-8 lg:pb-10">
                Explore from hundreds of expert crafted experiences where every journey becomes an adventure.
              </p>
            </div>

            {/* Search Bar */}
            <form onSubmit={handleSearch} className="relative flex max-w-xs sm:max-w-md md:max-w-none w-full md:w-[521px] my-2 sm:my-3 md:my-4 lg:my-6 mb-6 sm:mb-8 md:mb-12 lg:mb-16">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full h-10 sm:h-12 md:h-16 text-xs sm:text-sm md:text-base text-gray-900 rounded-full px-4 sm:px-5 md:px-6 py-2 sm:py-3 bg-white focus:outline-none focus:ring-2 focus:ring-blue-600 placeholder-gray-500 placeholder:text-[#667085]"
                placeholder="Where are you travelling next?"
              />
              <button
                type="submit"
                className="absolute right-1 sm:right-2 top-1/2 -translate-y-1/2 bg-blue-600 text-white text-xs sm:text-sm font-medium px-3 sm:px-4 md:px-6 py-1.5 sm:py-2 md:py-3 rounded-full hover:bg-blue-700 transition-colors"
              >
                Search Now
              </button>
            </form>

            {/* Stats Section */}
            <div className="flex flex-wrap gap-4 sm:gap-8 md:gap-12 lg:gap-16 xl:gap-20 pb-6 sm:pb-8 md:pb-10 lg:pb-0">
              {[
                { count: "300+", label: "Destinations" },
                { count: "12M+", label: "Holidays Booked" },
                { count: "60+", label: "Partners" },
              ].map((stat, index) => (
                <div key={index} className="space-y-0 sm:space-y-1">
                  <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold leading-tight">{stat.count}</p>
                  <p className="text-xs sm:text-sm md:text-base text-gray-200 font-medium leading-snug">{stat.label}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Carousel Dots */}
        {slides.length > 1 && (
          <div className="absolute bottom-1 sm:bottom-2 md:bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide
                    ? "bg-white"
                    : "bg-white/50 hover:bg-white/75"
                }`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BannerCarousel;