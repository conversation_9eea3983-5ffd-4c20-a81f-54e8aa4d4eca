import { FC } from 'react';

interface PaymentSummaryProps {
  title: string;
  duration: string;
  amount: string;
  paymentMethod: string;
  platformCharges: number;
}

export const PaymentSummary: FC<PaymentSummaryProps> = ({
  title,
  duration,
  amount,
  paymentMethod,
  platformCharges,
}) => {
  const numericAmount = parseFloat(amount);
  const charges = (numericAmount * platformCharges) / 100;
  const total = numericAmount + charges;

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
      
      <div className="space-y-3 mb-6">
        <div className="flex justify-between">
          <span className="text-gray-600">Package</span>
          <span className="font-medium">{title}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Duration</span>
          <span className="font-medium">{duration}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Amount</span>
          <span className="font-medium">₹{numericAmount.toFixed(2)}</span>
        </div>
        {paymentMethod && (
          <div className="flex justify-between">
            <span className="text-gray-600">Payment Method</span>
            <span className="font-medium capitalize">{paymentMethod.replace(/-/g, ' ')}</span>
          </div>
        )}
        {platformCharges > 0 && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Platform Fee ({platformCharges}%)</span>
            <span className="text-gray-500">+ ₹{charges.toFixed(2)}</span>
          </div>
        )}
        
        <div className="border-t border-gray-200 pt-3 mt-3">
          <div className="flex justify-between font-semibold">
            <span>Total Amount</span>
            <span>₹{total.toFixed(2)}</span>
          </div>
        </div>
      </div>
      
      <div className="text-xs text-gray-500 space-y-2">
        <p>By proceeding, you agree to our Terms of Service and Privacy Policy.</p>
        <div className="flex items-center space-x-2">
          <svg className="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          <span>Secure SSL Encryption</span>
        </div>
      </div>
    </div>
  );
};
