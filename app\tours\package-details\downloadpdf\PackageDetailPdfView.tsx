"use client";

import NavigationBar from '@/app/components/NavBar';
import Breadcrumb from '@/app/components/BreadCrumbs';
import PackageDetails from '@/app/components/PackageDetails';
import Footer from '@/app/components/Footer';
import ParentComponent from '@/app/components/PopularToursParents';
import { LoadScript } from '@react-google-maps/api';

const PackageDetailPdfView = () => {
  const breadCrumb = [
    {
      label:"Tours",
      link:"/vacation/tours"
    },
    {
      label:"Package-Details",
      link:"/last"
    },
  ];
  return (
  // <LoadScript googleMapsApiKey="AIzaSyDVYB1GofLKV56yqxseuasNRX0nNLKWOQg">
    <div className="">
      <NavigationBar />
      <Breadcrumb breadCrumb={breadCrumb}/>
      <PackageDetails from_download_pdf={true}/>
      <ParentComponent />
      <Footer />
    </div>
  // </LoadScript>
  );
};

export default PackageDetailPdfView; 