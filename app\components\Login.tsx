"use client"

import React, { useEffect, useState } from "react";
import <PERSON>lide<PERSON> from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import axios from "axios";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";

// Define type for carousel image data
interface CarouselImage {
  image: string;
  text: string;
}

// Slick settings for carousel
const sliderSettings = {
  dots: false, // Hide dots globally
  infinite: true,
  auto: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
  arrows: true,
  responsive: [
    {
      breakpoint: 768, // On screens smaller than 768px (e.g., mobile)
      settings: {
        dots: false, // Ensure dots are hidden for mobile
        arrows: false, // Disable arrows on smaller screens for better UX
      },
    },
  ],
};

const carouselImages: CarouselImage[] = [
  {
    image: "/assets/tours/imageone.png", // Ensure this path is correct and accessible
    text: "Welcome to Your Next Adventure",
  },
  {
    image: "/assets/tours/imageone.png", // Ensure this path is correct and accessible
    text: "Join Us and Start Your Journey Today",
  },
  {
    image: "/assets/tours/imageone.png", // Ensure this path is correct and accessible
    text: "We Make Travel Planning Easy",
  },
];

const Login: React.FC = () => {
  const NEXT_PUBLIC_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
  const router = useRouter();
  const searchParams = useSearchParams();
  const [passwordVisible, setPasswordVisible] = useState<boolean>(false);
  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [showForgotPasswordModal, setShowForgotPasswordModal] = useState(false);
  const [forgotPasswordEmail, setForgotPasswordEmail] = useState("");
  const [forgotPasswordLoading, setForgotPasswordLoading] = useState(false);
  const [forgotPasswordError, setForgotPasswordError] = useState("");
  const [forgotPasswordSuccess, setForgotPasswordSuccess] = useState("");
  const [redirectUrl, setRedirectUrl] = useState('/');
  const [isMounted, setIsMounted] = useState(false);
  const [showVerificationModal, setShowVerificationModal] = useState<boolean>(false);
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [verificationCode, setVerificationCode] = useState<string>("");
  const [verificationLoading, setVerificationLoading] = useState<boolean>(false);

 const handleInputChange = (setter: React.Dispatch<React.SetStateAction<string>>) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => setter(event.target.value);

  const handleVerificationSubmit = async (event: React.FormEvent) => {
      event.preventDefault();
      setVerificationLoading(true);
      setError("");
      try {
        const response = await axios.get(`${NEXT_PUBLIC_API_BASE_URL}/api/auth/verify-email?code=${verificationCode}`);
        console.log(response);
        if (response.data) {
          setSuccessMessage(response?.data || "Email verified successfully! Redirecting to login page in 3 seconds");
          // Show success message for 3 seconds before redirecting
          setTimeout(() => {
            window.location.href = "/login";
          }, 3000);
        } else {
          setError(response.data.message || "Verification failed. Please try again.");
        }
      } catch (err: any) {
        setError(err.response?.data?.message || "Invalid verification code. Please try again.");
      } finally {
        setVerificationLoading(false);
      }
    };

  const togglePasswordVisibility = () => setPasswordVisible(!passwordVisible);

  // Type assertion to HTMLButtonElement for safe access to 'click'
  const handleArrowClick = (direction: "prev" | "next") => {
    const arrow = document.querySelector(`.slick-${direction}`) as HTMLElement;
    arrow?.click();
  };

  const resendVerificationCode = async()=>{
    try {
      const response = await axios.post(`${NEXT_PUBLIC_API_BASE_URL}/api/auth/resend-verification-code`, {email});
      console.log(response?.data);
    } catch (error) {
      console.log("Error in sending the code");
    }
  }
  

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await axios.post(`${NEXT_PUBLIC_API_BASE_URL}/api/auth/signin`, { email, password });
      
      if (response.data.authToken) {
        // Store the token in localStorage
        localStorage.setItem('authToken', response.data.authToken);
        localStorage.setItem('userEmail', email);
        localStorage.setItem('wy_user_data', JSON.stringify(response.data));
        
        // Set the token in axios default headers
        axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.authToken}`;
        
        // Redirect to the intended URL or home page
        const redirectTo = redirectUrl || '/';
        console.log('Redirecting to:', redirectTo);
        router.push(redirectTo);
      }
    } catch (err: any) {
      if(err.response?.data == "Please verify your email to login"){
        // curl --location 'http://139.59.87.111/api/auth/resend-verification-code' \
        // --header 'Content-Type: application/json' \
        // --data-raw '{
        // "email": "<EMAIL>"  
        // }'
       resendVerificationCode()
       setShowVerificationModal(true);
       return; 
      }
      setError(err.response?.data || "Invalid email or password");
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setForgotPasswordLoading(true);
    setForgotPasswordError("");
    setForgotPasswordSuccess("");

    try {
      const response = await axios.post(`${NEXT_PUBLIC_API_BASE_URL}/api/auth/forgot-password?email=${forgotPasswordEmail}`);
      setForgotPasswordSuccess("Password reset link has been sent to your email");
      setForgotPasswordEmail("");
      setTimeout(() => {
        setShowForgotPasswordModal(false);
      }, 3000);
    } catch (err: any) {
      setForgotPasswordError(err.response?.data || "Failed to send reset link");
    } finally {
      setForgotPasswordLoading(false);
    }
  };
  useEffect(() => {
    setIsMounted(true);
    
    // Only run on client side
    if (typeof window !== 'undefined') {
      const redirect = searchParams.get("redirect");
      if (redirect) {
        // Decode the URL and ensure it's a valid path
        try {
          const decodedUrl = decodeURIComponent(redirect);
          // Basic validation to ensure it's a path and not an external URL
          if (decodedUrl.startsWith('/')) {
            setRedirectUrl(decodedUrl);
          }
        } catch (error) {
          console.error('Invalid redirect URL:', error);
        }
      }
    }
  }, [searchParams]);
  return (
      <div className="min-h-screen bg-white">
        <div className="grid grid-cols-1 lg:grid-cols-2 lg:gap-20 px-6 py-16 max-w-7xl mx-auto">
          {/* Left Column - Carousel */}
          <div className="relative h-full overflow-hidden rounded-lg">
            {/* Carousel */}
            <Slider {...sliderSettings} className="h-full">
              {carouselImages.map((slide, index) => (
                <div key={index} className="relative h-full">
                  <img
                    src={slide.image}
                    alt={`Slide ${index + 1}`}
                    className="w-full h-[500px] object-cover rounded-2xl"
                  />
                  <div className="absolute bottom-20 sm:bottom-12 left-4 text-white text-xl font-bold z-10">
                    {slide.text}
                  </div>
                </div>
              ))}
            </Slider>

            {/* Arrow Buttons Container */}
            <div className="absolute bottom-8 right-4 flex items-center space-x-2 p-2 z-10">
              {/* Left Arrow */}
              <button
                className="text-white text-2xl border border-white rounded-full px-3 py-2 hover:bg-white/10 transition-colors"
                onClick={() => handleArrowClick("prev")}
              >
                &#8592;
              </button>

              {/* Right Arrow */}
              <button
                className="text-white text-2xl border border-white rounded-full px-3 py-2 hover:bg-white/10 transition-colors"
                onClick={() => handleArrowClick("next")}
              >
                &#8594;
              </button>
            </div>
          </div>

          {/* Right Column - Login Form */}
          <div className="flex items-center justify-center">
            <div className="max-w-md w-full">
              <h3 className="text-2xl font-semibold text-gray-800 mb-2">Login to Your Account</h3>
              <p className="text-sm text-gray-500 mb-6">Enter your registered email address and password</p>

              <form onSubmit={handleLogin}>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input 
                    type="email" 
                    value={email} 
                    onChange={(e) => setEmail(e.target.value)} 
                    className="w-full px-4 py-2.5 border border-gray-300 rounded-md bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                    required 
                  />
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                  <div className="relative">
                    <input 
                      type={passwordVisible ? "text" : "password"} 
                      value={password} 
                      onChange={(e) => setPassword(e.target.value)} 
                      className="w-full px-4 py-2.5 border border-gray-300 rounded-md bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                      required 
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      {passwordVisible ? "Hide" : "Show"}
                    </button>
                  </div>
                </div>
                {error && <p className="text-red-500 text-sm mb-2">{error}</p>}
                <button 
                  type="submit" 
                  className="w-full py-3 text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors" 
                  disabled={loading}
                >
                  {loading ? "Logging in..." : "Login"}
                </button>
              </form>

              {/* Forgot Password Link */}
              <div className="mt-4 text-center">
                <button
                  onClick={() => setShowForgotPasswordModal(true)}
                  className="text-blue-600 hover:underline"
                >
                  Forgot Password?
                </button>
              </div>

              {/* Create Account Section */}
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-600">
                  Don&apos;t have an account?{" "}
                  <Link href={"/signup"} className="text-blue-600 hover:underline">
                    Create your account
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Forgot Password Modal */}
        {showForgotPasswordModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold text-gray-800">Reset Password</h3>
                <button
                  onClick={() => {
                    setShowForgotPasswordModal(false);
                    setForgotPasswordEmail("");
                    setForgotPasswordError("");
                    setForgotPasswordSuccess("");
                  }}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <form onSubmit={handleForgotPassword}>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input
                    type="email"
                    value={forgotPasswordEmail}
                    onChange={(e) => setForgotPasswordEmail(e.target.value)}
                    className="w-full px-4 py-2.5 border border-gray-300 rounded-md bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
                {forgotPasswordError && (
                  <p className="text-red-500 text-sm mb-2">{forgotPasswordError}</p>
                )}
                {forgotPasswordSuccess && (
                  <p className="text-green-500 text-sm mb-2">{forgotPasswordSuccess}</p>
                )}
                <button
                  type="submit"
                  className="w-full py-3 text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
                  disabled={forgotPasswordLoading}
                >
                  {forgotPasswordLoading ? "Sending..." : "Send Reset Link"}
                </button>
              </form>
            </div>
          </div>
        )}

         {showVerificationModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">Email Verification</h3>
            <p className="text-gray-600 mb-4">{successMessage}</p>
            {!successMessage.includes("successfully") && (
              <form onSubmit={handleVerificationSubmit}>
                <div className="mb-4">
                  <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700 mb-1">
                    Verification Code
                  </label>
                  <input
                    id="verificationCode"
                    type="text"
                    value={verificationCode}
                    onChange={handleInputChange(setVerificationCode)}
                    className="w-full px-4 py-2.5 border border-gray-300 rounded-md bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
                {error && <p className="text-red-500 text-sm mb-2">{error}</p>}
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowVerificationModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
                    disabled={verificationLoading}
                  >
                    {verificationLoading ? "Verifying..." : "Verify"}
                  </button>
                </div>
              </form>
            )}
            {successMessage.includes("successfully") && (
              <div className="flex justify-end">
                <button
                  onClick={() => window.location.href = "/login"}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Okay
                </button>
              </div>
            )}
          </div>
        </div>
      )}
      </div>
  );
};

export default Login;
