"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import PaymentIntegration from '../../components/PaymentIntegration';
import NavBar from '../../components/NavBar';
import Footer from '../../components/Footer';

const ItalyTourPackage = () => {
  const router = useRouter();
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentError, setPaymentError] = useState('');
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [paymentProcessing, setPaymentProcessing] = useState(false);

  const packageDetails = {
    name: "Italy Adventure Tour",
    price: 199999, // Price in INR
    duration: "7 Days / 6 Nights",
    highlights: [
      "Visit the Colosseum in Rome",
      "Explore Venice's canals",
      "Tour Florence's art galleries",
      "Experience Vatican City",
      "Visit the Leaning Tower of Pisa"
    ]
  };

  const handleBookNow = () => {
    // Check if user is logged in
    const authToken = localStorage.getItem('authToken');
    if (!authToken) {
      router.push('/login');
      return;
    }
    setShowPaymentModal(true);
    setPaymentError('');
    setPaymentProcessing(false);
  };

  const handlePaymentSuccess = () => {
    setPaymentProcessing(false);
    setPaymentSuccess(true);
    setShowPaymentModal(false);
    
    // Redirect to bookings page after 3 seconds
    setTimeout(() => {
      router.push('/my-bookings');
    }, 3000);
  };

  const handlePaymentError = (error: string) => {
    setPaymentProcessing(false);
    setPaymentError(error);
    setTimeout(() => setPaymentError(''), 5000); // Clear error after 5 seconds
  };

  return (
    <div className="min-h-screen bg-white">
      <NavBar />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Left Column - Package Images */}
          <div className="space-y-6">
            <img
              src="/assets/italy/rome.jpg"
              alt="Colosseum"
              className="w-full h-96 object-cover rounded-lg shadow-lg"
            />
            <div className="grid grid-cols-2 gap-4">
              <img
                src="/assets/italy/venice.jpg"
                alt="Venice"
                className="w-full h-48 object-cover rounded-lg shadow-md"
              />
              <img
                src="/assets/italy/florence.jpg"
                alt="Florence"
                className="w-full h-48 object-cover rounded-lg shadow-md"
              />
            </div>
          </div>

          {/* Right Column - Package Details */}
          <div className="space-y-8">
            <div>
              <h1 className="text-4xl font-bold text-gray-900">{packageDetails.name}</h1>
              <p className="mt-2 text-xl text-blue-600">₹{packageDetails.price.toLocaleString('en-IN')}</p>
              <p className="text-gray-600">{packageDetails.duration}</p>
            </div>

            <div>
              <h2 className="text-2xl font-semibold text-gray-900">Highlights</h2>
              <ul className="mt-4 space-y-2">
                {packageDetails.highlights.map((highlight, index) => (
                  <li key={index} className="flex items-center text-gray-600">
                    <svg className="h-5 w-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    {highlight}
                  </li>
                ))}
              </ul>
            </div>

            <button
              onClick={handleBookNow}
              className="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors"
            >
              Book Now
            </button>
          </div>
        </div>
      </main>

      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Complete Payment</h3>
              <button
                onClick={() => !paymentProcessing && setShowPaymentModal(false)}
                className="text-gray-400 hover:text-gray-500 disabled:opacity-50"
                disabled={paymentProcessing}
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="mb-6">
              <p className="text-gray-600 mb-4">Package: {packageDetails.name}</p>
              <p className="text-gray-600 mb-4">Amount: ₹{packageDetails.price.toLocaleString('en-IN')}</p>
            </div>

            {paymentError && (
              <div className="mb-4 p-3 bg-red-50 text-red-600 rounded-md">
                {paymentError}
              </div>
            )}

            {paymentProcessing && (
              <div className="mb-4 p-3 bg-blue-50 text-blue-600 rounded-md flex items-center">
                <svg className="animate-spin h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
                Processing payment...
              </div>
            )}

            <PaymentIntegration
              amount={packageDetails.price}
              tourPackage={packageDetails.name}
              packageId="ITALY-001"
              duration={packageDetails.duration}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
            />
          </div>
        </div>
      )}

      {/* Success Modal */}
      {paymentSuccess && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4 text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
              <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Payment Successful!</h3>
            <p className="text-sm text-gray-500 mb-6">Your booking has been confirmed. Redirecting to bookings...</p>
            <button
              onClick={() => {
                setPaymentSuccess(false);
                router.push('/my-bookings');
              }}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              View My Bookings
            </button>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
};

export default ItalyTourPackage; 