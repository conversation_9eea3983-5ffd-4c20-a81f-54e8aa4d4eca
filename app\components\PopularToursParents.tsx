"use client"

import React, { useEffect, useState } from "react";
import axios from "axios";
import PopularTours from "./Recommended";

// Example fetching data in the parent component
const ParentComponent: any = ({heading_test,title_test}:any) => {
  // const [tours, setTours] = useState<any[]>([]);

  const [tours, setTours] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // useEffect(() => {
  //   const fetchTours = async () => {
  //     const response = await fetch("/api/tours"); // Adjust the URL to match your API
  //     const data = await response.json();
  //     setTours(data);
  //   };

  //   fetchTours();
  // }, []);

  // const tours = [
  //   {
  //     id: 1,
  //     title: "Paris City Escape",
  //     image: "/assets/tours/imageone.png",
  //     price: "$1,500",
  //     duration: "5 Days, 4 Nights",
  //     country: "France",
  //     flag: "🇫🇷",
  //   },
  //   {
  //     id: 2,
  //     title: "Swiss Alps Adventure",
  //     image: "/assets/tours/imagetwo.png",
  //     price: "$2,200",
  //     duration: "7 Days, 6 Nights",
  //     country: "Switzerland",
  //     flag: "🇨🇭",
  //   },
  //   {
  //     id: 3,
  //     title: "Tokyo Highlights",
  //     image: "/assets/tours/imagethree.png",
  //     price: "$1,800",
  //     duration: "6 Days, 5 Nights",
  //     country: "Japan",
  //     flag: "🇯🇵",
  //   },
  //   {
  //     id: 4,
  //     title: "Santorini Getaway",
  //     image: "/assets/tours/imagefour.png",
  //     price: "$1,600",
  //     duration: "5 Days, 4 Nights",
  //     country: "Greece",
  //     flag: "🇬🇷",
  //   },
  //   {
  //     id: 5,
  //     title: "New York City Lights",
  //     image: "/assets/tours/imageone.png",
  //     price: "$1,700",
  //     duration: "4 Days, 3 Nights",
  //     country: "USA",
  //     flag: "🇺🇸",
  //   },
  //   {
  //     id: 6,
  //     title: "Sydney Coastal Tour",
  //     image: "/assets/tours/imagetwo.png",
  //     price: "$2,000",
  //     duration: "6 Days, 5 Nights",
  //     country: "Australia",
  //     flag: "🇦🇺",
  //   },
  //   {
  //     id: 7,
  //     title: "Dubai Luxury Experience",
  //     image: "/assets/tours/imagethree.png",
  //     price: "$2,500",
  //     duration: "5 Days, 4 Nights",
  //     country: "UAE",
  //     flag: "🇦🇪",
  //   },
  //   {
  //     id: 8,
  //     title: "Bali Island Escape",
  //     image: "/assets/tours/imagefour.png",
  //     price: "$1,400",
  //     duration: "6 Days, 5 Nights",
  //     country: "Indonesia",
  //     flag: "🇮🇩",
  //   },
  // ];

  // Function to fetch tours via Axios
  const fetchTours = async () => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/packages/website/featured`); // API call using Axios
      const filtered_response = response?.data?.filter((ele:any)=>ele.status.toLowerCase() == "active");
      setTours(filtered_response);
      setError(null); // Clear error if successful
    } catch (err) {
      setError("Error loading tours. Please try again later.");
      alert("three")
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTours(); // Fetch data on mount
  }, []);
  

  if (!tours.length) return <div></div>; // Show loading state
  if (error) return <div className="text-red-500">{error}</div>;



  // return <PopularTours tours={tours} heading_test={heading_test} title_test={title_test}/>;
  return <PopularTours tours={tours} loading={loading} error={error || ""}/>;
};

export default ParentComponent;