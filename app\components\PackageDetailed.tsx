"use client";

import { useState, useEffect, useRef } from 'react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import Image from 'next/image';
import axios from 'axios';
import { useRouter } from 'next/navigation';
import crypto from 'crypto';
import InclusionsExclusions from './package-details/InclusionsExclusions';
import ActivityDetails from './package-details/ActivityDetails';
import BookingCard from './package-details/BookingCard';
import DestinationMap from './package-details/DestinationMap';
import HotelDetails from './package-details/HotelDetails';
import ImageCarousel from './package-details/ImageCarousel';
import ItinerarySection from './package-details/ItinerarySection';

import NavigationSidebar from './package-details/NavigationSidebar';
import TransferDetails from './package-details/TransferDetails';
import TripHighlights from './package-details/TripHighlights';

const NEXT_PUBLIC_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

interface PackageDetails {
  packageCode: string;
  packageTitle: string;
  shortDescription: string;
  description: string;
  packageMainImage: string;
  packageType: string;
  packageTheme: string;
  otherTags: string;
  destinationId: number;
  noOfAdults:number;
  continent: string;
  region: string;
  country: string;
  state: string;
  city: string;
  bestDeal: boolean;
  featuredTour: boolean;
  flightIncluded: boolean;
  hotelIncluded: boolean;
  activityIncluded: boolean;
  transferIncluded: boolean;
  builtFrom: string;
  customerCanCustomize: boolean;
  status: string;
  publishForCustomizedTours: string;
  noOfDays: number;
  noOfNights: number;
  parentPackageCode: string;
  price: number;
  days: Array<{
    dayId: number;
    day_number: number;
    cityId: number | null;
    itineraryDate: string | null;
    cities: Array<{
      id: number;
      dayId: number;
      cityId: number;
      title: string;
      description: string;
      itineraries: Array<{
        itineraryId: number;
        sortNo: number;
        code: string;
        subCode: string;
        subCodeDescriptor: string;
        excursionId: string;
        excursionType: string;
        price: string;
        noOfNightsBooked: number;
        desc: string;
        customerNote: string | null;
        agentNote: string | null;
        adminNote: string | null;
        itineraryCityId: number;
        selected_excursion: {
          id: string;
          title?: string;
          cityId: number;
          status: string;
          cityName: string;
          airportId?: number;
          airportName?: string;
          destinationId: number;
          destinationName: string;
          titleDescriptor?: string;
          transportDetails?: Array<{
            status: string;
            carType: string;
            partner: string;
            currency: string;
            finalPrice: string;
            pickupTime: string;
            actualPrice: number;
            description: string;
            meetingPoint: string;
            transferFrom: string;
            transferMode: string;
            companyMarkup: number;
            partnerNumber: string;
            companyMarkupType: string;
            pickupTimeDisplayIn: string[];
            meetingPointDisplayIn: string[];
            meetingPointGoogleLink: string;
            partnerNumberDisplayIn: string[];
          }>;
          images?: Array<{
            filePath: string;
            DisplayIn?: string[];
          }>;
          currency?: string;
          adminNote?: string;
          agentNote?: string;
          hotelCode?: string;
          hotelDesc?: string;
          hotelName?: string;
          inclusions?: string;
          starRating?: number | string;
          roomDetails?: Array<{
            id: string;
            images?: Array<{ filePath: string }>;
            status: string;
            roomType: string;
            finalPrice: string;
            actualPrice: number;
            companyMarkup: number;
            companyMarkupType: string;
          }>;
          customerNote?: string;
          hotelAddress?: string;
          subAdminNote?: string;
          contactNumber?: string;
          locationRating?: number | string;
          externalRatings?: Array<{
            link?: string;
            name: string;
            rank: number;
            rankError: boolean;
          }>;
          hotelLocationLatitude?: number | string;
          publicTransportNearBy?: string;
          contactNumberDisplayIn?: string[];
          hotelLocationLongitude?: string;
          publicTransportDisplayIn?: string[];
          publicTransportLocationLatitude?: string;
          publicTransportLocationDisplayIn?: string[];
          publicTransportLocationLongitude?: string;
          others?: string;
          startTime?: string;
          description?: string;
          youTubeLink?: string;
          meetingPoint?: string;
          howToGetThere?: string;
          partnerNumber?: string;
          packageDetails?: Array<{
            id: string;
            status: string;
            partner: string;
            currency: string;
            tourType: string;
            adminNote: string;
            agentNote: string;
            finalPrice: string;
            inclusions: string;
            actualPrice: number;
            description: string;
            packageName: string;
            customerNote: string;
            subAdminNote: string;
            tourDuration: string;
            companyMarkup: number;
            companyMarkupType: string;
          }>;
          othersDisplayIn?: string[];
          sightseeingCode?: string;
          sightseeingName?: string;
          startTimeShowIn?: string[];
          shortDescription?: string;
          detailsOfActivity?: string;
          redemptionOfVoucher?: string;
          youTubeLinkDisplayIn?: string[];
          meetingPointDisplayIn?: string[];
          howToGetThereDisplayIn?: string[];
          partnerNumberDisplayIn?: string[];
          shortDescriptionDisplayIn?: string[];
          detailsOfActivityDisplayIn?: string[];
          meetingPointLocationLatitude?: number;
          redemptionOfVoucherDisplayIn?: string[];
          meetingPointLocationLongitude?: number;
          destinationCityId?: number;
          destinationCityName?: string;
        };
      }>;
    }>;
  }>;
  inclusions: Array<{
    id: number;
    name: string;
    desc: string;
    included: boolean;
    excluded: boolean;
  }>;
  priceSummary: {
    summaryId: number;
    actualPrice: number;
    miscCost: number;
    agentMarkup: number;
    agentDiscount: number;
    grossSellingPrice: number;
    taxGst: number;
    netSellingPrice: number;
    netCommission: number;
    netMarkup: number;
    netCost: number;
    grossMargin: number;
    taxGstMargin: number;
    taxGstPaid: number;
    netMargin: number;
  };
}

const PackageDetails = () => {
  const router = useRouter();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [expandedDays, setExpandedDays] = useState<number[]>([]);
  const [activeTab, setActiveTab] = useState('summary');
  const [expandedSections, setExpandedSections] = useState<string[]>([]);
  const [expandedDetails, setExpandedDetails] = useState<string[]>([]);
  const [packageData, setPackageData] = useState<PackageDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentError, setPaymentError] = useState('');
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [paymentProcessing, setPaymentProcessing] = useState(false);
  const [sightseeingImages, setSightseeingImages] = useState<Array<{ filePath: string; DisplayIn?: string[] }>>([]);
  const [hotelImageIndices, setHotelImageIndices] = useState<{ [key: string]: number }>({});
  const [sidebarFixed, setSidebarFixed] = useState(true);
  const mainContentRef = useRef<HTMLDivElement>(null);
  const sidebarRef = useRef<HTMLUListElement>(null);
  const sentinelRef = useRef<HTMLDivElement>(null);

  // PayU Configuration
  const PAYU_KEY = process.env.NEXT_PUBLIC_PAYU_MERCHANT_KEY;
  const PAYU_SALT = process.env.NEXT_PUBLIC_PAYU_MERCHANT_SALT;
  const PAYU_BASE_URL = process.env.NEXT_PUBLIC_PAYU_API_URL;
  const PAYMENT_SUCCESS_URL = process.env.NEXT_PUBLIC_APP_BASE_URL + "/payment-success";
  const PAYMENT_FAILURE_URL = process.env.NEXT_PUBLIC_APP_BASE_URL + "/payment-failure";

  const images = [
    '/assets/tours/imagefour.png',
    '/assets/tours/imageone.png',
    '/assets/tours/imagethree.png',
    '/assets/tours/imagetwo.png',
    '/assets/tours/imageone.png',
  ];

  // Function to collect all sightseeing images
  const collectSightseeingImages = (data: PackageDetails) => {
    const images: Array<{ filePath: string; DisplayIn?: string[] }> = [];

    data.days.forEach(day => {
      day.cities.forEach(city => {
        city.itineraries.forEach(itinerary => {
          if (itinerary.excursionType === 'sightseeing' &&
            itinerary.selected_excursion.images &&
            itinerary.selected_excursion.images.length > 0) {
            images.push(...itinerary.selected_excursion.images);
          }
        });
      });
    });

    return images;
  };

  useEffect(() => {
    const fetchPackageData = async () => {
      try {
        // Get the query parameter from the URL
        const searchParams = new URLSearchParams(window.location.search);
        const packageCode = searchParams.get('query');

        if (!packageCode) {
          throw new Error('Package code not found in URL');
        }

        const { data } = await axios.get(`${NEXT_PUBLIC_API_BASE_URL}/api/packages/website/${packageCode}`);
        setPackageData(data);
        setSightseeingImages(collectSightseeingImages(data));
      } catch (error) {
        console.error('Error fetching package data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPackageData();
  }, []);

  useEffect(() => {
    // Intersection Observer to detect when main content is out of view
    const observer = new window.IntersectionObserver(
      ([entry]) => {
        setSidebarFixed(entry.isIntersecting);
      },
      {
        root: null,
        threshold: 0,
      }
    );
    if (sentinelRef.current) {
      observer.observe(sentinelRef.current);
    }
    return () => {
      if (sentinelRef.current) {
        observer.unobserve(sentinelRef.current);
      }
    };
  }, []);

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const goToImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  const toggleDay = (dayNumber: number) => {
    setExpandedDays(prev =>
      prev.includes(dayNumber)
        ? prev.filter(d => d !== dayNumber)
        : [...prev, dayNumber]
    );
  };

  const toggleSection = (section: string) => {
    setExpandedSections(prev =>
      prev.includes(section)
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const tourTags = [
    { icon: "🏃‍♂️", text: "Adventure" },
    { icon: "🏛️", text: "Culture" },
    { icon: "💑", text: "Honeymoon" },
    { icon: "🌍", text: "Western Europe" }
  ];

  const tripHighlights = [
    {
      title: "Venice - 2 nights",
      description: "Experience the romantic canals, historic architecture, and unique culture of Venice with a 2-night stay in this enchanting city.",
      duration: "2 nights",
      image: "/assets/tours/venice-highlight.jpg"
    },
    {
      title: "Rome - 2 nights",
      description: "Explore the eternal city of Rome, visiting ancient ruins, Vatican City, and experiencing the vibrant Italian lifestyle.",
      duration: "2 nights",
      image: "/assets/tours/rome-highlight.jpg"
    },
    {
      title: "5 Days & 4 Nights",
      description: "A perfectly balanced 5-day Italian adventure covering the best of Venice and Rome, with comfortable accommodations and guided tours.",
      duration: "Total duration",
      image: "/assets/tours/italy-duration.jpg"
    },
    {
      title: "Guided Tours & Activities",
      description: "Expert-led tours of major attractions, skip-the-line access, and authentic local experiences including cooking classes and gondola rides.",
      duration: "Throughout the trip",
      image: "/assets/tours/guided-tours.jpg"
    }
  ];

  const itinerary = [
    {
      day: 1,
      title: "Arrival in Venice",
      description: "Arrive at Venice Marco Polo Airport. Transfer to your hotel. Evening gondola ride through Venice's romantic canals.",
      meals: {
        breakfast: false,
        lunch: false,
        dinner: true
      },
      activities: [
        "Airport pickup",
        "Hotel check-in",
        "Evening gondola ride"
      ]
    },
    {
      day: 2,
      title: "Venice Exploration",
      description: "Visit St. Mark's Basilica, Doge's Palace, and Bridge of Sighs. Afternoon cooking class to learn Italian cuisine.",
      meals: {
        breakfast: true,
        lunch: true,
        dinner: true
      },
      activities: [
        "St. Mark's Basilica tour",
        "Doge's Palace visit",
        "Italian cooking class"
      ]
    },
    {
      day: 3,
      title: "Venice to Rome",
      description: "Morning train to Rome. Check-in at hotel. Evening walking tour of Rome's historic center.",
      meals: {
        breakfast: true,
        lunch: false,
        dinner: true
      },
      activities: [
        "High-speed train journey",
        "Hotel check-in",
        "Evening walking tour"
      ]
    },
    {
      day: 4,
      title: "Rome Highlights",
      description: "Visit the Colosseum, Roman Forum, and Palatine Hill. Afternoon at Vatican Museums and Sistine Chapel.",
      meals: {
        breakfast: true,
        lunch: true,
        dinner: true
      },
      activities: [
        "Colosseum guided tour",
        "Roman Forum exploration",
        "Vatican Museums visit"
      ]
    },
    {
      day: 5,
      title: "Departure",
      description: "Breakfast at hotel. Transfer to Rome Fiumicino Airport for departure.",
      meals: {
        breakfast: true,
        lunch: false,
        dinner: false
      },
      activities: [
        "Hotel checkout",
        "Airport transfer"
      ]
    }
  ];

  const inclusions = [
    "4 nights accommodation in 4-star hotels",
    "Daily breakfast and selected meals",
    "All transfers and transportation",
    "English-speaking tour guides",
    "Skip-the-line entrance tickets",
    "Cooking class in Venice",
    "Gondola ride in Venice",
    "High-speed train ticket (Venice to Rome)",
    "24/7 customer support"
  ];

  const exclusions = [
    "International flights",
    "Travel insurance",
    "Personal expenses",
    "Additional meals and beverages",
    "City tourist tax (to be paid locally)",
    "Optional activities not mentioned in inclusions"
  ];

  const tabs = [
    { id: 'summary', label: 'Summary' },
    { id: 'flights', label: 'Flights' },
    { id: 'hotels', label: 'Hotels' },
    { id: 'transfers', label: 'Transfers' },
    { id: 'activities', label: 'Activities' }
  ];

  // Add new data structures for hotel details
  const hotelDetails = {
    name: "Hotel Campiello",
    rating: 4,
    location: "City Centre, Venice, Italy",
    nights: "2 Nights",
    roomType: "Deluxe Double Room",
    amenities: ["Free WiFi", "Air conditioning", "Tea/Coffee maker in the room", "24- hour front desk", "Lift"],
    contact: {
      phone: "+33144677575",
      email: "<EMAIL>"
    },
    description: "In the city center, Hotel Campiello is in Venice's Castello neighborhood, an area with good shopping. Mark's Square and Bridge of Sighs are notable landmarks, and the area's natural beauty can be seen Grand Canal and Giardini."
  };

  const generateHash = (data: any) => {
    // Hash format: sha512(key|txnid|amount|productinfo|firstname|email|udf1|udf2|udf3|udf4|udf5||||||SALT)
    const hashString = `${PAYU_KEY}|${data.txnid}|${data.amount}|${data.productinfo}|${data.firstname}|${data.email}|${data.udf1 || ''}|${data.udf2 || ''}|${data.udf3 || ''}|${data.udf4 || ''}|${data.udf5 || ''}||||||${PAYU_SALT}`;
    return crypto.createHash('sha512').update(hashString).digest('hex');
  };

  const initiatePayment = async () => {
    try {
      setPaymentProcessing(true);

      // Generate unique transaction ID with timestamp and random string
      const txnid = `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Payment data
      const paymentData = {
        key: PAYU_KEY,
        txnid: txnid,
        amount: packageData?.priceSummary.netSellingPrice.toString() || "0",
        productinfo: `${packageData?.packageCode} - European Tour Package`,
        firstname: localStorage.getItem('userName') || 'Guest',
        email: localStorage.getItem('userEmail') || '<EMAIL>',
        phone: localStorage.getItem('userMobile') || '',
        surl: PAYMENT_SUCCESS_URL,
        furl: PAYMENT_FAILURE_URL,
        udf1: '',
        udf2: '',
        udf3: '',
        udf4: '',
        udf5: 'BOLT_KIT_NODE_JS',
        hash: ''
      };

      // Generate hash
      paymentData.hash = generateHash(paymentData);

      // Create form and submit
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = PAYU_BASE_URL || "";

      // Add all fields to the form
      Object.entries(paymentData).forEach(([key, value]) => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value?.toString() || "";
        form.appendChild(input);
      });

      document.body.appendChild(form);
      form.submit();
      document.body.removeChild(form);

    } catch (error: any) {
      setPaymentError(error.message || 'Error initiating payment');
      setPaymentProcessing(false);
    }
  };

  const handleBookNow = () => {
    // Redirect to traveller page
    router.push('package-details/travellers');
  };

  const handleHotelImageNavigation = (dayId: number, cityIndex: number, itineraryIndex: number, direction: 'prev' | 'next') => {
    const key = `${dayId}-${cityIndex}-${itineraryIndex}`;
    const currentIndex = hotelImageIndices[key] || 0;
    const images = packageData?.days[dayId]?.cities[cityIndex]?.itineraries[itineraryIndex]?.selected_excursion?.images || [];

    if (images.length > 0) {
      const newIndex = direction === 'next'
        ? (currentIndex + 1) % images.length
        : (currentIndex - 1 + images.length) % images.length;

      setHotelImageIndices(prev => ({
        ...prev,
        [key]: newIndex
      }));
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
      <Skeleton height={30} count={5} />
    </div>
    );
  }

  if (!packageData) {
    return <div className="flex justify-center items-center min-h-screen">Error loading package data</div>;
  }

  return (
    <div className="flex">
      {/* Left Navigation Sidebar OUTSIDE main content */}
      <NavigationSidebar />
      {/* Main Content Area */}
      <div className="flex-1 bg-white" ref={mainContentRef}>
        <div className="max-w-7xl mx-auto py-2 sm:py-2 lg:py-2">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6 sm:space-y-8">
              {/* Image Carousel */}
              <div className="relative">
                <div className="relative w-full h-[250px] sm:h-[350px] md:h-[450px] lg:h-[500px] rounded-3xl overflow-hidden">
                  {sightseeingImages.length > 0 ? (
                    <Image
                      src={sightseeingImages[currentImageIndex].filePath}
                      alt={`Sightseeing image ${currentImageIndex + 1}`}
                      fill
                      className="object-cover"
                      priority
                    />
                  ) : (
                    <Image
                      src={packageData?.packageMainImage || '/assets/tours/default-image.jpg'}
                      alt={packageData?.packageTitle || 'Tour package'}
                      fill
                      className="object-cover"
                      priority
                    />
                  )}
                  <button
                    onClick={prevImage}
                    className="absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-1.5 sm:p-2 lg:p-3 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-gray-900">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                    </svg>
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-1.5 sm:p-2 lg:p-3 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-gray-900">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                    </svg>
                  </button>
                  <div className="absolute bottom-4 left-0 right-0">
                    <div className="flex justify-center space-x-1.5 sm:space-x-2">
                      {(sightseeingImages.length > 0 ? sightseeingImages : [packageData?.packageMainImage]).map((_, index) => (
                        <button
                          key={index}
                          onClick={() => goToImage(index)}
                          className={`w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full transition-colors ${currentImageIndex === index ? 'bg-white' : 'bg-white/50 hover:bg-white/80'
                            }`}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Title Section */}
              <div className="space-y-4">
                <p className="text-3xl sm:text-2xl font-bold text-[#1E1E1E]">
                  {packageData.packageTitle}
                </p>
                <div className="flex flex-wrap gap-8">
                  {packageData.inclusions
                    .filter(
                      (inc) =>
                        inc.included &&
                        ['hotel', 'sightseeing', 'intercity'].includes(inc.name.toLowerCase())
                    )
                    .map((item, index) => (
                      <div key={index} className="flex items-center gap-4">
                        <div className="text-blue-600 w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6">
                          {item.name.toLowerCase() === 'hotel' ? (

                            <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M10.7563 4.66669C10.6601 4.66637 10.5678 4.70419 10.4996 4.77208C10.4314 4.8398 10.3929 4.93194 10.3928 5.02812V6.70126H9.01382C8.81388 6.70207 8.6524 6.86484 8.65321 7.06479V11.4179H3.86062C3.66067 11.4187 3.49919 11.5815 3.5 11.7815V23.4079C3.50049 23.607 3.66148 23.7685 3.86062 23.7693H24.1366H24.1365C24.2326 23.7696 24.3249 23.7318 24.3932 23.6639C24.4614 23.5962 24.4998 23.5041 24.5 23.4079V11.7815C24.5003 11.685 24.4622 11.5923 24.394 11.524C24.3258 11.4557 24.233 11.4176 24.1365 11.4179H19.3468V7.06479C19.3473 6.96829 19.309 6.87551 19.2407 6.80729C19.1725 6.73908 19.0799 6.70094 18.9834 6.70126H17.6036V5.02812C17.6032 4.82898 17.4422 4.6675 17.243 4.66669H10.7563ZM11.1175 5.39149H16.8826V6.70124H11.1175V5.39149ZM9.37733 7.42539H18.6219V23.0474H16.1313V19.2252C16.1305 19.0252 15.9678 18.8637 15.7677 18.8645H12.2321C12.0319 18.8637 11.8693 19.0252 11.8685 19.2252V23.0474H9.37718L9.37733 7.42539ZM10.9224 9.21878C10.8259 9.21846 10.7333 9.2566 10.6649 9.32481C10.5967 9.39302 10.5586 9.48581 10.5589 9.58231V11.5892C10.5591 11.6854 10.5975 11.7775 10.6658 11.8453C10.734 11.913 10.8263 11.951 10.9224 11.9506H13.0207C13.1167 11.951 13.2091 11.913 13.2774 11.8453C13.3454 11.7775 13.3839 11.6854 13.384 11.5892V9.58231C13.3845 9.48581 13.3462 9.39303 13.278 9.32481C13.2098 9.2566 13.1172 9.21846 13.0207 9.21878H10.9224ZM14.9767 9.21878C14.7767 9.21959 14.6153 9.38236 14.6161 9.58231V11.5892C14.6164 11.7884 14.7776 11.9498 14.9767 11.9506H17.0777C17.2768 11.9498 17.438 11.7884 17.4383 11.5892V9.58231C17.4391 9.38236 17.2776 9.21959 17.0777 9.21878H14.9767ZM11.2831 9.94292H12.6593V11.2257H11.2831V9.94292ZM15.34 9.94292H16.7141V11.2257H15.3402L15.34 9.94292ZM4.22424 12.143H8.65351V23.0474H4.22424V12.143ZM19.3468 12.143H23.7753V23.0474H19.3468V12.143ZM10.9224 13.8266C10.8259 13.8263 10.7332 13.8645 10.6649 13.9328C10.5967 14.0011 10.5585 14.0937 10.5588 14.1902V16.1971C10.5596 16.3972 10.7224 16.5585 10.9224 16.5579H13.0206C13.2205 16.5585 13.3831 16.3972 13.384 16.1971V14.1902C13.3844 14.0937 13.3461 14.0011 13.2779 13.9328C13.2097 13.8645 13.1171 13.8263 13.0206 13.8266H10.9224ZM14.9766 13.8266C14.7767 13.8275 14.6152 13.9902 14.616 14.1902V16.1971C14.6168 16.3961 14.7778 16.5571 14.9766 16.5579H17.0776C17.2764 16.5571 17.4374 16.3961 17.4382 16.1971V14.1902C17.439 13.9902 17.2775 13.8275 17.0776 13.8266H14.9766ZM5.38936 14.2114C5.29287 14.211 5.20025 14.2492 5.13187 14.3175C5.06366 14.3858 5.02551 14.4784 5.02584 14.5749V16.5818C5.026 16.678 5.06447 16.7701 5.13268 16.838C5.20089 16.9057 5.29318 16.9435 5.38936 16.9432H7.4876C7.68674 16.9424 7.84773 16.7809 7.84821 16.5818V14.5749C7.84902 14.3749 7.68754 14.2122 7.4876 14.2114H5.38936ZM20.512 14.2114C20.4155 14.211 20.3228 14.2492 20.2546 14.3175C20.1864 14.3858 20.1481 14.4784 20.1486 14.5749V16.5818C20.1488 16.678 20.1872 16.7701 20.2553 16.838C20.3235 16.9057 20.4159 16.9435 20.512 16.9432H22.6102C22.7064 16.9435 22.7987 16.9057 22.8669 16.838C22.9351 16.7701 22.9736 16.678 22.9737 16.5818V14.5749C22.974 14.4784 22.9359 14.3858 22.8677 14.3175C22.7993 14.2492 22.7067 14.211 22.6102 14.2114H20.512ZM11.2826 14.5508H12.6589V15.8337H11.2826V14.5508ZM15.3396 14.5508H16.7137V15.8337H15.3398L15.3396 14.5508ZM5.7499 14.9355H7.124V16.2184H5.7499V14.9355ZM20.8725 14.9355H22.2487V16.2184H20.8725V14.9355ZM5.38907 18.2444H5.38924C5.29274 18.2441 5.20012 18.2822 5.13174 18.3505C5.06353 18.4187 5.02538 18.5114 5.02571 18.6079V20.6157V20.6155C5.02652 20.8156 5.18929 20.9769 5.38924 20.9763H7.48747C7.68629 20.9755 7.84728 20.8145 7.84809 20.6155V18.6079C7.8489 18.408 7.68742 18.2452 7.48747 18.2444L5.38907 18.2444ZM20.5117 18.2444H20.5118C20.4153 18.2441 20.3227 18.2822 20.2545 18.3505C20.1863 18.4187 20.148 18.5114 20.1485 18.6079V20.6157V20.6155C20.1493 20.8156 20.3119 20.9769 20.5118 20.9763H22.6101C22.81 20.9769 22.9728 20.8156 22.9736 20.6155V18.6079C22.9739 18.5115 22.9358 18.4187 22.8676 18.3505C22.7992 18.2822 22.7066 18.2441 22.6101 18.2444L20.5117 18.2444ZM5.74949 18.9692H7.12358V20.252L5.74949 20.2522V18.9692ZM20.8721 18.9692H22.2483V20.252L20.8723 20.2522L20.8721 18.9692ZM12.592 19.5888H15.4065V23.0476H12.592V19.5888Z" fill="#175CD3" />
                            </svg>

                          ) : item.name.toLowerCase() === 'sightseeing' ? (

                            <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M6.4188 17.8267C6.54724 17.8252 6.66474 17.7537 6.72537 17.6405C6.78584 17.5272 6.7799 17.3898 6.70974 17.2822C6.53333 17.018 2.45294 10.7598 7.75426 6.03258C8.80566 5.29102 10.0607 4.89274 11.3474 4.89242C12.6339 4.89196 13.8893 5.28961 14.9411 6.03054C14.9743 6.05617 18.2279 8.60866 17.5743 11.8181H17.5744C17.5358 12.0074 17.6582 12.1921 17.8475 12.2306C18.0368 12.2691 18.2214 12.1469 18.26 11.9575C19.0017 8.31642 15.5167 5.5899 15.3517 5.4639C11.1781 2.62702 7.36934 5.44546 7.31014 5.49218C1.52854 10.6458 6.08138 17.6018 6.12794 17.6714C6.19279 17.7684 6.30201 17.8265 6.41872 17.8265L6.4188 17.8267Z" fill="#175CD3" />
                              <path d="M16.3859 18.9899C15.8394 18.7311 14.3486 18.2635 13.9331 17.8296V17.8294C13.5625 17.4813 13.2981 17.0353 13.1705 16.543C13.9031 16.2647 14.5341 15.7704 14.9797 15.1255C15.4252 14.4807 15.6645 13.7157 15.6658 12.9317V10.1431C15.6651 9.98955 15.565 9.85392 15.4183 9.80845C14.707 9.56721 14.1511 9.00485 13.9181 8.29093C13.8864 8.17984 13.8019 8.09171 13.6923 8.05515C13.5828 8.01859 13.4623 8.03828 13.3703 8.10797C13.3395 8.13125 10.2911 10.4045 7.4399 9.65453L7.43975 9.65468C7.3349 9.62796 7.2235 9.65093 7.13787 9.71703C7.05209 9.78296 7.00146 9.88484 7.00053 9.99296V12.9317C7.00193 13.7211 7.24459 14.4911 7.69601 15.1386C8.14741 15.7861 8.78617 16.28 9.52633 16.5542L9.40679 16.965C9.25851 17.4542 8.89007 17.8461 8.41087 18.0242L6.32007 18.8189C5.86023 18.9977 5.46459 19.3106 5.18507 19.7172C4.90538 20.1238 4.75443 20.605 4.75195 21.0983V23.3559C4.75555 23.5465 4.91117 23.6992 5.10195 23.6992C5.29258 23.6992 5.4482 23.5465 5.45195 23.3559V21.0983C5.45383 20.7466 5.56148 20.4033 5.76086 20.1134C5.96039 19.8236 6.24226 19.6003 6.57022 19.4728L8.43102 18.7653L10.1981 21.3342C9.8715 21.2975 9.54462 21.4008 9.29838 21.6184C9.05213 21.8361 8.90963 22.1478 8.90619 22.4765V23.3542C8.90619 23.5475 9.06276 23.7042 9.25619 23.7042C9.44947 23.7042 9.60619 23.5475 9.60619 23.3542V22.4764C9.60635 22.2323 9.80416 22.0343 10.0484 22.0342H12.6608C12.905 22.0343 13.103 22.2323 13.1031 22.4764V23.3542C13.1031 23.5475 13.2598 23.7042 13.4531 23.7042C13.6464 23.7042 13.8031 23.5475 13.8031 23.3542V22.4764C13.8023 22.1862 13.6909 21.9073 13.4917 21.6964C13.2923 21.4854 13.0201 21.3584 12.7305 21.3412L13.7444 19.8231C13.8472 19.6623 13.8025 19.4489 13.6438 19.3429C13.4852 19.237 13.2709 19.2775 13.1619 19.4342L11.8931 21.3342H11.0478L9.28984 18.7784C10.5683 19.6654 12.313 19.8386 13.7114 18.5484C14.1278 18.8887 15.6204 19.4122 16.1202 19.6372C16.2982 19.7073 16.4996 19.6215 16.5722 19.4445C16.6451 19.2675 16.5621 19.065 16.3862 18.9898L16.3859 18.9899ZM7.70033 12.9319V10.4272C10.1383 10.85 12.5095 9.51738 13.4139 8.92846C13.7452 9.57986 14.2943 10.0943 14.9658 10.3825V12.9321V12.9319C14.9649 13.7716 14.631 14.5766 14.0372 15.1703C13.4436 15.7641 12.6386 16.098 11.7989 16.0991H10.8672C10.0277 16.098 9.22269 15.7641 8.62909 15.1703C8.03533 14.5766 7.70145 13.7716 7.70049 12.9319L7.70033 12.9319ZM9.53097 18.0983C9.87457 17.7147 10.1066 17.2442 10.2015 16.738C10.1805 16.8216 12.5099 16.7803 12.4954 16.7328C12.6296 17.2239 12.868 17.6805 13.1943 18.0714C11.8162 19.2317 10.496 18.8028 9.53101 18.0983L9.53097 18.0983Z" fill="#175CD3" />
                              <path d="M23.1907 17.0771L19.4935 14.8749C18.9725 14.589 18.3774 14.4673 17.7859 14.5257V14.1469C17.7859 13.9537 17.6292 13.7969 17.4359 13.7969C17.2425 13.7969 17.0859 13.9537 17.0859 14.1469V23.4401C17.0897 23.6308 17.2453 23.7833 17.4359 23.7833C17.6266 23.7833 17.782 23.6308 17.7859 23.4401V15.2257C18.2511 15.1728 18.7216 15.2604 19.1367 15.4773L22.8322 17.6784C22.9157 17.7303 22.9608 17.8263 22.9474 17.9235C22.9341 18.0209 22.8646 18.1012 22.7703 18.1285L18.931 19.161V19.1612C18.7443 19.2113 18.6336 19.4034 18.6838 19.5901C18.7339 19.7768 18.9261 19.8874 19.1127 19.8373L22.9515 18.8048V18.8046C23.1898 18.7367 23.3921 18.5786 23.5157 18.3638C23.6394 18.1493 23.6746 17.8949 23.614 17.6546C23.5533 17.4145 23.4014 17.2074 23.1907 17.0771Z" fill="#175CD3" />
                            </svg>

                          ) : item.name.toLowerCase() === 'intercity' ? (

                            <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M20.9987 19.8333H24.4987C24.8081 19.8333 25.1049 19.7104 25.3237 19.4916C25.5425 19.2728 25.6654 18.9761 25.6654 18.6666V14.7361C25.6654 14.5011 25.5945 14.2716 25.4619 14.0776C25.3293 13.8836 25.1412 13.7341 24.9222 13.6488L19.832 11.6666L16.332 5.83331H6.9987L4.66536 11.6666H3.4987C3.18928 11.6666 2.89253 11.7896 2.67374 12.0084C2.45495 12.2271 2.33203 12.5239 2.33203 12.8333V18.6666C2.33203 18.9761 2.45495 19.2728 2.67374 19.4916C2.89253 19.7104 3.18928 19.8333 3.4987 19.8333H6.9987" stroke="#175CD3" stroke-linecap="round" stroke-linejoin="round" />
                              <path d="M18.6654 22.1667C19.2842 22.1667 19.8777 21.9208 20.3153 21.4832C20.7529 21.0457 20.9987 20.4522 20.9987 19.8333C20.9987 19.2145 20.7529 18.621 20.3153 18.1834C19.8777 17.7458 19.2842 17.5 18.6654 17.5C18.0465 17.5 17.453 17.7458 17.0154 18.1834C16.5779 18.621 16.332 19.2145 16.332 19.8333C16.332 20.4522 16.5779 21.0457 17.0154 21.4832C17.453 21.9208 18.0465 22.1667 18.6654 22.1667Z" stroke="#175CD3" stroke-linecap="round" stroke-linejoin="round" />
                              <path d="M9.33333 22.1667C8.71449 22.1667 8.121 21.9208 7.68342 21.4832C7.24583 21.0457 7 20.4522 7 19.8333C7 19.2145 7.24583 18.621 7.68342 18.1834C8.121 17.7458 8.71449 17.5 9.33333 17.5C9.95217 17.5 10.5457 17.7458 10.9832 18.1834C11.4208 18.621 11.6667 19.2145 11.6667 19.8333C11.6667 20.4522 11.4208 21.0457 10.9832 21.4832C10.5457 21.9208 9.95217 22.1667 9.33333 22.1667Z" stroke="#175CD3" stroke-linecap="round" stroke-linejoin="round" />
                              <path d="M16.3346 19.8333H11.668" stroke="#175CD3" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>

                          ) : null}
                        </div>
                        <span className="text-sm mt-1 font-bold text-black capitalize">
                          {item.name}
                        </span>
                      </div>
                    ))}

                </div>
                <div className="flex flex-wrap gap-2 mt-32">
                  <span className="px-2.5 sm:px-3 py-1 sm:py-1.5 bg-[#F2F4F7] rounded-full text-xs sm:text-sm flex items-center gap-1 sm:gap-1.5">
                    {/* <span>
                        {packageData.packageType === 'adventure' ? '🏃‍♂️' :
                         packageData.packageType === 'culture' ? '🏛️' :
                         packageData.packageType === 'honeymoon' ? '💑' :
                         packageData.packageType === 'western' ? '🌍' : '✨'}
                      </span> */}
                    <span className="text-[#344054] text-xs capitalize font-medium">{packageData.packageType || 'Standard'}</span>
                  </span>
                </div>
              </div>

              {/* Content with Sidebar */}
              <div className="flex">

                {/* Main Content Area */}
                <div className="flex-1">
                  {/* Trip Highlights */}
                  <div id="trip-highlights" className="space-y-4 mb-8">
                    <h2 className="text-xl font-bold text-black">Trip Highlights</h2>
                    <TripHighlights highlights={tripHighlights} />
                  </div>

                  {/* Destination Map */}
                  <div id="destinations" className="space-y-4 mb-8">
                    <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Destination Route</h2>
                    <DestinationMap />
                  </div>

                  {/* Itinerary Section */}
                  <div id="itinerary" className="space-y-4 mb-8">
                    <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Itinerary</h2>
                    <ItinerarySection packageData={packageData} />
                  </div>

                  {/* Inclusions & Exclusions */}
                  <div className="space-y-4 mt-6 sm:mt-8">
                    <InclusionsExclusions
                      packageData={packageData}
                      expandedSections={expandedSections}
                      toggleSection={toggleSection}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Booking Card */}
            <BookingCard packageData={packageData} handleBookNow={handleBookNow} />
          </div>
        </div>

      </div>
    </div>
  );
};

export default PackageDetails;
