"use client"

import Image from 'next/image'
import Link from 'next/link'
import React, { useEffect, useState, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import NavigationBar from '../.././components/NavBar';
import Breadcrumb from '../../components/BreadCrumbs';
import DestinationTable from '@/app/components/DestinationTours';
import ParentComponent from '@/app/components/PopularToursParents';
import TestimonialSection from '../../components/Testimonials';
import CallToAction from '../../components/ContactSection';
import Footer from '../.././components/Footer';
import axios from 'axios';
import { useQueryStore } from '@/app/store/queryStore'

interface SearchResult {
  id: number;
  packageName: string;
  packageTitle?: string;
  packageMainImage?: string;
  destination: string;
  duration: string;
  priceSummary: number | null;
  imageLink: string;
  tags?: string[];
  country?: string | string[];
  hotels?: number;
  transfers?: number;
  activities?: number;
  noOfDays?: number;
  noOfNights?: number;
  packageType?: string;
  packageTheme?: string;
  region?: string;
}

// New Client Component to handle search params and data fetching
const ToursContent = ({query_test,setQuery}:any) => {
  const searchParams = useSearchParams();
  const query = searchParams.get('query');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSearchResults = async () => {
      // Reset state for new searches
      setLoading(true);
      setError(null);
      setSearchResults([]);
      try {
        const NEXT_PUBLIC_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'default_api_url'; // Provide a fallback
        const response = await axios.get(
          // `${NEXT_PUBLIC_API_BASE_URL}/api/packages/website/search/destination?complete_data=true&query=${query?encodeURIComponent(query):""}`
          `${NEXT_PUBLIC_API_BASE_URL}/api/packages/website/search/destination?query=${query?encodeURIComponent(query):""}`
        );
        
        if (response.data && Array.isArray(response.data)) {
          let filtered_data = response.data.filter((ele)=>ele.status.toLowerCase() =="active")
          setSearchResults(filtered_data);
        } else {
          setSearchResults([]);
        }
      } catch (err) {
        console.error('Error fetching search results:', err);
        setError('Failed to load search results. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchSearchResults();
  }, [query]);
  useEffect(()=>{
    return ()=>{
      setQuery('');
    }
  },[])
  return (
    <>
      <div className="px-4 pb-8 pt-2">
        <h1 className="font-[Manrope]  text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 container mx-auto max-w-7xl  mb-4">
          Find your perfect dream destination
        </h1>
      </div>
      <DestinationTable 
        searchResults={searchResults}
        loading={loading}
        error={error}
        searchQuery={query} // Pass query to DestinationTable
      />
    </>
  );
};

// Main Page Component (can remain client or server)
const ToursPage = () => {
  const query = useQueryStore((state) => state.query);
  const setQuery = useQueryStore((state) => state.setQuery);
  return (
    <main>
      <NavigationBar />
      <Breadcrumb />
      {/* Wrap the content using searchParams in Suspense */}
      <Suspense fallback={<div className="container mx-auto max-w-7xl p-4 text-center"></div>}>
      <ToursContent query_test={query} setQuery={setQuery}/>
      </Suspense>
      <ParentComponent heading_test={"RECOMMENDED FOR YOU"} title_test={"We think you'll love this"}/>
      <TestimonialSection />
      <CallToAction />
      <Footer />
    </main>
  );
};

export default ToursPage;