"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface BreadcrumbItem {
  label: string;
  link: string;
}

interface BreadcrumbProps {
  nav_style_class?: string;
  breadCrumb?: BreadcrumbItem[];
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ 
  nav_style_class = "", 
  breadCrumb = [] 
}) => {
  const pathname = usePathname();
  const pathSegments = pathname.split("/").filter((segment) => segment);

  // Helper function to capitalize first letter of each word
  const capitalizeWords = (str: string) => {
    return decodeURIComponent(str).replace(/\b\w/g, (char) => char.toUpperCase());
  };

  const renderBreadcrumbItems = () => {
    if (breadCrumb.length > 0) {
      return breadCrumb.map((segment: BreadcrumbItem, index: number) => {
        const href = segment?.link;
        const isLast = index === breadCrumb.length - 1;
        
        return (
          <React.Fragment key={href || index}>
            <li className="flex items-center">
              {isLast ? (
                <span className="text-[#344054] font-semibold text-xs truncate max-w-[200px] sm:max-w-none">
                  {segment?.label}
                </span>
              ) : (
                <Link 
                  href={href} 
                  className="text-[#667085] hover:text-[#344054] text-xs font-normal truncate max-w-[150px] sm:max-w-none transition-colors"
                >
                  {segment?.label}
                </Link>
              )}
            </li>
            {!isLast && (
              <li className="text-[#667085] text-sm font-normal mx-1 flex-shrink-0">
                &gt;
              </li>
            )}
          </React.Fragment>
        );
      });
    }

    return pathSegments.map((segment, index) => {
      const href = `/${pathSegments.slice(0, index + 1).join("/")}`;
      const isLast = index === pathSegments.length - 1;

      return (
        <React.Fragment key={href}>
          <li className="flex items-center">
            {isLast ? (
              <span className="text-[#344054] font-semibold text-xs truncate max-w-[200px] sm:max-w-none">
                {capitalizeWords(segment)}
              </span>
            ) : (
              <Link 
                href={href} 
                className="text-[#667085] hover:text-[#344054] text-xs font-normal truncate max-w-[150px] sm:max-w-none transition-colors"
              >
                {capitalizeWords(segment)}
              </Link>
            )}
          </li>
          {!isLast && (
            <li className="text-[#667085] text-sm font-normal mx-1 flex-shrink-0">
              &gt;
            </li>
          )}
        </React.Fragment>
      );
    });
  };

  return (
    <nav className={`w-full max-w-7xl mx-auto text-gray-600 text-sm mb-4 mt-4 px-4 lg:px-6 ${nav_style_class}`}>
      <div className="overflow-x-auto">
        <ul className="flex items-center space-x-1 min-w-max">
          <li className="flex-shrink-0">
            <Link 
              href="/" 
              className="text-[#667085] font-normal hover:text-[#344054] text-xs transition-colors"
            >
              Home
            </Link>
          </li>
          {(pathSegments.length > 0 || breadCrumb.length > 0) && (
            <li className="text-[#667085] text-sm font-normal mx-1 flex-shrink-0">
              &gt;
            </li>
          )}
          {renderBreadcrumbItems()}
        </ul>
      </div>
    </nav>
  );
};

export default Breadcrumb;