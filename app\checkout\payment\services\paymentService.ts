import axios from 'axios';
import { PaymentInitData, PaymentResponseData } from '../types/payment.types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

/**
 * Initialize payment with the backend and get payment parameters
 */
export const initializePayment = async (
  paymentData: PaymentInitData
): Promise<PaymentResponseData> => {
  try {
    console.log('Initializing payment with data:', paymentData);

    const response = await axios.post<PaymentResponseData>(
      `${API_BASE_URL}/api/payment`,
      paymentData,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        timeout: 30000, // 30 seconds timeout
      }
    );

    if (!response.data || typeof response.data !== 'object') {
      throw new Error('Invalid response from payment gateway');
    }

    console.log('Received payment parameters:', response.data);
    return response.data;
  } catch (error) {
    console.error('Payment initialization error:', error);
    throw new Error(
      axios.isAxiosError(error)
        ? error.response?.data?.message || error.message
        : 'Failed to initialize payment'
    );
  }
};

/**
 * Submit payment form to the payment gateway
 */
export const submitPaymentForm = (
  paymentData: Record<string, any>,
  gatewayUrl: string
): void => {
  // Log payment data (masking sensitive information)
  console.log('Submitting payment with data:', JSON.stringify({
    ...paymentData,
    ...(paymentData.ccnum && { ccnum: '•••• •••• •••• ' + paymentData.ccnum.slice(-4) }),
    ...(paymentData.ccvv && { ccvv: '•••' }),
    hash: paymentData.hash ? '•••' + paymentData.hash.slice(-8) : 'not generated',
  }, null, 2));

  // Create form
  const form = document.createElement('form');
  form.method = 'POST';
  form.action = gatewayUrl;
  form.target = '_self';
  form.style.display = 'none';

  // Add all fields to the form
  Object.entries(paymentData).forEach(([key, value]) => {
    if (value != null) {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = key;
      input.value = value.toString();
      form.appendChild(input);
    }
  });

  // Submit the form
  document.body.appendChild(form);
  console.log('Submitting form to:', form.action);
  form.submit();
};

/**
 * Fetch user details from the API
 */
export const fetchUserDetails = async (): Promise<{
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  customer_id: number;
}> => {
  try {
    const userId = localStorage.getItem('userId');
    if (!userId) {
      throw new Error('User ID not found');
    }

    const response = await axios.get(`${API_BASE_URL}/api/users/${userId}`);
    console.log('User details response:', response.data);

    return {
      customer_name: response.data?.name || 'Customer',
      customer_email: response.data?.email || '<EMAIL>',
      customer_phone: response.data?.phone || '9999999999',
      customer_id: response.data?.id || 0,
    };
  } catch (error) {
    console.error('Failed to fetch user details:', error);
    throw new Error('Failed to fetch user details');
  }
};
