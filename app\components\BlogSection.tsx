"use client";
import React from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { useRouter } from 'next/navigation';

const blogs = [
  {
    image: "/assets/destination/imageone.png",
    title: "Understanding AI and its Impact",
    description: "Discover how AI is transforming industries and reshaping our world.",
    link: "#",
  },
  {
    image: "/assets/destination/imagetwo.png",
    title: "Top 10 UX Trends for 2024",
    description: "Stay ahead in design with these innovative UX trends and best practices.",
    link: "#",
  },
  {
    image: "/assets/destination/imagethree.png",
    title: "The Future of Remote Work",
    description: "Explore how remote work is evolving and what it means for businesses.",
    link: "#",
  },
  {
    image: "/assets/destination/imagefour.png",
    title: "Mastering Web Performance",
    description: "Learn how to optimize your website for speed and user experience.",
    link: "#",
  },
  {
    image: "/assets/destination/imagefive.png",
    title: "Cybersecurity in 2024",
    description: "Find out the latest trends and how to keep your data secure online.",
    link: "#",
  },
];

export const PrevArrow = (props: any) => {
  const { onClick } = props;
  return (
    <div
  className="absolute left-[-25px] bg-white border border-blue-600 p-3 rounded-full cursor-pointer z-20 text-blue-600 hover:bg-blue-600 hover:text-white transition-all duration-300 shadow-lg"
  onClick={onClick}
  style={{ 
    zIndex: 999, 
    top: '30%', // Adjust this percentage to target image center
    transform: 'translateY(-50%)'
  }}
>
  <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
  </svg>
</div>
  );
};

export const NextArrow = (props: any) => {
  const { onClick } = props;
  return (
    <div
      className="absolute right-[-25px] top-1/2 transform -translate-y-1/2 bg-white border border-blue-600 p-3 rounded-full cursor-pointer z-20 text-blue-600 hover:bg-blue-600 hover:text-white transition-all duration-300 shadow-lg"
      onClick={onClick}
      style={{ 
        zIndex: 999, 
        top: '30%', // Adjust this percentage to target image center
        transform: 'translateY(-50%)'
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
      </svg>
    </div>
  );
};

const settings = {
  infinite: true,
  speed: 500,
  slidesToShow: 4,
  slidesToScroll: 1,
  autoplay: true,
  autoplaySpeed: 3000,
  arrows: true,
  variableWidth: false,
  
  prevArrow: <PrevArrow />,
  nextArrow: <NextArrow />,
  responsive: [
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 4,
      },
    },
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 2,
        arrows: false,
        centerMode: true,
        centerPadding: '20px',
      },
    },
    {
      breakpoint: 480,
      settings: {
        slidesToShow: 1,
        arrows: false,
        centerMode: true,
        centerPadding: '10px',
      },
    },
  ],
};

const BlogSection: React.FC = () => {
  const router = useRouter();

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 ">
      {/* Header Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div className="py-8">
          <h3 className="text-[#175CD3] text-sm sm:text-base lg:text-lg leading-relaxed font-semibold uppercase tracking-wide">Curated Articles</h3>
          <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight sm:leading-snug lg:leading-[60px] text-gray-900">Checkout our blog</h2>
        </div>
        <button 
          onClick={() => router.push('/destination')}
          className="bg-[#EFF8FF] text-[#175CD3] px-5 py-4 font-semibold rounded-full shadow-sm flex items-center hover:bg-blue-200 transition"
        >
          <span className="mr-3">See All</span>
          <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.5 12H19.5M19.5 12L12.5 5M19.5 12L12.5 19" stroke="#175CD3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>

      {/* Blog Carousel */}
      <div className="relative px-8">
        <Slider {...settings} className="blog-slider">
          {blogs.map((blog, index) => (
            <div key={index} className="px-3">
              <div className="bg-white rounded-lg overflow-hidden h-full flex flex-col mx-2">
                {/* Image container with proper aspect ratio */}
                <div className="w-full aspect-[4/3] overflow-hidden bg-gray-100" style={{ borderRadius: '1.5rem' }}>
                  <img 
                    src={blog.image} 
                    alt={blog.title} 
                    className="w-full h-full object-cover object-center"
                  />
                </div>
                
                <div className="p-4 flex flex-col flex-1">
                  <h4 className="text-base font-semibold text-black">{blog.title}</h4>
                  
                  <p
                    className="text-gray-600 text-xs mt-2 overflow-hidden"
                    style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      height: '32px'
                    }}
                  >
                    {blog.description}
                  </p>

                  <div className="pt-4">
                    <a href={blog.link} className="text-blue-600 font-semibold flex items-center text-xs">
                      Read More
                      <span className="ml-1">
                        <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M5.5 12H19.5M19.5 12L12.5 5M19.5 12L12.5 19" stroke="#175CD3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </Slider>
      </div>
    </div>
  );
};

export default BlogSection;