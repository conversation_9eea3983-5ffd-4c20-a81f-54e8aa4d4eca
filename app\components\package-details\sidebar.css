/* Sticky sidebar styles */
html {
  scroll-behavior: smooth;
}

/* Fixed booking card styles */
.fixed-booking-card {
  position: fixed;
  width: 350px;
  top: 120px;
  right: calc((100% - 1200px) / 2 + 20px);
  z-index: 20;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  transform: translateZ(0);
  /* Force GPU acceleration */
  backface-visibility: hidden;
  /* Prevent flickering */
}

/* When recommendation section is reached, fix the booking card at its current position */
.fixed-booking-card.stop-floating {
  position: absolute;
  /* The top value will be set dynamically in JavaScript */
}

/* Fixed sidebar styles */
.fixed-sidebar {
  position: fixed;
  /* Use fixed position so it's not affected by other elements */
  width: 150px;
  /* Slightly wider for better visibility */
  top: 120px;
  /* Fixed distance from the top */
  left: 0;
  /* Attach to the left edge of the browser window */
  z-index: 10;
  max-height: calc(100vh - 140px);
  overflow-y: auto;
  background-color: transparent;
  /* Transparent background */
  padding: 15px 15px 15px 20px;
  /* Increased padding for better spacing */
  transform: translateZ(0);
  /* Force GPU acceleration */
  backface-visibility: hidden;
  /* Prevent flickering */
  will-change: transform;
  /* Optimize for animations */
  transition: none !important;
  /* Disable transitions to prevent flickering */
}

/* When recommendation section is reached, fix the sidebar at its current position */
.fixed-sidebar.stop-floating {
  position: absolute;
  /* The top value will be set dynamically in JavaScript */
}

/* Navigation sidebar styles */
.navigation-sidebar {
  background-color: transparent;
  width: 100%;
}

.navigation-sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* Desktop navigation active item styles */
.navigation-sidebar a.active {
  color: #175CD3;
  font-weight: 600;
}

/* Mobile navigation styles */
.mobile-nav ul {
  display: flex;
  flex-wrap: nowrap;
  gap: 1.5rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
  white-space: nowrap;
  scrollbar-width: none;
  /* Firefox */
}

.mobile-nav ul::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Edge */
}

.mobile-nav li {
  flex: 0 0 auto;
}

.mobile-nav a.active {
  color: #175CD3;
  font-weight: 600;
}

/* Recommendation section styles */
#recommendations {
  position: relative;
  scroll-margin-top: 80px;
  /* Adjust based on your header height */
}

.recommendation-visible {
  position: relative;
}

/* Smooth transition when reaching recommendations */
.recommendation-visible::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.1));
  pointer-events: none;
}