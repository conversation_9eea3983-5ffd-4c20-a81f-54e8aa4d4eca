import Image from 'next/image'
import Link from 'next/link'
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { GetServerSideProps } from 'next';
import NavigationBar from '.././components/NavBar'
import SignUp from '../components/Signup';
import Footer from '.././components/Footer';

const fetchSlides = async () => {
  // Simulating fetching slide data from an API or server-side logic
  const slides = [
    { id: 1, image: '/assets/HeroBannerOne.png' },
    { id: 2, image: '/assets/HeroBannerTwo.png' },
    { id: 3, image: '/assets/HeroBannerThree.png' },
    { id: 4, image: '/assets/HeroBannerFour.png' },
  ];
  return slides;
};


const Home = async () => {
  // Fetching data asynchronously (server-side)
  const slides = await fetchSlides();

  return (
    <main>
      <NavigationBar></NavigationBar>
      <SignUp></SignUp>
      <Footer></Footer>
    </main>
  );
};


export default Home;