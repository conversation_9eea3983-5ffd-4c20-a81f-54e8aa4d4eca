"use client";  // Add this at the very top

import { useEffect, useState, useMemo } from "react";
import axios from "axios";
import NavigationBar from './components/NavBar';
import BannerCarousel from './components/BannerImage';
import SponsorSection from './components/Sponsors';
import CardCarousel from './components/TopDestinations';
import PopularTours from './components/PopularTourist';
import CuratedTours from './components/CuratedTour';
import FeaturesSection from './components/WhyChooseUs';
import ResponsiveImageSection from './components/Deal';
import TestimonialSection from './components/Testimonials';
import BlogSection from './components/BlogSection';
import CallToAction from './components/ContactSection';
import EsteemedPartners from './components/Partners';
import Footer from './components/Footer';


interface BannerSlide {
  id: number;
  image: string;
}

interface APIImage {
  id: number;
  imageLink: string;
  imageType: string;
}

interface SponsorImage {
  id: number;
  image: string;
}

interface Destination {
  id: number;
  destination: string;
  imageLink: string;
  status: string;
}

interface CardData {
  title: string;
  description: string;
  imageUrl: string;
}

const Home = () => {
  const [slides, setSlides] = useState<BannerSlide[]>([]);
  const [sponsors, setSponsors] = useState<SponsorImage[]>([]);
  const [destinations, setDestinations] = useState<CardData[]>([]);
  const [tours, setTours] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Memoize default sponsors
  const defaultSponsors = useMemo(() => [
    { id: 1, src: "/assets/sponsors/Traveloka.png", alt: "Sponsor 1" },
    { id: 2, src: "/assets/sponsors/tiket.com.png", alt: "Sponsor 2" },
    { id: 3, src: "/assets/sponsors/Booking.png", alt: "Sponsor 3" },
    { id: 4, src: "/assets/sponsors/Tripadvisor.png", alt: "Sponsor 4" },
    { id: 5, src: "/assets/sponsors/Airbnb.png", alt: "Sponsor 5" },
  ], []); // Empty dependency array since the data never changes

  useEffect(() => {
    const fetchImages = async () => {
      const NEXT_PUBLIC_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
      try {
        const response = await axios.get(`${NEXT_PUBLIC_API_BASE_URL}/api/web-images`);
        
        // Filter and transform banner images (HOME type)
        const bannerSlides = response.data
          .filter((item: APIImage) => item.imageType === 'HOME')
          .map((item: APIImage) => ({
            id: item.id,
            image: item.imageLink
          }));
        
        // Filter and transform sponsor images (PARTNER type)
        const sponsorImages = response.data
          .filter((item: APIImage) => item.imageType === 'PARTNER')
          .map((item: APIImage) => ({
            id: item.id,
            image: item.imageLink
          }));

        if (bannerSlides.length > 0) {
          // setSlides(bannerSlides);
          setSlides(bannerSlides);
        } else {
          setSlides([
            { id: 1, image: '/assets/HeroBannerOne.png' },
            { id: 2, image: '/assets/HeroBannerTwo.png' },
            { id: 3, image: '/assets/HeroBannerThree.png' },
            { id: 4, image: '/assets/HeroBannerFour.png' },
          ]);
        }

        if (sponsorImages.length > 0) {
          setSponsors(sponsorImages);
        } else {
          setSponsors(defaultSponsors.map(s => ({ id: s.id, image: s.src })));
        }
      } catch (err: any) {
        console.error("Error fetching images:", err);
        setSlides([
          { id: 1, image: '/assets/HeroBannerOne.png' },
          { id: 2, image: '/assets/HeroBannerTwo.png' },
          { id: 3, image: '/assets/HeroBannerThree.png' },
          { id: 4, image: '/assets/HeroBannerFour.png' },
        ]);
        setSponsors(defaultSponsors.map(s => ({ id: s.id, image: s.src })));
      }
    };

    const fetchDestinations = async () => {
      const NEXT_PUBLIC_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
      try {
        // Add timeout and error handling
        const response = await axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/cities/popular-destinations`, {
          timeout: 5000, // 5 second timeout
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
        
        // Filter destinations with status "FILTER" and transform the data
        const filteredDestinations = response.data
          .filter((item: Destination) => item.status === 'ACTIVE')
          .map((item: Destination) => ({
            title: item.destination,
            description: item.destination, // You can customize this if needed
            imageUrl: item.imageLink
          }));

        if (filteredDestinations.length > 0) {
          setDestinations(filteredDestinations);
        } else {
          // Fallback to default destinations if no filtered destinations found
          setDestinations([
            { title: 'Card 1', description: 'This is card 1', imageUrl: '/assets/destination/imageone.png' },
            { title: 'Card 2', description: 'This is card 2', imageUrl: '/assets/destination/imagetwo.png' },
            { title: 'Card 3', description: 'This is card 3', imageUrl: '/assets/destination/imagethree.png' },
            { title: 'Card 4', description: 'This is card 4', imageUrl: '/assets/destination/imagefour.png' },
            { title: 'Card 5', description: 'This is card 5', imageUrl: '/assets/destination/imagefive.png' },
            { title: 'Card 6', description: 'This is card 6', imageUrl: '/assets/destination/imagesix.png' },
          ]);
        }
      } catch (err: any) {
        console.error("Error fetching destinations:", err);
        setError("Error loading destinations. Please try again later.");
        // Set default destinations on error
        setDestinations([
          { title: 'Card 1', description: 'This is card 1', imageUrl: '/assets/destination/imageone.png' },
          { title: 'Card 2', description: 'This is card 2', imageUrl: '/assets/destination/imagetwo.png' },
          { title: 'Card 3', description: 'This is card 3', imageUrl: '/assets/destination/imagethree.png' },
          { title: 'Card 4', description: 'This is card 4', imageUrl: '/assets/destination/imagefour.png' },
          { title: 'Card 5', description: 'This is card 5', imageUrl: '/assets/destination/imagefive.png' },
          { title: 'Card 6', description: 'This is card 6', imageUrl: '/assets/destination/imagesix.png' },
        ]);
      } finally {
        setLoading(false);
      }
    };

    const fetchTours = async () => {
      try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/packages/website/featured`);
         const filtered_response = response?.data?.filter((ele:any)=>ele.status.toLowerCase() == "active");
        setTours(filtered_response);
      } catch (err: any) {
        setError("Error loading tours. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchImages();
    fetchDestinations();
    fetchTours();
  }, [defaultSponsors]); // Add defaultSponsors back as a dependency since it's used in the effect

  if (loading) return <p></p>;
  if (error) return <p>{error}</p>;

  return (
      <main>
        <NavigationBar />
        <BannerCarousel slides={slides} />
        <SponsorSection sponsors={sponsors} />
        <CardCarousel 
          cards={destinations} 
          loading={loading} 
          error={error || undefined}
        />
        <PopularTours tours={tours} />
        <CuratedTours />
        <FeaturesSection />
        <ResponsiveImageSection />
        <TestimonialSection />
        <BlogSection />
        <CallToAction />
        
        <EsteemedPartners />
        <Footer />
      </main>
  );
};

export default Home;
