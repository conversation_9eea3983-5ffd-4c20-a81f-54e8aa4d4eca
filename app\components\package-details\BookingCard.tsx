"use client";

import { useCurrencyStore } from '@/app/store/useCurrencyStore';
import PackageDetailPdfView from '@/app/tours/package-details/downloadpdf/PackageDetailPdfView';
import type { PackageDetails } from '@/app/types/PackageDetails';
import PDFDownloadButton from '@/app/tours/package-details/downloadpdf/PDFDownloadButton/PDFDownloadButton';
import { useRouter } from 'next/navigation';
import { use, useEffect, useState } from 'react';

interface BookingCardProps {
  packageData: PackageDetails;
  handleBookNow: () => void;
  componentRefData?: any;
  setLoadingForDownload?: any;

}

const BookingCard: React.FC<BookingCardProps> = ({ packageData, handleBookNow,componentRefData,setLoadingForDownload=null }) => {
  const router = useRouter();
  const convertFromINR = useCurrencyStore(state => state.convertFromINR);
  const symbols = useCurrencyStore(state => state.symbols);
  const selectedCurrency = useCurrencyStore(state => state.selectedCurrency);
  const [numberOfNightsInCities, setNumberOfNightsInCities] = useState({});
  const backup_packageData = packageData;
  const max_discount_rule = packageData?.applicableDiscountRules ? [...packageData?.applicableDiscountRules || []].sort((a:any,b:any)=>b?.discountPercentage-a?.discountPercentage)[0] :null;
  let price_to_show = max_discount_rule? packageData?.priceSummary?.netSellingPrice - (packageData?.priceSummary?.netSellingPrice*(max_discount_rule?.discountPercentage/100)): packageData?.priceSummary?.netSellingPrice;
  price_to_show = convertFromINR(price_to_show);
  useEffect(()=>{
  //   if(backup_packageData?.days?.length !==0){
     
  //   const nightsInCities = backup_packageData.days.reduce((acc: any, day: any) => {
  //     let city_name:any= [];
  //     day.cities.forEach((city: any) => {
  //       city.itineraries.forEach((itinerary: any) => {
  //         const cityName = itinerary.selected_excursion.cityName;
  //         if(city_name.indexOf(cityName) === -1){
  //           city_name.push(cityName);
  //         }
  //         // if (acc[cityName]) {
  //         //   acc[cityName] += 1;
  //         // } else {
  //         //   acc[cityName] = 1;
  //         // }
  //       });
  //     });
  //     city_name.forEach((cityName: any) => {
  //       if (acc[cityName]) {
  //         acc[cityName] += 1;
  //       } else {
  //         acc[cityName] = 1;
  //       }
  //     });
  //     return acc;
  //   }, {});
  //   console.log("Nights in cities:", nightsInCities);
  //   setNumberOfNightsInCities(nightsInCities);
  // }
    if(backup_packageData?.days?.length !==0){
      let last_city_name='';
    const nightsInCities = backup_packageData.days.reduce((acc: any, day: any, index) => {
      let city_name:any= [];
      day.cities.forEach((city: any,city_index:any) => {
        if(index !== backup_packageData.days.length -1 && city_index == day?.cities?.length -1){
          city.itineraries.forEach((itinerary: any,intinaryIndex:any) => {
            if(intinaryIndex == city.itineraries.length-1){
            // const cityName = itinerary.selected_excursion.cityName;
            let keys = Object.keys(itinerary.selected_excursion);
            let index_of_city_name = keys.indexOf("cityName");
            let index_of_destination_city_name = keys.indexOf("destinationCityName");
            let cityName = null;
            if(index_of_destination_city_name && (index_of_destination_city_name > index_of_city_name)){
              cityName = itinerary.selected_excursion.destinationCityName;
            } else{
              cityName = itinerary.selected_excursion.cityName;
            }
            last_city_name=cityName;
            if(city_name.indexOf(cityName) === -1){
              city_name.push(cityName);
            }
            }
          
        });
        }
      });
      if(day?.cities[`${day?.cities?.length-1}`]?.itineraries?.length ==0){
        if (acc[last_city_name]) {
            acc[last_city_name] += 1;
          } else {
            acc[last_city_name] = 1;
          }
      }
      if(index!==backup_packageData.days.length-1){
        city_name.forEach((cityName: any) => {
          if (acc[cityName]) {
            acc[cityName] += 1;
          } else {
            acc[cityName] = 1;
          }
        });
      }
      // }
      return acc;
    }, {});
    console.log("Nights in cities:", nightsInCities);
    setNumberOfNightsInCities(nightsInCities);
  }

/*
    if(backup_packageData?.days?.length !==0){
    const nightsInCities = backup_packageData.days.reduce((acc: any, day: any) => {
      let city_name:any= [];
      day.cities.forEach((city: any) => {
        city.itineraries.forEach((itinerary: any) => {
          const cityName = itinerary.selected_excursion.cityName;
          if(city_name.indexOf(cityName) === -1){
            city_name.push(cityName);
          }
          // if (acc[cityName]) {
          //   acc[cityName] += 1;
          // } else {
          //   acc[cityName] = 1;
          // }
        });
      });
      if(city_name.length>1){
        let day_per_city = 1/city_name.length;
        city_name.forEach((cityName: any) => {
          if (acc[cityName]) {
            acc[cityName] += day_per_city;
          } else {
            acc[cityName] = day_per_city;
          }
        });
      } else {
        city_name.forEach((cityName: any) => {
          if (acc[cityName]) {
            acc[cityName] += 1;
          } else {
            acc[cityName] = 1;
          }
        });
      }
      return acc;
    }, {});
    console.log("Nights in cities:", nightsInCities);
    setNumberOfNightsInCities(nightsInCities);
  }


*/

  },[])
  
  return (
    <div className="lg:sticky lg:top-6 space-y-4">
      {/* Main Booking Container */}
      <div className="bg-white rounded-2xl sm:rounded-3xl border relative p-3 sm:p-4 lg:p-6">
        {/* Add title and subtitle */}
        <div>
          <h2 className="text-base text-black">
            {/* <span className="font-bold">{Object.entries(numberOfNightsInCities).reduce((acc, [city, nights]:any) => acc + +nights, 0) + 1} </span> Days{" "} */}
            <span className="font-bold">{backup_packageData?.days?.length} </span> {backup_packageData?.days?.length ==1 ?"Day":"Days"}{" "}
            <span className="font-bold">{Object.entries(numberOfNightsInCities).reduce((acc, [city, nights]:any) => acc + +nights, 0)}</span> {Object.entries(numberOfNightsInCities).reduce((acc, [city, nights]:any) => acc + +nights, 0) >1 ? "Nights":"Night"}
          </h2>
          {/* <p className="text-xs text-[#667085] mt-1 font-semibold">
            {packageData.days[0]?.cities[0]?.itineraries[0]?.selected_excursion.cityName} - {packageData.noOfNights} nights •
            {packageData.days[3]?.cities[0]?.itineraries[0]?.selected_excursion.cityName} - {packageData.noOfNights} nights
          </p> */}
          <p className="text-xs text-[#667085] mt-1 font-semibold">
            {
              numberOfNightsInCities &&
              Object.entries(numberOfNightsInCities).map(([city, nights]:any, index) => (
                <span key={index}>
                  {city} - {nights} {nights>1?"nights":"night"}
                  {index < Object.entries(numberOfNightsInCities).length - 1 ? ' • ' : ''}
                </span>
              ))
            }
          </p>
        </div>

        <div className="mt-2 sm:mt-3">
          <div className="text-sm text-[#667085] mb-1 font-medium">
            {
              packageData?.priceOnWebsite === "total_price" ?
              "Total Price" :`Price per person ${!packageData?.itineraryType ? "starting from" :''}`
            }
            {/* Price per person */}
          </div>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold text-black">
              {/* ₹{Math.round(packageData?.priceSummary?.netSellingPrice)?.toLocaleString() || '10,000'} */}
              {symbols[`${selectedCurrency}`]} {Math.round((typeof window !== "undefined" && new URLSearchParams(window.location.search).get('custom') !== 'true')? price_to_show:packageData?.priceOnWebsite === "total_price"? convertFromINR(packageData?.priceSummary?.netSellingPrice):convertFromINR((packageData?.priceSummary?.netSellingPrice)/packageData?.noOfAdults))?.toLocaleString() || '10,000'}
            </span>
            {/* { (packageData?.priceSummary?.grossSellingPrice !== packageData?.priceSummary?.netSellingPrice) &&<span className="ml-2 text-sm text-[#98A2B3] line-through decoration-[#98A2B3]"> */}
            { (convertFromINR(packageData?.priceSummary?.grossSellingPrice) !== price_to_show) &&<span className="ml-2 text-sm text-[#98A2B3] line-through decoration-[#98A2B3]">
              {/* ₹{Math.round(packageData?.priceSummary?.netSellingPrice)?.toLocaleString() || '10,000'} */}
              {symbols[`${selectedCurrency}`]} {Math.round((typeof window !== "undefined" && new URLSearchParams(window.location.search).get('custom') !== 'true')? convertFromINR(packageData?.priceSummary?.grossSellingPrice): packageData?.priceOnWebsite === "total_price"?convertFromINR(packageData?.priceSummary?.grossSellingPrice):convertFromINR((packageData?.priceSummary?.grossSellingPrice)/packageData?.noOfAdults))?.toLocaleString() || '10,000'}
            </span>}
          </div>
          <div className="mt-2">
            <div className="text-xs text-[#667085] font-medium">
              Package Code: {packageData.packageCode}
            </div>
          </div>
        </div>

        <div className="mt-6 sm:mt-4 space-y-2 ">
          <button
            onClick={handleBookNow}
            className="w-full bg-[#1570EF] text-white py-2.5 sm:py-3 rounded-full hover:bg-blue-700 transition font-semibold text-sm sm:text-base shadow-custom"
          >
            Book Now
          </button>
          <button onClick={()=>{
            router.push(`/contact?code=${packageData?.packageCode}`);
          }} className="w-full bg-[#EFF8FF] text-[#175CD3] py-2.5 sm:py-3 rounded-full transition font-semibold text-sm sm:text-base border border-[#EFF8FF] shadow-custom">
            Request a callback
          </button>
        </div>

        {/* <div className="mt-6">
          <div className="flex items-center gap-1">
            <span className="font-bold text-black text-xs">🎁</span>
            <span className="font-bold text-black text-xs">Special Offers</span>
          </div>
          <div className="mt-1 sm:mt-2 text-xs text-[#175CD3] font-bold">
            Save ₹{(packageData.priceSummary.netSellingPrice - packageData.priceSummary.netSellingPrice).toLocaleString()} on bookings
            <br />
            <span className="inline-block text-[#667085] text-xs mt-1">
              Offer expires before 31 January 2025. T&C Apply.
            </span>
          </div>
        </div> */}
      </div>

      {/* Need Help Container */}
      <div className="bg-[#EFF8FF] p-3 sm:p-4 lg:p-6 rounded-2xl sm:rounded-3xl">
        <h3 className="text-xl font-bold text-black">Need help?</h3>
        <p className="text-base font-normal text-black mt-1">
          We are ready to help you 24x7 365 days.
        </p>
        <div className="mt-2 sm:mt-3 flex justify-center">
          <div className="bg-[#D1E9FF] rounded-full w-full">
            <a
              href="tel:+919717559499"
              className="flex items-center justify-center gap-1.5 sm:gap-2 px-3 sm:px-4 py-1.5 sm:py-2 text-[#175CD3] hover:text-blue-800 transition-colors text-base w-full"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="w-4 h-4 sm:w-5 sm:h-5"
              >
                <path
                  fillRule="evenodd"
                  d="M1.5 4.5a3 3 0 013-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 01-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 006.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 011.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 01-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5z"
                  clipRule="evenodd"
                />
              </svg>
              <span className="font-semibold">Call us at +91 97175 59499</span>
            </a>
          </div>
        </div>
      </div>

      {/* <PDFDownloadButton targetRef={componentRefData}/> */}
      {/* <PDFDownloadButton setLoadingForDownload={setLoadingForDownload} query={packageData?.packageCode} isCustom={(packageData?.itineraryType && (packageData?.itineraryType?.toLowerCase() == 'final' || packageData?.itineraryType?.toLowerCase() == 'quotation'))?true:false}/> */}
    </div>
  );
};

export default BookingCard;
