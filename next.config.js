/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      'wyimages.s3.ap-south-1.amazonaws.com',
      'localhost',
      '127.0.0.1'
    ],
  },
  env: {
    //API_BASE_URL_DEV: "https://cors-anywhere.herokuapp.com/http://*************", // Publicly accessible
    // API_BASE_URL_DEV: "http://*************", // Publicly accessible
    API_BASE_URL_DEV: process.env.NEXT_PUBLIC_API_BASE_URL, // new Api
  },
  // crossOrigin: '*',
}

module.exports = nextConfig;