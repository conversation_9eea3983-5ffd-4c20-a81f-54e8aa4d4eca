"use client"

import { Suspense } from 'react';
import NavigationBar from '../components/NavBar';
import Breadcrumb from '../components/BreadCrumbs';
import Customized from '../components/Customized';
import Loading from '../components/Loading';
import CallToAction from '../components/ContactSection';
import Footer from '../components/Footer';

export default function CustomizedToursPage() {
  return (
    <main>
      <NavigationBar />
      <Breadcrumb />
      <Suspense fallback={<Loading />}>
        <Customized />
      </Suspense>
      <CallToAction />
      <Footer />
    </main>
  );
}