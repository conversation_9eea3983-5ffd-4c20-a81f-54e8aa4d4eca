'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet-defaulticon-compatibility';
import 'leaflet-defaulticon-compatibility/dist/leaflet-defaulticon-compatibility.css';
import { useEffect, useState } from 'react';

interface MapComponentProps {
  routeData: {
    name: string;
    lat: number;
    lng: number;
    type: string;
    transport?: string;
  }[];
}

const iconMap: any = {
  start: 'http://maps.google.com/mapfiles/ms/icons/green-dot.png',
  end: 'http://maps.google.com/mapfiles/ms/icons/red-dot.png',
  visited: 'http://maps.google.com/mapfiles/ms/icons/blue-dot.png',
  optional: 'http://maps.google.com/mapfiles/ms/icons/yellow-dot.png',
  'start-end': 'http://maps.google.com/mapfiles/ms/icons/purple-dot.png',
};

const transportColors: any = {
  plane: '#9c27b0',   // Purple
  train: '#009688',   // Teal
  cruise: '#3f51b5',  // Indigo
  bus: '#ff5722',     // Deep Orange
};

function FitBounds({ routeData }: { routeData: any[] }) {
  const map = useMap();
  useEffect(() => {
    const bounds = L.latLngBounds(routeData.map((loc) => [loc.lat, loc.lng]));
    map.fitBounds(bounds, { padding: [50, 50] });
  }, [map, routeData]);
  return null;
}

const MapComponent: React.FC<MapComponentProps> = ({ routeData }) => {
  const [activeMarker, setActiveMarker] = useState<number | null>(null);

  return (
    <div className="relative">
      <MapContainer
        style={{
          height: '300px',
          width: '100%',
          borderRadius: '1rem',
          overflow: 'hidden',
          zIndex: 0,
        }}
        center={[0, 0]} // placeholder, real center from FitBounds
        zoom={6}
        scrollWheelZoom={false}
        wheelDebounceTime={100}
        wheelPxPerZoomLevel={120}
      >
        <FitBounds routeData={routeData} />
        <TileLayer
          attribution='&copy; OpenStreetMap contributors'
          url='https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
        />

        {routeData.map((loc:any, index) => (
          <Marker
            key={index}
            position={[loc.lat, loc.lng]}
            eventHandlers={{
              click: () => setActiveMarker(index),
            }}
            icon={
              new L.Icon({
                iconUrl: iconMap[loc.type] || iconMap['visited'],
                iconSize: [32, 32],
                iconAnchor: [16, 32],
                popupAnchor: [0, -32],
              })
            }
          >
            {activeMarker === index && (
                <Popup closeButton={true} autoPan={true}>
                {/* // <Popup closeButton={true} autoPan={false}> */}
                  <div style={{
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '13px',
                    color: '#2c3e50',
                    maxWidth: '240px',
                    lineHeight: '1.4',
                  }}>
                    <strong style={{ color: '#1e40af' }}>{loc.name}</strong><br />
                    <span>Type: {loc.type}</span><br />
                    {loc.days && <span>Visited on Day(s): {loc.days.join(", ")}<br /></span>}
                    {loc.transport && <span>Transport: {loc.transport}<br /></span>}

                    {loc.activities?.length > 0 && (
                      <div className="mt-2">
                        <strong style={{ color: '#374151' }}>Activities:</strong>
                        <ul className="list-disc pl-4">
                          {loc.activities.map((activity: any, i: number) => (
                            <li key={i}>
                              <span className="text-gray-700">{activity.name}</span>
                              <span className="text-gray-400 text-[11px]"> ({activity.type})</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </Popup>
              )}
            {/* {activeMarker === index && (
              <Popup closeButton={true} autoPan={true}>
                <div style={{
                  fontFamily: 'Inter, sans-serif',
                  fontSize: '13px',
                  color: '#2c3e50',
                  maxWidth: '240px',
                  lineHeight: '1.4',
                }}>
                  <strong style={{ color: '#1e40af' }}>{loc.name}</strong><br />
                  <span>Type: {loc.type}</span><br />
                  {loc.transport && <span>Transport: {loc.transport}<br /></span>}

                  {loc.activities?.length > 0 && (
                    <div className="mt-2">
                      <strong style={{ color: '#374151' }}>Activities:</strong>
                      <ul className="list-disc pl-4">
                        {loc.activities.map((activity: any, i: number) => (
                          <li key={i}>
                            <span className="text-gray-700">{activity.name}</span>
                            <span className="text-gray-400 text-[11px]"> ({activity.type})</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </Popup>
            )} */}
          </Marker>
        ))}

        {routeData.map((loc, index) => {
          if (index === 0) return null;

          const prevLoc = routeData[index - 1];
          const transport:any = loc.transport;
          const color = transportColors[transport] || '#808080';

          return (
            <Polyline
              key={`path-${index}`}
              positions={[
                [prevLoc.lat, prevLoc.lng],
                [loc.lat, loc.lng],
              ]}
              pathOptions={{ color, weight: 4 }}
            />
          );
        })}
      </MapContainer>

      {/* Floating Legend */}
      <div
        className="absolute top-3 right-3 bg-white/90 rounded-md p-3 text-xs shadow-md z-[0] space-y-2"
      >
        <LegendItem color="green" label="Start" />
        <LegendItem color="red" label="End" />
        <LegendItem color="blue" label="Visited" />
        <LegendItem color="yellow" label="Optional" />
        <LegendItem color="#9c27b0" label="Plane" />
        <LegendItem color="#009688" label="Train" />
        <LegendItem color="#3f51b5" label="Cruise" />
        <LegendItem color="#ff5722" label="Bus" />
      </div>
    </div>
  );
};

function LegendItem({ color, label }: { color: string; label: string }) {
  return (
    <div className="flex items-center gap-2">
      <div
        className="w-3 h-3 rounded-full"
        style={{ backgroundColor: color }}
      ></div>
      <span>{label}</span>
    </div>
  );
}

export default MapComponent;
