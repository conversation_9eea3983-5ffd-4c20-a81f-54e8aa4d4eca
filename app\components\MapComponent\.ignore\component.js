// with city name and not lat llong

// import { useCallback, useEffect, useRef, useState } from 'react';
// import {
// 	GoogleMap,
// 	LoadScript,
// 	Marker,
// 	InfoWindow,
// 	Polyline,
// } from '@react-google-maps/api';

// const MapComponent = ({ data_to_plot }: any) => {
// 	const mapRef = useRef<any>(null);
// 	const [locations, setLocations] = useState<any[]>([]);
// 	const [activeMarker, setActiveMarker] = useState<number | null>(null);

// 	const geocodeCity = async (city: string) => {
// 		const response = await fetch(
// 			`https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(city)}&key=AIzaSyDVYB1GofLKV56yqxseuasNRX0nNLKWOQg`
// 		);
// 		const data = await response.json();
// 		if (data.status === 'OK') {
// 			const { lat, lng } = data.results[0].geometry.location;
// 			return { lat, lng };
// 		} else {
// 			console.warn(`Geocoding failed for ${city}:`, data.status);
// 			return null;
// 		}
// 	};

// 	useEffect(() => {
// 		const geocodeAll = async () => {
// 			const results = await Promise.all(
// 				data_to_plot.map(async (item: any) => {
// 					const coords = await geocodeCity(item.city_name);
// 					return coords
// 						? { ...item, lat: coords.lat, lng: coords.lng }
// 						: null;
// 				})
// 			);
// 			setLocations(results.filter(Boolean));
// 		};
// 		geocodeAll();
// 	}, [data_to_plot]);

// 	const onLoad = useCallback((map: any) => {
// 		mapRef.current = map;
// 		const bounds = new window.google.maps.LatLngBounds();
// 		locations.forEach((loc: any) => bounds.extend({ lat: loc.lat, lng: loc.lng }));
// 		map.fitBounds(bounds);
// 	}, [locations]);

// 	const mapContainerStyle = {
// 		width: '100%',
// 		height: '600px',
// 	};

// 	const iconMap: any = {
// 		start: 'http://maps.google.com/mapfiles/ms/icons/green-dot.png',
// 		end: 'http://maps.google.com/mapfiles/ms/icons/red-dot.png',
// 		visited: 'http://maps.google.com/mapfiles/ms/icons/blue-dot.png',
// 		optional: 'http://maps.google.com/mapfiles/ms/icons/yellow-dot.png',
// 		'start-end': 'http://maps.google.com/mapfiles/ms/icons/purple-dot.png',
// 	};

// 	const transportColors: any = {
// 		plane: '#FF0000',
// 		train: '#0000FF',
// 		cruise: '#00BFFF',
// 		bus: '#FFA500',
// 	};

// 	return (
// 		<LoadScript googleMapsApiKey="AIzaSyDVYB1GofLKV56yqxseuasNRX0nNLKWOQg">
// 			<GoogleMap
// 				onLoad={onLoad}
// 				mapContainerStyle={mapContainerStyle}
// 			>
// 				{/* Markers */}
// 				{locations.map((loc: any, index: number) => (
// 					<Marker
// 						key={index}
// 						position={{ lat: loc.lat, lng: loc.lng }}
// 						icon={iconMap[loc.type] || iconMap['visited']}
// 						onClick={() => setActiveMarker(index)}
// 					>
// 						{activeMarker === index && (
// 							<InfoWindow onCloseClick={() => setActiveMarker(null)}>
// 								<div>
// 									<strong>{loc.name}</strong><br />
// 									City: {loc.city_name}<br />
// 									Type: {loc.type}<br />
// 									{loc.transport && <>Transport: {loc.transport}</>}
// 								</div>
// 							</InfoWindow>
// 						)}
// 					</Marker>
// 				))}

// 				{/* Polylines */}
// 				{locations.slice(1).map((loc: any, index: number) => {
// 					const prev = locations[index];
// 					const color = transportColors[loc.transport] || '#999';
// 					return (
// 						<Polyline
// 							key={index}
// 							path={[
// 								{ lat: prev.lat, lng: prev.lng },
// 								{ lat: loc.lat, lng: loc.lng },
// 							]}
// 							options={{
// 								strokeColor: color,
// 								strokeOpacity: 0.8,
// 								strokeWeight: 4,
// 							}}
// 						/>
// 					);
// 				})}
// 			</GoogleMap>
// 		</LoadScript>
// 	);
// };

// export default MapComponent;
