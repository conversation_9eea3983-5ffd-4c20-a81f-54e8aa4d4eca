"use client";

import { useContext, useEffect, useState } from 'react';
import { addDays, format } from 'date-fns';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import crypto from 'crypto';
import { AppContext } from '../context/useAppContext';
import { useCurrencyStore } from '../store/useCurrencyStore';
import { useCuponStore } from '../store/bookingCuponStore';

interface TravellerInfo {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  passportNumber: string;
  passportIssueDate: string;
  passportExpiryDate: string;
  nationality: string;
  // passportIssueCity: string;
  mealPreference: 'veg' | 'non-veg';
   gender: 'Male',
  pancard_no: '',
  // mealPreference: 'veg',
  pancard :"",
  passport :""
}

interface EditableSections {
  travelDate: boolean;
  travellers: boolean[];
}

interface TravelDates {
  startDate: string;
  endDate: string;
  duration: string;
}

interface PayUPaymentData {
  key: string;
  txnid: string;
  amount: number;
  productinfo: string;
  firstname: string;
  lastname: string;
  email: string;
  phone: string;
  surl: string;
  furl: string;
  hash?: string;
}
const Checkout = () => {
  const {setCuponApplied,setCuponData,cuponApplied,cuponData} = useCuponStore();
  const router = useRouter();
  const convertFromINR = useCurrencyStore(state => state.convertFromINR);
  const symbols = useCurrencyStore(state => state.symbols);
  const selectedCurrency = useCurrencyStore(state => state.selectedCurrency);
  // const [paymentType, setPaymentType] = useState<'full' | 'installments'>('full');
  const [paymentType, setPaymentType] = useState<'full' | 'installments'>('installments');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'payu' | 'paysharp' | null>(null);
  const [travelDates, setTravelDates] = useState<TravelDates>({
    startDate: '2025-06-04',
    endDate: '2025-06-08',
    duration: '5 days'
  });
  const {bookingData, setBookingData} = useContext(AppContext);
  const {travellers} = bookingData;
  const [editIndex, setEditIndex] = useState<number|any>(null);
  const [editableSections, setEditableSections] = useState<EditableSections>({
    travelDate: false,
    travellers: Array(travellers.length).fill(false)
  });
  const [packageData, setPackageData] = useState<any>(null);
 
  

  // ============ for getting installment on api call ==========
  const [hasInitiatedPayment, setHasInitiatedPayment] = useState(false);
  const [installmentList, setInstallmentList] = useState<any>([]);
  const [isPaymentButtonShowed, setIsPaymentButtonShowed] = useState(false);
  const [currentPaymentPendingId, setCurrentPaymentPendingId] = useState<string | null>(null);
  const [price_to_show,set_price_to_show]= useState<any>(0);
  const [isBookingCreated, setIsBookingCreated] = useState(false);
  
  useEffect(()=>{
    let test = bookingData;
    console.log('test',test);
  },[])
  // Create booking when component mounts
  useEffect(() => {
    const createInitialBooking = async () => {
      try {

        if (!bookingData?.id && !isBookingCreated) {
          console.log('Creating initial booking...');
          const bookingResult = await submitBooking();
          if (bookingResult?.bookingId) {
            setIsBookingCreated(true);
            console.log('Initial booking created:', bookingResult);
          }
        } else{
          // https://stage-api.wiseyatra.com/api/booking/425
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/booking/${bookingData?.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
        },
      });
      let pendingInstallmentId =null;
        const response_json_for_booking= await response.json();  
          debugger;
          setInstallmentList(response_json_for_booking?.bookingInstallmentsInfo);
          if (response_json_for_booking?.bookingInstallmentsInfo?.length > 0) {
        const pendingInstallment = response_json_for_booking?.bookingInstallmentsInfo?.find(
          (element: any) => element.paymentStatus === "PENDING"
        );
        if (pendingInstallment?.id) {
          pendingInstallmentId = pendingInstallment.id;
          setCurrentPaymentPendingId(pendingInstallment.id);
          console.log('Set current payment pending ID:', pendingInstallment.id);
        }
      }
        }
      } catch (error) {
        console.error('Error creating initial booking:', error);
        setError('Failed to initialize booking. Please refresh the page.');
      }
    };

    if (packageData && !isBookingCreated) {
      createInitialBooking();
    }
  }, [packageData, isBookingCreated, bookingData?.id]);

  useEffect(() => {
    const is_custom = typeof window !== "undefined" && new URLSearchParams(window.location.search).get('custom') === 'true';
    if(cuponApplied){
      if(!bookingData?.rule){
        if(!is_custom){
          let total = (packageData?.priceSummary?.netSellingPrice)*(travellers.length);
          set_price_to_show(total-cuponData?.discout_price);
        } else{
          const total = (packageData?.priceSummary?.netSellingPrice / packageData?.noOfAdults)*travellers.length;
          set_price_to_show(total-cuponData?.discout_price);
        }
      } else{
        if(!is_custom){
          let total = (packageData?.priceSummary?.netSellingPrice)*(travellers.length);
          total = total -(total*(bookingData?.rule?.discountPercentage/100))
          set_price_to_show(total-cuponData?.discout_price);
        } else{
          let total = (packageData?.priceSummary?.netSellingPrice / packageData?.noOfAdults)*travellers.length;
          total = total -(total*(bookingData?.rule?.discountPercentage/100));
          set_price_to_show(total-cuponData?.discout_price);
        }
      }
    } else{
      if(!bookingData?.rule){
        if(!is_custom){
          let total = (packageData?.priceSummary?.netSellingPrice)*(travellers.length);
          set_price_to_show(total);
        } else{
          const total = (packageData?.priceSummary?.netSellingPrice / packageData?.noOfAdults)*travellers.length;
          set_price_to_show(total);
        }
      } else{
        if(!is_custom){
          let total = (packageData?.priceSummary?.netSellingPrice)*(travellers.length);
          total = total -(total*(bookingData?.rule?.discountPercentage/100))
          set_price_to_show(total);
        } else{
          let total = (packageData?.priceSummary?.netSellingPrice / packageData?.noOfAdults)*travellers.length;
          total = total -(total*(bookingData?.rule?.discountPercentage/100));
          set_price_to_show(total);
        }
      }
    }
  // Only proceed with payment if we have a booking ID
  if (packageData && bookingData?.id && !hasInitiatedPayment) {
    handlePayment();
    setHasInitiatedPayment(true);
  }

}, [packageData, hasInitiatedPayment]);
  // ============================================================


  useEffect(() => {
    // Fetch package data when component mounts
    const fetchPackageData = async () => {
      const searchParams = new URLSearchParams(window.location.search);
      const packageCode = searchParams.get('query');
      const custom = searchParams.get('custom');
      const API_END_POINT = custom=="true" ?`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/packages/${bookingData.packageData.packageCode}`:`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/packages/website/${bookingData.packageData.packageCode}`
      try {
        const response = await fetch(`${API_END_POINT}`);
        const data = await response.json();
        setPackageData(data);
      } catch (error) {
        console.error('Error fetching package data:', error);
      }
    };
    if (bookingData?.packageData?.packageCode) {
      fetchPackageData();
    } else{
      window.location.href = "/tours/package-details/travellers";

    }
  }, [bookingData.packageCode]);

  const toggleEdit = (section: keyof EditableSections | 'travellers', index?: number) => {
    const searchParams = new URLSearchParams(window.location.search);
    const custom = searchParams.get('custom');
    let redirect_path = custom=="true" ? "/tours/package-details/travellers?custom=true" : '/tours/package-details/travellers';
    router.push(redirect_path);

    // router.replace("package-details/travellers");
    // if(index == -2){
    //   setEditIndex(null);
    // }
    // if(section == "travellers"){
    //   setEditIndex(index);
    // } else {
    //   setEditIndex(-1);
    // }
  };

  type BookingResult = {
    bookingId: string;
    pendingInstallmentId?: string;
  } | null;

  const redirectToLogin = (): BookingResult => {
    const searchParams = new URLSearchParams(window.location.search);
    const custom = searchParams.get('custom');
    const redirectPath = custom == "true" ? '/tours/travellers/checkout?custom=true' : '/tours/travellers/checkout';
    const queryString = new URLSearchParams({ redirect: redirectPath }).toString();
    router.push(`/login?${queryString}`);
    return null;
  };

  const submitBooking = async (): Promise<BookingResult> => {
    console.log("packageData", packageData);
    try {
      setIsProcessing(true);
      setError(null);
      
      // Check if we already have a booking ID
      if (bookingData?.id) {
        console.log('Using existing booking ID:', bookingData.id);
        const result: BookingResult = {
          bookingId: bookingData.id
        };
        if (currentPaymentPendingId) {
          result.pendingInstallmentId = currentPaymentPendingId;
        }
        return result;
      }
      
      const token = localStorage.getItem('authToken');
      if (!token) {
        redirectToLogin();
        return null;
      }
      
      let cupon_code_data = {
        // price: packageData?.priceSummary?.netSellingPrice || 0,
        price: (cuponData?.finalPrice + cuponData?.discout_price ) || 0,
        // finalPrice: packageData?.discountedPrice || packageData?.price || 0,
        finalPrice: cuponData?.finalPrice || packageData?.price || 0,
        couponCode: cuponData?.code || ''
      };
      
      const bookingRequest:any = {
        bookingInfoRequestDTO: {
          userId: JSON.parse(localStorage.getItem('wy_user_data') as string).userId,
          packageCode: bookingData.packageData.packageCode,
          bookingDate: format(new Date(), 'yyyy-MM-dd'),
          "travelDate": format(travelDates.startDate, 'yyyy-MM-dd'),
          passengersInfo: travellers.map((traveller: TravellerInfo) => ({
            name: `${traveller.firstName} ${traveller.lastName}`,
            age: new Date(traveller.dateOfBirth).getFullYear() - new Date().getFullYear(),
            passportNo: traveller.passportNumber,
            panNo: 'asdasdasdsad',
            issueDate: traveller.passportIssueDate,
            expiryDate: traveller.passportExpiryDate,
            issueCountry: traveller.nationality,
            foodType: traveller.mealPreference === 'veg' ? 'Vegetarian' : 'Non-Vegetarian'
          })),
          bookingInstallmentsInfo: [],
          totalAmount: packageData?.price || 0
        },
        applyCouponResponse: cuponApplied ? cupon_code_data : null
      };
      if(bookingData?.rule){
        bookingRequest.bookingInfoRequestDTO.dynamicDiscountRuleId = `${bookingData?.rule?.ruleId}`;
      }
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/booking/book?authtoken=${token}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(bookingRequest)
      });

      if (response.status === 401) {
        // Clear any invalid token
        localStorage.removeItem('authToken');
        setError('Your session has expired. Please login again.');
        redirectToLogin();
        return null;
      }

      if (!response.ok) {
        throw new Error('Booking failed');
      }

      const data = await response.json();
      
      // Update the booking data with the new ID
      // Find pending installment if any
      let pendingInstallmentId: string | undefined;
      if (data.bookingInstallmentsInfo?.length > 0) {
        const pendingInstallment = data.bookingInstallmentsInfo?.find(
          (element: any) => element.paymentStatus === "PENDING"
        );
        if (pendingInstallment?.id) {
          pendingInstallmentId = pendingInstallment.id;
          setCurrentPaymentPendingId(pendingInstallment.id);
          console.log('Set current payment pending ID:', pendingInstallment.id);
        }
      }

      // Update component state
      setBookingData((prev: any) => ({
        ...prev,
        id: data.id
      }));
      
      setInstallmentList(data.bookingInstallmentsInfo || []);
      
      console.log('Booking created successfully. ID:', data.id);
      console.log('Installments:', data.bookingInstallmentsInfo);
      
      return {
        bookingId: data.id,
        pendingInstallmentId: pendingInstallmentId
      };
      
    } catch (error) {
      console.error('Payment processing error:', error);
      setError('Failed to process payment. Please try again.');
      return null;
    } finally {
      setIsProcessing(false);
    }
  };

  const updateTraveller = (index: number, field: keyof TravellerInfo, value: string) => {
    const newTravellers = [...travellers];
    newTravellers[index] = {
      ...newTravellers[index],
      [field]: value
    };
    setBookingData((prev:any)=>{
      let new_data = {...prev};
      return {
        ...prev,
        travellers:newTravellers
      }
    });
  };

  const PAYU_BASE_URL = "https://test.payu.in/_payment";
  const SUCCESS_URL = `${process.env.NEXT_PUBLIC_BASE_URL}/payment/success`;
  const FAILURE_URL = `${process.env.NEXT_PUBLIC_BASE_URL}/payment/failure`;
  const MERCHANT_KEY = "6ACACh";
  const MERCHANT_SALT = "kndcSciPLAVKK9bb0mp5uw8fBRTIBdSD";

  const generateHash = (paymentData: PayUPaymentData): string => {
    const hashString = `${MERCHANT_KEY}|${paymentData.txnid}|${paymentData.amount}|${paymentData.productinfo}|${paymentData.firstname}|${paymentData.email}|||||||||||${MERCHANT_SALT}`;
    return crypto.createHash('sha512').update(hashString).digest('hex');
  };

  const handlePayment = async (fromPaymenClick = false): Promise<void> => {
    try {
      setIsProcessing(true);
      setError(null);

      // Check for authentication first
      const token = localStorage.getItem('authToken');
      if (!token) {
        redirectToLogin();
        return;
      }

      // If we don't have a booking ID yet, create one
      if (!bookingData?.id) {
        const bookingResult = await submitBooking();
        if (!bookingResult) {
          // If bookingResult is null, it means we've been redirected to login
          return;
        }
        if (!bookingResult.bookingId) {
          throw new Error('Failed to create booking');
        }
      }

      let amount = paymentType === 'installments' 
        ? ((packageData?.priceSummary?.netSellingPrice || 82500) * travellers.length) / 3 
        : (packageData?.priceSummary?.netSellingPrice || 82500) * travellers.length;
      
      // If this is a payment for a specific installment, get its amount
      if (fromPaymenClick && installmentList.length > 0 && currentPaymentPendingId) {
        const pendingInstallment = installmentList.find((element: any) => element.id == currentPaymentPendingId);
        if (pendingInstallment) {
          amount = pendingInstallment.amount;
          // amount = bookingData?.rule ? Math.round(amount - (amount * (bookingData?.rule?.discountPercentage / 100))) : amount;
          amount = amount;
        }
      }
      
      const total_amount_before_discount = (packageData?.priceSummary?.netSellingPrice || 82500) * travellers.length;
      const displayAmount = amount.toLocaleString();
      // let after_discount = bookingData?.rule 
      //   ? Math.round(total_amount_before_discount - (total_amount_before_discount * (bookingData?.rule?.discountPercentage / 100))) 
      //   : total_amount_before_discount;
      // const originalAmount = after_discount.toLocaleString();
      const originalAmount = total_amount_before_discount.toLocaleString();
      
      // Encode the title to handle special characters in URL
      const encodedTitle = encodeURIComponent(packageData?.packageTitle || '');
      
      // Only create a new booking if we don't have one yet
      let bookingId = bookingData?.id;
      let pendingInstallmentId = currentPaymentPendingId;
      
      console.log("Current booking ID:", bookingId, "Pending Installment ID:", pendingInstallmentId);
      
      if (!bookingId) {
        const bookingResult = await submitBooking();
        console.log("Booking result:", bookingResult);
        
        if (!bookingResult) {
          // If bookingResult is null, it means we've been redirected to login
          return;
        }
        
        bookingId = bookingResult.bookingId;
        
        // Use the pending installment ID from the booking result if available
        if (bookingResult.pendingInstallmentId) {
          pendingInstallmentId = bookingResult.pendingInstallmentId;
        }
        
        if (!bookingId) {
          throw new Error('Failed to create booking');
        }
      }

      // Use the pending installment ID for the payment
      const installmentId = fromPaymenClick && pendingInstallmentId 
        ? pendingInstallmentId 
        : '';

        console.log("Installment ID:", installmentId);

      // Construct query parameters for payment
      const queryParams = new URLSearchParams({
        // amount: amount.toString(),
        // displayAmount,
        // originalAmount,
        // paymentType,
        // title: encodedTitle,
        // packageId: packageData?.packageCode || '',
        // duration: `${travelDates?.duration || 0} days`,
        // custom: (packageData?.itineraryType?.toLowerCase() === "final" || packageData?.itineraryType?.toLowerCase() === "quotation") ? "true" : "false",
        bookingId: bookingId,
        installmentId: installmentId
      });

      // Only redirect if this is a payment click
      if (fromPaymenClick) {
        router.push(`/checkout/payment?${queryParams.toString()}`);
      }
  } catch (error) {
    console.error('Payment processing error:', error);
    setError('Failed to process payment. Please try again.');
    setIsProcessing(false);
    return;
  } finally {
    setIsProcessing(false);
  }
  };

  const handlePayU = async () => {
    try {
      setIsProcessing(true);
      setError(null);
      
      const amount = paymentType === 'installments' ? 45000 : 135000;
      const orderId = `ORDER${Date.now()}`;

      const paymentData: PayUPaymentData = {
        key: MERCHANT_KEY,
        txnid: orderId,
        amount: amount,
        productinfo: "Italy Tour Package",
        firstname: travellers[0].firstName,
        lastname: travellers[0].lastName,
        email: "<EMAIL>", // Should come from user profile
        phone: "9111100000", // Should come from user profile
        surl: SUCCESS_URL,
        furl: FAILURE_URL
      };

      // Generate hash
      paymentData.hash = generateHash(paymentData);

      // Create form and submit to PayU
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = PAYU_BASE_URL;

      // Add all fields to the form
      Object.entries(paymentData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          const input = document.createElement('input');
          input.type = 'hidden';
          input.name = key;
          input.value = value?.toString() || "";
          form.appendChild(input);
        }
      });

      // Add form to body and submit
      document.body.appendChild(form);
      form.submit();
      document.body.removeChild(form);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to process payment');
      setIsProcessing(false);
    }
  };

  const PAYSHARP_BASE_URL = "https://sandbox.paysharp.co.in/external/api/v1/upi/order/request";
  const PAYSHARP_API_KEY_ID = process.env.NEXT_PUBLIC_PAYSHARP_API_KEY_ID || '';
  const PAYSHARP_API_TOKEN = process.env.NEXT_PUBLIC_PAYSHARP_API_SECRET_KEY || '';

  const handlePaySharp = async () => {
    try {
      setIsProcessing(true);
      setError(null);
      
      const amount = paymentType === 'installments' ? 45000 : 135000;
      const orderId = `ORDER${Date.now()}`;

      // Get customer VPA from user input
      const customerVPA = prompt("Please enter your UPI ID (e.g., username@upi)");
      if (!customerVPA) {
        setError("UPI ID is required");
        setIsProcessing(false);
        return;
      }

      const paymentData = {
        orderId,
        amount,
        customerVPA,
        customerId: travellers[0].firstName + travellers[0].lastName,
        customerName: `${travellers[0].firstName} ${travellers[0].lastName}`,
        customerMobileNo: "9111100000", // Should come from user profile
        customerEmail: "<EMAIL>", // Should come from user profile
        remarks: `Tour Package Payment - ${orderId}`
      };

      // Generate timestamp for request
      const timestamp = new Date().toISOString();

      const headers = new Headers({
        'Content-Type': 'application/json',
        'x-api-key': PAYSHARP_API_KEY_ID,
        'x-api-token': PAYSHARP_API_TOKEN,
        'x-timestamp': timestamp
      });

      const response = await fetch(`${PAYSHARP_BASE_URL}/request`, {
        method: 'POST',
        headers,
        body: JSON.stringify(paymentData)
      });

      const result = await response.json();

      if (result.code === 200) {
        // Payment request sent successfully
        alert("Payment request has been sent to your UPI app. Please complete the payment.");
        
        // Poll for payment status
        const checkPaymentStatus = async () => {
          const statusHeaders = new Headers({
            'x-api-key': PAYSHARP_API_KEY_ID,
            'x-api-token': PAYSHARP_API_TOKEN,
            'x-timestamp': timestamp
          });

          const statusResponse = await fetch(`${PAYSHARP_BASE_URL}/status/${orderId}`, {
            headers: statusHeaders
          });
          const statusResult = await statusResponse.json();
          
          if (statusResult.data.status === 'SUCCESS') {
            // Payment successful
            router.push('/payment/success');
          } else if (statusResult.data.status === 'FAILED') {
            setError(`Payment failed: ${statusResult.data.failureReason}`);
            setIsProcessing(false);
          } else if (statusResult.data.status === 'EXPIRED') {
            setError("Payment request expired. Please try again.");
            setIsProcessing(false);
          } else {
            // Still processing, check again after 5 seconds
            setTimeout(checkPaymentStatus, 5000);
          }
        };

        // Start polling
        checkPaymentStatus();
      } else {
        setError(`Failed to initiate payment: ${result.message}`);
        setIsProcessing(false);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to process payment');
      setIsProcessing(false);
    }
  };

  useEffect(()=>{
    
    setTravelDates(()=>{
      //  
      if(bookingData?.packageData?.itineraryType?.toLowerCase() =="final" || bookingData?.packageData?.itineraryType?.toLowerCase() =="quotation" ){
      return {
        startDate:new Date(bookingData?.packageData?.packageStartDate),
        // endDate: '2025-06-08',
        // endDate: format(addDays(bookingData?.date, 5), 'yyyy-MM-dd'),
        endDate: format(addDays(new Date(bookingData?.packageData?.packageStartDate), bookingData?.packageData?.noOfDays-1), 'yyyy-MM-dd'),
        duration: bookingData.packageData.noOfDays
      }
      }
      if(!bookingData?.date || bookingData?.date==""){
      return {
    startDate: '2025-06-04',
    endDate: '2025-06-08',
    duration: '5 days'
  };
    }
      return {
        startDate: bookingData?.date,
        // endDate: '2025-06-08',
        // endDate: format(addDays(bookingData?.date, 5), 'yyyy-MM-dd'),
        endDate: format(addDays(bookingData?.date, bookingData?.packageData?.noOfDays-1), 'yyyy-MM-dd'),
        duration: bookingData.packageData.noOfDays
      }
    })
  },[bookingData?.date])

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-[0]">
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Left Column */}
        <div className="flex-1 space-y-4">
          {/* Title and Features */}
          <div className="mb-8">
            <a href={`/tours/package-details?query=${packageData?.packageCode}`} target='_blank'>
            <h1 className="text-[30px] font-bold text-[#1E1E1E] mb-3">
              {packageData?.packageTitle || 'Best of Italy in one week (5 Days in Italy - Attractions of Venice & Rome)'}
            </h1>
            </a>
            <div className="flex flex-wrap mb-4 gap-[40px]">
              {packageData?.hotelIncluded &&
              <div className="flex items-center gap-2">
                <img src="/assets/hotels_included.svg" alt="Hotels" className="h-[19.10262680053711px] w-[21px]" />
                <span className="font-[700] text-[14px] text-[#000000] font-[Manrope] ">Hotels included</span>
              </div>}

              {packageData?.transferIncluded &&
              <div className="flex items-center gap-2">
                <img src="/assets/transfer_included.svg" alt="Transfers" className="h-[28px] w-[28px]" />
                <span className="font-[700] text-[14px] text-[#000000] font-[Manrope] ">Transfers included</span>
              </div>}

              {packageData?.activityIncluded && 
              <div className="flex items-center gap-2">
                <img src="/assets/activites_included.svg" alt="Activities" className="h-[28px] w-[28px]" />
                <span className="font-[700] text-[14px] text-[#000000] font-[Manrope] ">Activities included</span>
              </div>}
              {packageData?.flightIncluded && 
              <div className="flex items-center gap-2">
                {/* <img src="/assets/activites_included.svg" alt="Activities" className="h-[28px] w-[28px]" /> */}
                <img src="/assets/transfer_included.svg" alt="Transfers" className="h-[28px] w-[28px]" />
                <span className="font-[700] text-[14px] text-[#000000] font-[Manrope] ">Flights included</span>
              </div>}
            </div>
            <div className="flex flex-wrap gap-2">
              <span className="px-3 py-1 bg-gray-100 rounded-full text-[12px] text-[#344054] font-medium">Adventure</span>
              <span className="px-3 py-1 bg-gray-100 rounded-full text-[12px] text-[#344054] font-medium">Culture</span>
              <span className="px-3 py-1 bg-gray-100 rounded-full text-[12px] text-[#344054] font-medium">Honeymoon</span>
              <span className="px-3 py-1 bg-gray-100 rounded-full text-[12px] text-[#344054] font-medium">Western Europe</span>
            </div>
          </div>

          {/* Travelling Dates Section */}
          <div className="mb-8" style={{marginTop:"48px"}}>
            <div className="bg-blue-50 rounded-xl p-6 relative">
                <div className="absolute -top-3 left-4">
                <span className="text-base font-bold text-[#000000] bg-blue-100 px-3 py-1 rounded-md">
                  Travelling Dates
                  </span>
              </div>
              <div className="absolute -top-3 right-4">
                {/* onClick={() => toggleEdit('travelDate')} */}
                {editIndex == -1 ?
                    <button
                    onClick={() => toggleEdit('travellers', -2)}
                    className="text-blue-600 hover:text-blue-700 p-1.5 rounded-full"
                      >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M3 13.5V12a9 9 0 019-9m0 0v3.75M12 3L9.75 5.25M21 10.5v1.5a9 9 0 01-9 9m0 0v-3.75M12 21l2.25-2.25" />
                        </svg>
    
                      </button> :
                    <button
                  onClick={() => toggleEdit('travelDate')}
                  className="text-blue-600 hover:text-blue-700 p-1.5 rounded-full"
                    >
                      <img src="/assets/edit_icon_new.svg"/>
                  {/* <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                      </svg> */}
                    </button>
                    
                  }
              </div>
              <div className="mt-3 grid grid-cols-3 gap-8">
                {
                  editIndex == -1 ?
                  <div className="flex gap-2 flex-col items-start">
                    <div className="text-sm text-gray-700">
                    Start Date
                  </div>
                    <input
                      type="date"
                      className="border rounded-lg px-2.5 py-1 text-xs text-gray-700 bg-white w-48"
                      placeholder="When are you planning to travel"
                      value ={bookingData.date}
                      onChange ={(e)=>setBookingData((prev:any)=>{
                        return {
                          ...prev,
                          date:e.target.value
                        }
                      })}
                    />
                    <span className="text-sm text-gray-500">Prices may differ based on your travel dates.</span>
                  </div> :
                <div>
                  <div className="text-sm text-gray-500">Start Date</div>
                  <div className="text-sm font-medium text-gray-900">{travelDates.startDate && travelDates.startDate!="" &&format(new Date(travelDates.startDate), 'dd MMM yyyy')}</div>
                </div>
                }
                
                <div>
                  <div className="text-sm text-gray-500">End Date</div>
                  <div className="text-sm font-medium text-gray-900">{travelDates.endDate && travelDates.endDate !="" && format(new Date(travelDates.endDate), 'dd MMM yyyy')}</div>
                  </div>
                <div>
                  <div className="text-sm text-gray-500">Duration</div>
                  <div className="text-sm font-medium text-gray-900">{travelDates.duration}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Traveller Information */}
            {travellers.map((traveller:any, index:number) => (
            <div key={index} className="mb-8 bg-blue-50 rounded-xl p-6 relative" style={{marginTop:"48px"}}>
                <div className="absolute -top-3 left-4">
                <span className="text-base font-bold text-[#000000] rounded-md  bg-blue-100 px-3 py-1">
                    Traveller {index + 1}
                  </span>
                </div>
                <div className="absolute -top-3 right-4">
                  {editIndex == index ?
                   <button
                   onClick={() => toggleEdit('travellers', -2)}
                   className="text-blue-600 hover:text-blue-700 p-1.5 rounded-full"
                     >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                         <path strokeLinecap="round" strokeLinejoin="round" d="M3 13.5V12a9 9 0 019-9m0 0v3.75M12 3L9.75 5.25M21 10.5v1.5a9 9 0 01-9 9m0 0v-3.75M12 21l2.25-2.25" />
                       </svg>
   
                     </button> :
                  <button
                onClick={() => toggleEdit('travellers', index)}
                className="text-blue-600 hover:text-blue-700 p-1.5 rounded-full"
                  >
                    <img src="/assets/edit_icon_new.svg"/>
                {/* <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                    </svg> */}
                  </button>
                  
                }
                </div>
                {
                  editIndex == index ? 
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-3">
                  {/* First Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      *First Name
                    </label>
                    <input
                      type="text"
                      value={traveller.firstName}
                      onChange={(e) => updateTraveller(index, 'firstName', e.target.value)}
                      className="w-full border rounded-lg px-3 py-2"
                      required
                    />
                  </div>

                  {/* Last Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      *Last Name
                    </label>
                    <input
                      type="text"
                      value={traveller.lastName}
                      onChange={(e) => updateTraveller(index, 'lastName', e.target.value)}
                      className="w-full border rounded-lg px-3 py-2"
                      required
                    />
                  </div>

                  {/* Date of Birth */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      *Date of Birth
                    </label>
                    <input
                      type="date"
                      value={traveller.dateOfBirth}
                      onChange={(e) => updateTraveller(index, 'dateOfBirth', e.target.value)}
                      className="w-full border rounded-lg px-3 py-2"
                      required
                    />
                  </div>

                  {/* Passport Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      *Passport Number
                    </label>
                    <input
                      type="text"
                      value={traveller.passportNumber}
                      onChange={(e) => updateTraveller(index, 'passportNumber', e.target.value)}
                      className="w-full border rounded-lg px-3 py-2"
                      required
                    />
                  </div>

                  {/* Passport Issue Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      *Passport Issue Date
                    </label>
                    <input
                      type="date"
                      value={traveller.passportIssueDate}
                      onChange={(e) => updateTraveller(index, 'passportIssueDate', e.target.value)}
                      className="w-full border rounded-lg px-3 py-2"
                      required
                    />
                  </div>

                  {/* Passport Expiry Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      *Passport Expiry Date
                    </label>
                    <input
                      type="date"
                      value={traveller.passportExpiryDate}
                      onChange={(e) => updateTraveller(index, 'passportExpiryDate', e.target.value)}
                      className="w-full border rounded-lg px-3 py-2"
                      required
                    />
                  </div>

                  {/* Nationality */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      *Nationality
                    </label>
                    <select
                      value={traveller.nationality}
                      onChange={(e) => updateTraveller(index, 'nationality', e.target.value)}
                      className="w-full border rounded-lg px-3 py-2"
                      required
                    >
                      <option value="India">India</option>
                      {/* Add more nationality options as needed */}
                    </select>
                  </div>

                  {/* Passport Issue City */}
                  {/* <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      *Passport Issue City
                    </label>
                    <input
                      type="text"
                      value={traveller.passportIssueCity}
                      onChange={(e) => updateTraveller(index, 'passportIssueCity', e.target.value)}
                      className="w-full border rounded-lg px-3 py-2"
                      required
                    />
                  </div> */}

                  {/* Meal Preference */}
                  {/* <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      *Meal Preference
                    </label>
                    <div className="flex gap-4">
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          checked={traveller.mealPreference === 'veg'}
                          onChange={() => updateTraveller(index, 'mealPreference', 'veg')}
                          className="text-blue-600"
                        />
                        <span className="text-sm text-gray-700">🥗 Veg</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          checked={traveller.mealPreference === 'non-veg'}
                          onChange={() => updateTraveller(index, 'mealPreference', 'non-veg')}
                          className="text-blue-600"
                        />
                        <span className="text-sm text-gray-700">🍖 Non-Veg</span>
                      </label>
                    </div>
                  </div> */}
                </div> : 
                <div className="mt-3 grid grid-cols-3 gap-8">
                  <div>
                    <div className="text-sm text-gray-500">First Name</div>
                    <div className="text-sm font-medium text-gray-900">{traveller.firstName}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Last Name</div>
                    <div className="text-sm font-medium text-gray-900">{traveller.lastName}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Date of Birth</div>
                    <div className="text-sm font-medium text-gray-900">{traveller.dateOfBirth && traveller.dateOfBirth!=="" && format(new Date(traveller.dateOfBirth), 'dd MMM yyyy')}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Passport Number</div>
                    <div className="text-sm font-medium text-gray-900">{traveller.passportNumber}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Passport Issue Date</div>
                    <div className="text-sm font-medium text-gray-900">{traveller?.passportIssueDate && traveller?.passportIssueDate!="" && format(new Date(traveller.passportIssueDate), 'dd MMM yyyy')}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Passport Expiry Date</div>
                    <div className="text-sm font-medium text-gray-900">{traveller?.passportExpiryDate && traveller?.passportExpiryDate!=="" && format(new Date(traveller.passportExpiryDate), 'dd MMM yyyy')}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Nationality</div>
                    <div className="text-sm font-medium text-gray-900">{traveller.nationality}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Gender</div>
                    <div className="text-sm font-medium text-gray-900">{traveller.gender}</div>
                  </div>
                  {
                  traveller.pancard_no && traveller.pancard_no!="" && traveller.nationality.toLowerCase() ==="india" && <div>
                    <div className="text-sm text-gray-500">Pancard Number</div>
                    <div className="text-sm font-medium text-gray-900">{traveller.pancard_no}</div>
                  </div>
                  }
                  {/* <div>
                    <div className="text-sm text-gray-500">Passport Issue City</div>
                    <div className="text-sm font-medium text-gray-900">{traveller.passportIssueCity}</div>
                  </div> */}
                  {/* <div>
                    <div className="text-sm text-gray-500">Meal Preference</div>
                    <div className="text-sm font-medium text-gray-900">
                      {traveller.mealPreference === 'veg' ? 'Vegetarian' : 'Non-Vegetarian'}
                    </div>
                  </div> */}
                </div>
                }
                </div>
            ))}
        </div>

        {/* Right Column - Payment Section */}
        <div className="lg:w-96">
          <div className="border-gray-200 border rounded-xl p-6">
            <div className="bg-yellow-50 -mx-6 -mt-6 p-6 rounded-t-xl">
              <div className="mb-1">
                <span className="text-sm text-gray-600">Amount Payable</span>
              </div>
              <div className="flex items-baseline gap-2">
                <span className="text-2xl font-bold text-gray-900">{symbols[`${selectedCurrency}`]} {Math.round(convertFromINR(price_to_show)).toLocaleString()}</span>
                {bookingData.isPromoApplied && (
                  <span className="text-sm text-gray-500 line-through">{symbols[`${selectedCurrency}`]} {(Math.round(((typeof window !== "undefined" && new URLSearchParams(window.location.search).get('custom') !== 'true')? convertFromINR(packageData?.priceSummary?.grossSellingPrice):convertFromINR((packageData?.priceSummary?.grossSellingPrice)/packageData?.noOfAdults)) * travellers.length)).toLocaleString()}</span>
                )}
              </div>
              <div className="text-xs text-gray-500">inclusive of all taxes</div>
            </div>

            <div className="mt-6 space-y-4">
              {/* <div className="flex gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    checked={paymentType === 'full'}
                    onChange={() => setPaymentType('full')}
                    className="text-blue-600"
                  />
                  <span className="text-sm text-gray-700">Pay in full</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    checked={paymentType === 'installments'}
                    onChange={() => setPaymentType('installments')}
                    className="text-blue-600"
                  />
                  <span className="text-sm text-gray-700">Pay in installments</span>
                </label>
              </div> */}

              {paymentType === 'installments' && (
                <>
                  <div>Payment Installments</div>
                  {
                    installmentList.map((installment: any, index: number) =>{
                      const firstInstallmentDate = new Date(installmentList[0].paymentDate);
                      let paymentDate = new Date(installment.paymentDate);

                      if (index !== 0 && paymentDate < new Date()) {
                        // Set to first installment + 1 day
                        const updatedDate = new Date(firstInstallmentDate);
                        updatedDate.setDate(updatedDate.getDate() + 1);
                        paymentDate = updatedDate;
                      }
                      return (
                      <div key={index} className='flex justify-between items-center'>
                      <div key={index}>
                        {/* <div className="text-sm text-gray-600 mb-1">Installment {index + 1}</div> */}
                        {/* <div className="text-lg font-medium text-gray-900">{symbols[`${selectedCurrency}`]} {Math.round(bookingData?.rule ? convertFromINR(installment.amount-(installment.amount*(bookingData?.rule?.discountPercentage/100))) : convertFromINR(installment.amount) )}</div> */}
                        <div className="text-lg font-medium text-gray-900">{symbols[`${selectedCurrency}`]} {Math.round(convertFromINR(installment.amount))}</div>
                        <div className="text-sm text-gray-600 mb-1">Pay on: <span className="text-sm text-gray-600 mb-1">{paymentDate.toLocaleDateString('en-GB').replace(/\//g, '-')}</span></div>
                      </div>
                      {
                      installment.paymentStatus.toLowerCase() === 'pending' && installment.id ==currentPaymentPendingId && (
                        <button
                          className=" text-sm bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                          // onClick={()=>{}}
                          onClick={()=>handlePayment(true)}
                        >
                          {isProcessing ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Processing...
                              </>
                            ) : ( 
                                      "Pay Now"
                            )}
                        </button>
                      )}
                      </div>
                      )
                    })

                  }
                </>
                // <div className="space-y-4">
                //   <div className="flex items-center gap-4">
                //     <label className="text-sm text-gray-600 whitespace-nowrap">Choose Tenure</label>
                //     <select className="border rounded-lg px-3 py-2 text-sm text-gray-700 bg-white flex-1">
                //       <option value="3">3 months</option>
                //       <option value="6">6 months</option>
                //       <option value="9">9 months</option>
                //       <option value="12">12 months</option>
                //     </select>
                //   </div>
                //   <div>
                //     <div className="text-sm text-gray-600 mb-1">Today youll pay</div>
                //     <div className="text-lg font-medium text-gray-900">₹{(((bookingData.isPromoApplied ? (Math.round((packageData?.priceSummary?.netSellingPrice) * travellers.length - 30000)) : ((packageData?.priceSummary?.netSellingPrice) * travellers.length)) / 3)).toLocaleString()}</div>
                //   </div>
                //   <button 
                //     className="text-blue-600 text-sm hover:text-blue-700 hover:underline"
                //   >
                //     View Payment Schedule
                //   </button>
                // </div>
              )}

              {error && (
                <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-lg text-sm">
                  {error}
                </div>
              )}

              {/* Comment this later.. */}
              {/* <button
                className={`w-full ${
                  isProcessing 
                    ? 'bg-blue-400 cursor-not-allowed' 
                    : 'bg-blue-600 hover:bg-blue-700'
                } text-white py-3 rounded-lg font-medium flex items-center justify-center`}
                onClick={handlePayment}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  `Pay ${paymentType === 'installments' ? 
                    `₹${(((bookingData.isPromoApplied ? (Math.round((packageData?.priceSummary?.netSellingPrice) * travellers.length - 30000)) : ((packageData?.priceSummary?.netSellingPrice) * travellers.length)) / 3)).toLocaleString()}` : 
                    `₹${(bookingData.isPromoApplied ? (Math.round((packageData?.priceSummary?.netSellingPrice) * travellers.length - 30000)) : ((packageData?.priceSummary?.netSellingPrice) * travellers.length)).toLocaleString()}`}`
                )}
              </button> */}

              <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                </svg>
                <span>100% Safe and Secure Payment</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Checkout;
