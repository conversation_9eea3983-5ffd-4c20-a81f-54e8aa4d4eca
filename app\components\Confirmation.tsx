"use client";
import Image from 'next/image';

interface TourDetails {
  title: string;
  image: string;
  tags: string[];
  country: string;
  startDate: string;
  endDate: string;
  duration: string;
  travellers: number;
}

const Confirmation = () => {
  const tourDetails: TourDetails = {
    title: "Best of Italy in one week (5 Days in Italy - Attractions of Venice & Rome)",
    image: "/venice.jpg",
    tags: ["Adventure", "Culture", "Honeymoon", "Western Europe"],
    country: "Italy",
    startDate: "04 Jun 2025",
    endDate: "08 Jun 2025",
    duration: "6 Days 5 Nights",
    travellers: 2
  };

  return (
    <div className="w-full px-20 py-8">
      <div className="space-y-6">
        {/* Success Message */}
        <div className="flex items-start gap-4">
          <div className="w-8 h-8 rounded-full bg-green-400 flex-shrink-0" />
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 mb-1">
              Congratulations! Your booking was successful.
            </h1>
            <p className="text-gray-600">
              Youll soon receive the itinerary and other details on your registered email address.
            </p>
          </div>
        </div>

        {/* Tour Card */}
        <div className="bg-white rounded-2xl overflow-hidden">
          <div className="flex">
            {/* Left - Image */}
            <div className="relative w-80 h-52 overflow-hidden rounded-2xl m-3">
              <Image 
                src="/assets/tours/imagetwo.png"
                alt={tourDetails.title}
                fill
                className="object-cover"
                sizes="320px"
                priority
              />
            </div>
            
            {/* Middle - Tour Details */}
            <div className="flex-1 p-6">
              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-3 text-xs text-gray-600">
                {tourDetails.tags.map((tag, index) => (
                  <span key={index}>
                    {index > 0 && "• "}{tag}
                  </span>
                ))}
              </div>

              {/* Title */}
              <h2 className="text-lg font-semibold text-gray-900 mb-3">
                {tourDetails.title}
              </h2>

              {/* Country */}
              <div className="flex items-center gap-2 mb-4">
                <Image 
                  src="/assets/flags/italy.png"
                  alt="Italy"
                  width={16}
                  height={12}
                  className="object-cover rounded-sm"
                  priority
                />
                <span className="text-sm text-gray-900">{tourDetails.country}</span>
              </div>

              {/* Tour Info Grid */}
              <div className="grid grid-cols-4 gap-8">
                <div>
                  <div className="text-xs text-gray-500">Starts</div>
                  <div className="text-sm font-medium text-gray-900">{tourDetails.startDate}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500">Ends</div>
                  <div className="text-sm font-medium text-gray-900">{tourDetails.endDate}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500">Duration</div>
                  <div className="text-sm font-medium text-gray-900">{tourDetails.duration}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500">Travellers</div>
                  <div className="text-sm font-medium text-gray-900">{tourDetails.travellers.toString().padStart(2, '0')}</div>
                </div>
              </div>
            </div>

            {/* Right - View Details Button */}
            <div className="flex items-center justify-end pl-0 pr-6 min-w-[160px]">
              <button className="bg-blue-700 text-white px-8 py-4 rounded-full text-sm hover:bg-blue-800 whitespace-nowrap transition-colors">
                View Details
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Confirmation;
