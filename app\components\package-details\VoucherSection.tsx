"use client";

import React from 'react';
import type { PackageDetails } from '@/app/types/PackageDetails';

interface VoucherSectionProps {
  packageData: PackageDetails;
  expandedSections: string[];
  toggleSection: (section: string) => void;
}

const VoucherSection: React.FC<VoucherSectionProps> = ({
  packageData,
  expandedSections,
  toggleSection
}) => {
  // You can use packageData to dynamically generate voucher links if needed
  // For now, we'll keep the static links from the original component

  return (
    <section id="vouchers" className="mt-6 sm:mt-8 border-t pt-4">
      <button
        onClick={() => toggleSection('vouchers')}
        className="w-full flex items-center justify-between"
      >
        <h2 className="text-base sm:text-lg font-semibold text-gray-900">Vouchers</h2>
        <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full border-2 border-blue-700 flex items-center justify-center bg-white">
          {expandedSections.includes('vouchers') ? (
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 text-blue-700">
              <path strokeLinecap="round" strokeLinejoin="round" d="M18 12H6" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 text-blue-700">
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m6-6H6" />
            </svg>
          )}
        </div>
      </button>
      <div className={`mt-2 space-y-2 transition-all duration-300 ${expandedSections.includes('vouchers')
          ? 'max-h-[1000px] opacity-100'
          : 'max-h-0 opacity-0 overflow-hidden'
        }`}>
        <div className="space-y-2 text-sm sm:text-base">
          {/* If you have dynamic voucher data, you can map through it here */}
          {/* For now, using static links as in the original component */}
          <a href="/assets/vouchers/italy-tour-package.pdf" className="block text-blue-600 hover:underline">
            Italy Tour Package.pdf
          </a>
          <a href="/assets/vouchers/italy-visa-requirements.pdf" className="block text-blue-600 hover:underline">
            Italy Visa Requirements.pdf
          </a>
          <a href="/assets/vouchers/italy-travel-guide.pdf" className="block text-blue-600 hover:underline">
            Italy Travel Guide.pdf
          </a>
        </div>
      </div>
    </section>
  );
};

export default VoucherSection;
