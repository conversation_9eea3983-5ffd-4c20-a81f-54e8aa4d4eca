"use client"

import Image from 'next/image'
import Link from 'next/link'
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { GetServerSideProps } from 'next';
import NavigationBar from '../components/NavBar';
import Breadcrumb from '../components/BreadCrumbs';
import DestinationTable from '@/app/components/DestinationTours';
import ParentComponent from '@/app/components/PopularToursParents';
import TestimonialSection from '../components/Testimonials';
import CallToAction from '../components/ContactSection';
import Footer from '../components/Footer';
import { useState, useEffect } from 'react';



const Home = () => {
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // Placeholder for fetching data
    // Replace with actual data fetching logic
    setLoading(false);
    setSearchResults([]); // Replace with actual search results
  }, []);

  return (
    <main>
      <NavigationBar></NavigationBar>
      <Breadcrumb></Breadcrumb>
      <div className="px-4 pb-8 pt-2">
        <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 container mx-auto max-w-7xl  mb-4">
            Find your perfect dream destination
        </h1>
      </div>
      <DestinationTable 
        searchResults={searchResults}
        loading={loading}
        error={error}
        searchQuery={searchQuery}
      />
      <ParentComponent></ParentComponent>
      <TestimonialSection></TestimonialSection>
      <CallToAction></CallToAction>
      <Footer></Footer>
    </main>
  );
};


export default Home;