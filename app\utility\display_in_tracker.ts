export function findDisplayInFromObject(data: any, path: string[] = [], displayInTracker: any[] = []) {
  if (Array.isArray(data)) {
    data.forEach((item, index) => {
      // findDisplayInFromObject(item, [...path, `[${index}]`], displayInTracker);
      findDisplayInFromObject(item, [...path.slice(0, -1), `${path[path.length - 1]}[${index}]`], displayInTracker);

    });
  } else if (typeof data === "object" && data !== null) {
    Object.entries(data).forEach(([key, value]) => {
      const lowerKey = key.toLowerCase();
      if (lowerKey.endsWith("displayin")) {
        displayInTracker.push({
          key: key,
          path: [...path, key].join("."),
          value: value
        });
      }

      findDisplayInFromObject(value, [...path, key], displayInTracker);
    });
  }

  return displayInTracker;
}
export function getValueFromPath(obj:any, path:any) {
  debugger;
  // const keys = path.split(".").slice(0, -1); // remove 'displayIn'
  // const keys =[...path.split(".").splice(0,path.split(".").length-1),path.split(".").at(-1).replace(/displayin$/i, "")].join(".")
  const keys =[...path.split(".").splice(0,path.split(".").length-1),path.split(".").at(-1).replace(/displayin$/i, "")];
  // return keys.reduce((acc:any, key:any) => acc && acc[key], obj);
  return keys.reduce((acc:any, key:any) => {
    debugger;
    if(!acc[key]){
      if(Array.isArray(acc[key.slice(0,key.length-3)]) && !isNaN(key[key.length-2])){
        return acc[key.slice(0,key.length-3)][key[key.length-2]]
      }
    } else{
      return acc && acc[key]
    }
  }, obj);
}
