import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface CuponStore {
	cuponApplied: boolean;
	cuponData:any;
	setCuponApplied: (newData: any) => void;
	setCuponData: (newData: any) => void;
}

export const useCuponStore = create<CuponStore>()(
	persist(
		(set) => ({
			cuponApplied: false,
			cuponData: {},
			setCuponApplied: (isApplied) => set({ cuponApplied: isApplied }),
			setCuponData: (newData) => set({ cuponData: newData }),
		}),
		{
			name: 'cupon-code-store', // localStorage key
		}
	)
);