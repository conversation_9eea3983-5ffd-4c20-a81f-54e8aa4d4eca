"use client";
import axios from 'axios';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';

const getCurrencySymbol = (currencyCode?: string): string => {
  if (!currencyCode) return '₹'; // Default to Rupee if no currency code
  
  const currencySymbols: { [key: string]: string } = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    AED: 'د.إ',
    SAR: '﷼',
    SGD: 'S$',
    AUD: 'A$',
    CAD: 'C$',
    // Add more currency codes and symbols as needed
  };

  return currencySymbols[currencyCode.toUpperCase()] || '₹'; // Default to Rupee if currency code not found
};

interface Trip {
  id: number;
  title: string;
  image: string;
  duration: string;
  price: number;
  location: string;
  flag: string;
}

interface SearchResultsProps {
  date: Date | null;
  destination: string;
  onEdit: () => void;
  destinationName: string;
  travellers: number;
}

// const trips: Trip[] = [
//   {
//     id: 1,
//     title: "Paris in 5 days with included Disneyland and Louvre visit",
//     image: "/assets/tours/imagefour.png",
//     duration: "5 Days 4 Nights",
//     price: 845.57,
//     location: "France",
//     flag: "🇫🇷"
//   },
//   {
//     id: 2,
//     title: "Munich in 4 Days: Bavarian Culture & History",
//     image: "/assets/tours/imagefour.png",
//     duration: "4 Days 3 Nights",
//     price: 625.67,
//     location: "Germany",
//     flag: "🇩🇪"
//   },
//   {
//     id: 3,
//     title: "Best of Italy in one week (5 Days in Italy - Attractions of Venice & Rome)",
//     image: "/assets/tours/imagefour.png",
//     duration: "5 Days 4 Nights",
//     price: 845.57,
//     location: "Italy",
//     flag: "🇮🇹"
//   },
//   {
//     id: 4,
//     title: "Greece in 5 Days: Greek Culture, Architecture & History",
//     image: "/assets/tours/imagefour.png",
//     duration: "5 Days 4 Nights",
//     price: 845.67,
//     location: "Greece",
//     flag: "🇬🇷"
//   },{
//     id: 5,
//     title: "Greece in 5 Days: Greek Culture, Architecture & History",
//     image: "/assets/tours/imagefour.png",
//     duration: "5 Days 4 Nights",
//     price: 845.67,
//     location: "Greece",
//     flag: "🇬🇷"
//   },
//   {
//     id: 6,
//     title: "Greece in 5 Days: Greek Culture, Architecture & History",
//     image: "/assets/tours/imagefour.png",
//     duration: "5 Days 4 Nights",
//     price: 845.67,
//     location: "Greece",
//     flag: "🇬🇷"
//   },{
//     id: 7,
//     title: "Greece in 5 Days: Greek Culture, Architecture & History",
//     image: "/assets/tours/imagefour.png",
//     duration: "5 Days 4 Nights",
//     price: 845.67,
//     location: "Greece",
//     flag: "🇬🇷"
//   },{
//     id: 8,
//     title: "Greece in 5 Days: Greek Culture, Architecture & History",
//     image: "/assets/tours/imagefour.png",
//     duration: "5 Days 4 Nights",
//     price: 845.67,
//     location: "Greece",
//     flag: "🇬🇷"
//   }
// ];



export default function SearchResults({ date, destination, onEdit, destinationName = "", travellers = 1 }: SearchResultsProps) {
  const router = useRouter();
  
  // Print auth token to console
  useEffect(() => {
    const authToken = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
    console.log('Auth Token:', authToken);
  }, []);
  
  const [selectedTrips, setSelectedTrips] = useState<any>([]);
  const [trips, setTrips] = useState<any>([]);
  const [isLoadingAirport, setIsLoadingAirport] = useState(false);
  interface TransportModalState {
    isOpen: boolean;
    type: 'airport' | 'intercity' | '';
    startPackageCode: string;
    endPackageCode: string;
    isFinalTransfer?: boolean;
  }

  const [showTransportModal, setShowTransportModal] = useState<TransportModalState>({
    isOpen: false,
    type: '',
    startPackageCode: '',
    endPackageCode: '',
    isFinalTransfer: false
  });
  const [transportOptions, setTransportOptions] = useState<any[]>([]);
  const [selectedCity, setSelectedCity] = useState('');
  const [loadingTransport, setLoadingTransport] = useState(false);
  const [selectedTransport, setSelectedTransport] = useState<{[key: string]: any}>({});
  const filteredTrips = trips

  const handleIntercityTransport = async (startPackageCode: string, endPackageCode: string) => {
    setLoadingTransport(true);
    try {
      if (!startPackageCode || !endPackageCode) {
        throw new Error('Missing package codes for intercity transport');
      }
      
      const authToken = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
      if (!authToken) {
        throw new Error('Authentication token not found');
      }
      
      const url = new URL(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/packages/stitch-able/type/intercity/destination/1`);
      url.searchParams.append('startPackageCode', startPackageCode);
      url.searchParams.append('endPackageCode', endPackageCode);
      url.searchParams.append('authtoken', authToken);
      
      const response = await axios.get(url.toString());
      
      console.log('Intercity transport data:', response.data);
      setTransportOptions(response.data || []);
      setSelectedCity('Intercity Transport');
      
      // Store the package codes in the modal state to use when selecting a transport
      setShowTransportModal({
        isOpen: true,
        type: 'intercity',
        startPackageCode,
        endPackageCode
      });
    } catch (error) {
      console.error('Error fetching intercity transport options:', error);
      // Handle error (e.g., show error message to user)
    } finally {
      setLoadingTransport(false);
    }
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState('');

  // Save current state to localStorage
  const saveCurrentState = () => {
    try {
      const stateToSave = {
        selectedTrips,
        selectedTransport,
        destination,
        date: date ? date.toISOString() : null,
        travellers,
        destinationName
      };
      localStorage.setItem('pendingItinerary', JSON.stringify(stateToSave));
    } catch (error) {
      console.error('Error saving state:', error);
    }
  };

  // Restore state from localStorage on component mount
  useEffect(() => {
    const savedState = localStorage.getItem('pendingItinerary');
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState);
        // Only restore if we're on the same destination
        if (parsedState.destination === destination) {
          // Only restore if we don't already have selections
          if (selectedTrips.length === 0) {
            setSelectedTrips(parsedState.selectedTrips || []);
          }
          if (Object.keys(selectedTransport).length === 0) {
            setSelectedTransport(parsedState.selectedTransport || {});
          }
          
          // Don't clear the state here - we'll clear it after successful API call
        }
      } catch (error) {
        console.error('Error restoring state:', error);
        // Don't clear here - let the Customized component handle it
      }
    }
  }, [destination, selectedTrips.length, selectedTransport]);

  const handleLogoutAndSaveState = () => {
    // Save the current state before logging out
    const stateToSave = {
      selectedTrips,
      selectedTransport,
      destination,
      date: date ? date.toISOString() : null,
      travellers,
      destinationName
    };
    
    // Save to localStorage
    localStorage.setItem('pendingItinerary', JSON.stringify(stateToSave));
    
    // Redirect to login with current path
    const currentPath = encodeURIComponent(window.location.pathname + window.location.search);
    localStorage.removeItem('authToken');
    localStorage.removeItem('userEmail');
    delete axios.defaults.headers.common['Authorization'];
    router.push(`/login?redirect=${currentPath}`);
  };

  const handleCreateFinalItinerary = async () => {
    if (selectedTrips.length === 0) {
      setSubmitError('Please select at least one trip');
      return;
    }

    setIsSubmitting(true);
    setSubmitError('');

    try {
      const authToken = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
      if (!authToken) {
        throw new Error('Authentication token not found');
      }

      // Prepare template package items from selected transports
      const templatePackageItems = [];
      
      // Add initial airport transport if exists
      if (selectedTransport['airport-initial']) {
        const transport = selectedTransport['airport-initial'];
        templatePackageItems.push({
          excursionId: transport.id || '', // You might need to adjust this based on your data structure
          price: transport.transportDetails?.[0]?.finalPrice?.toString() || '0',
          description: transport.titleDescriptor || 'Airport Transfer',
          packageCode: transport.packageCode || ''
        });
      }

      // Add intercity transports
      Object.entries(selectedTransport).forEach(([key, transport]: [string, any]) => {
        if (key.startsWith('intercity-')) {
          templatePackageItems.push({
            excursionId: transport.id || '',
            price: transport.transportDetails?.[0]?.finalPrice?.toString() || '0',
            description: transport.titleDescriptor || 'Intercity Transfer',
            packageCode: transport.packageCode || ''
          });
        }
      });

      // Prepare final transfer if exists
      let finalTransfer = null;
      if (selectedTransport['airport-final']) {
        const transport = selectedTransport['airport-final'];
        finalTransfer = {
          packageCode: transport.packageCode || '',
          excursionId: transport.id || '',
          price: transport.transportDetails?.[0]?.finalPrice?.toString() || '0',
          description: transport.titleDescriptor || 'Final Airport Transfer'
        };
      }

      // Calculate total days and nights from selected trips
      const totalDays = selectedTrips.reduce((sum: number, trip: any) => sum + (parseInt(trip.noOfDays) || 0), 0);
      const totalNights = selectedTrips.reduce((sum: number, trip: any) => sum + (parseInt(trip.noOfNights) || 0), 0);

      // Prepare the request body
      const requestBody = {
        templatePackageDTO: {        
          itineraryType: "Quotation",
          customerName: "Sudhar", // You might want to get this from user input
          email: "<EMAIL>", // You might want to get this from user input
          mobileNo: "1234567890", // You might want to get this from user input
          noOfAdults: travellers,        
          destinationId: selectedTrips[0]?.destinationId || 0, // Assuming all trips have the same destination
          packageStartDate: date ? date.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
          priceOnWebsite: "price_per_person",
          status: "Active",
          templatePackageItems,
          ...(finalTransfer && { finalTransfer })
        },
        websitePackageInfoDTO: {
          packageTitle: selectedTrips.map((t: any) => t.packageTitle).join(' + '),
          shortDescription: `Custom itinerary with ${selectedTrips.length} destinations`,
          description: `Custom itinerary including: ${selectedTrips.map((t: any) => t.packageTitle).join(', ')}`,
          destinationId: selectedTrips[0]?.destinationId || 0,
          noOfDays: totalDays,
          noOfNights: totalNights
        }
      };

      console.log('Submitting itinerary:', requestBody);

      // Prepare URL with auth token
      const url = new URL(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/packages/web/template`);
      url.searchParams.append('authtoken', authToken || '');
      
      console.log('Submitting JSON:', requestBody);
      
      // Send as JSON
      const response = await axios.post(
        url.toString(),
        requestBody,
        {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('Raw response:', response);

      console.log('Itinerary created successfully:', response.data);
      
      // Clear the saved state after successful submission
      localStorage.removeItem('pendingItinerary');
      
      // Show success message
      toast.success('Itinerary created successfully!');
      
      // Optionally redirect to a success page or booking confirmation
      // router.push('/my-bookings');
      
    } catch (error: any) {
      console.error('Error creating itinerary:', error);
      
      // Handle 401 Unauthorized
      if (error.response?.status === 401) {
        setSubmitError('Your session has expired. Please log in again.');
        // Save state and logout
        setTimeout(() => {
          handleLogoutAndSaveState();
        }, 2000); // Give user time to see the message
        return;
      }
      
      // Handle other errors
      setSubmitError(error.response?.data?.message || error.message || 'Failed to create itinerary');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAirportTransportClick = async (isFinalTransfer: boolean = false) => {
    if (selectedTrips.length === 0) {
      console.log('No trips selected');
      return;
    }
    setLoadingTransport(true);
    try {
      // Get auth token
      const authToken = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
      if (!authToken) {
        throw new Error('Authentication token not found');
      }
      
      // Use the last package if isFinalTransfer is true, otherwise use the first one
      const selectedTrip = isFinalTransfer 
        ? selectedTrips[selectedTrips.length - 1] 
        : selectedTrips[0];
        
      const packageId = selectedTrip?.packageCode;
      if (!packageId) {
        throw new Error('No package code found in the selected trip');
      }
      
      const url = new URL(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/packages/stitch-able/type/airport/destination/1`);
      url.searchParams.append('startPackageCode', packageId);
      url.searchParams.append('isFinalTransfer', String(isFinalTransfer));
      url.searchParams.append('authtoken', authToken);
      
      const response = await axios.get(url.toString());
      console.log('Airport transport data:', response.data);
      setTransportOptions(response.data || []);
      setSelectedCity(selectedTrip?.selectDestination || 'City');
      
      // Store the package code in the modal state to use when selecting a transport
      setShowTransportModal({
        isOpen: true,
        type: 'airport',
        startPackageCode: packageId,
        endPackageCode: '',
        isFinalTransfer
      });
    } catch (error) {
      console.error('Error fetching airport transport:', error);
      // Handle error (e.g., show error message to user)
    } finally {
      setLoadingTransport(false);
    }
  };

  const handleIntercityTransportClick = async () => {
    if (selectedTrips.length === 0) {
      console.log('No trips selected');
      return;
    }
    setLoadingTransport(true);
    try {
      const packageId = selectedTrips[0]?.packageCode;
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/packages/stitch-able/type/airport/destination/1?startPackageCode=${packageId}&isFinalTransfer=true`
      );
      console.log('Intercity transport data:', response.data);
      setTransportOptions(response.data || []);
      setShowTransportModal({
        isOpen: true,
        type: 'intercity',
        startPackageCode: packageId,
        endPackageCode: '',
        isFinalTransfer: false
      });
    } catch (error) {
      console.error('Error fetching intercity transport options:', error);
    } finally {
      setLoadingTransport(false);
    }
  };

  const handleDragStart = (e: React.DragEvent, trip: any) => {
    e.dataTransfer.setData('tripId', trip?.packageCode.toString());
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent) => {
     
    e.preventDefault();
    const tripId = e.dataTransfer.getData('tripId');
    const trip = trips.find((t:any) => t.packageCode === tripId);
    if (trip && !selectedTrips.some((t:any) => t.packageCode === trip?.packageCode)) {
      setSelectedTrips([...selectedTrips, trip]);
    }
  };

  const removeTrip = (tripId: any) => {
    setSelectedTrips(selectedTrips.filter((trip:any) => trip?.packageCode !== tripId));
  };
useEffect(()=>{
  const fetchDestination = async () => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/packages/stitch-able/destination/${destination}`,
        { params: { travellers } }
      );
      console.log(response.data);
       
      if(response.status == 200) {
        setTrips(response.data); 
      } else {
        // alert("Failed to submit request. Please try again.");
      }
    } catch (error) {
      console.error("Error fetching destination:", error);
      // alert("Failed to submit request. Please try again.");
    }
  }
  fetchDestination();
},[]);
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-6">
          Showing trips according to your search
        </h2>

            <div className="max-w-2xl  relative mb-8" style={{marginTop:"48px"}}>
            <div className="bg-blue-50 rounded-xl p-6 relative">
                <div className="absolute -top-3 left-4">
                <span className="text-base font-bold text-[#000000] bg-blue-100 px-3 py-1 rounded-md">
                  Date and Destination
                  </span>
              </div>
              <div className="absolute -top-3 right-4">
                <button
                onClick={onEdit}
                className="text-blue-600 hover:text-blue-700 p-1.5 rounded-full"
                  >
                    <img src="/assets/edit_icon_new.svg"/>
                  </button>
              </div>
              <div className="mt-3 grid grid-cols-3 gap-8">
                <div>
                <div className="text-[#344054] font-medium text-sm">Travel Date</div>
                <div className="font-normal text-base text-[#101828]">
                  {date?.toLocaleDateString('en-GB', { 
                    day: '2-digit',
                    month: 'short', 
                    year: 'numeric'
                  })}
                </div>
              </div>
              <div className="absolute left-1/2 transform -translate-x-1/2">
                <div className="text-[#344054] font-medium text-sm">Destination</div>
                <div className="font-normal text-base text-[#101828]">{destinationName || 'Europe'}</div>
              </div>
              </div>
            </div>
          </div>
        <div className="flex gap-6">
          <div className="flex-1">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {trips
                .filter((trip:any) => 
                  !selectedTrips.some((selectedTrip:any) => selectedTrip.packageCode === trip?.packageCode)
                )
                .map((trip:any) => (
                <div 
                  key={trip?.packageCode} 
                  className="bg-white rounded-lg overflow-hidden flex flex-col h-full cursor-move"
                  draggable
                  onDragStart={(e) => handleDragStart(e, trip)}
                >
                  <div className="relative">
                    <img src={trip.image || "/assets/dummy_image_new.jpg"} alt={trip?.packageTitle} className="w-full h-[200px] object-cover rounded-2xl" />
                  </div>
                  <div className="p-4 flex flex-col flex-1">
                    <h3 className="text-[15px] font-bold text-gray-900 mb-2 line-clamp-2">{trip?.packageTitle}</h3>
                    <div className="flex flex-col flex-1 justify-between">
                      <div>
                        <div className="text-xs text-gray-600">Starting from</div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-baseline gap-1">
                            {/* <span className="text-gray-900 font-bold">₹{trip?.priceOnWebsite}</span> */}
                            <span className="text-gray-900 font-bold">₹{Math.round(trip?.priceSummary?.netSellingPrice).toLocaleString() || ''}</span>
                            {/* <span className="text-gray-900 text-sm font-bold">.{trip.price.toFixed(2).split('.')[1]}</span> */}
                            <span className="text-gray-600 text-sm font-bold">per person</span>
                          </div>
                          <div className="text-xs text-gray-600 font-bold">{trip.noOfDays && `${trip.noOfDays} ${trip.noOfDays > 1 ? 'Days' : 'Day'} ${trip.noOfNights && trip.noOfNights} ${trip.noOfNights && trip.noOfNights > 1 ? 'Nights' : 'Night'}`}</div>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex items-center gap-1.5">
                          {/* <img src={`/${trip.location.toLowerCase()}.png`} alt={trip.location} className="w-5 h-3.5 object-cover" />
                          <span className="text-gray-600 text-sm">{trip.location}</span> */}
                          <img src={`/${trip.selectDestination}.png`} alt={trip.selectDestination} className="w-5 h-3.5 object-cover" />
                          <span className="text-gray-600 text-sm">{trip.selectDestination}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <button className="flex items-center justify-center gap-1.5 px-4 py-1.5 text-sm text-blue-600 hover:text-blue-700 bg-blue-50 rounded-2xl flex-1 font-bold"
                          onClick={()=>{
                            router.push(`/tours/package-details?query=${trip.packageCode}&custom=true`)
                          }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-4 h-4">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
                              <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                            </svg>
                            Quick View
                          </button>
                          <button 
                            onClick={() => {
                              if (!selectedTrips.some((t:any) => t.id === trip?.packageCode)) {
                                setSelectedTrips([...selectedTrips, trip]);
                              }
                            }}
                            className="flex items-center justify-center gap-1.5 px-4 py-1.5 text-sm text-blue-600 hover:text-blue-700 bg-blue-50 rounded-2xl flex-1 font-bold"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-4 h-4">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                            </svg>
                            Select
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Custom Package Card */}
          <div className="w-[352px] shrink-0 mt-10">
            <div 
              className="bg-blue-50 rounded-lg border-2 border-blue-100 border-dashed sticky top-10"
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              {selectedTrips.length === 0 ? (
                <div className="text-center flex flex-col items-center justify-center py-16">
                  <h3 className="text-lg font-bold text-gray-900 mb-2">Your selected tours</h3>
                  <p className="text-sm text-gray-600">Select or drag and drop trips to<br />this bucket</p>
                </div>
              ) : (
                <div className="p-4 flex flex-col gap-4">
                  <div className="px-4 py-2">
                    <h3 className="text-lg font-bold text-gray-900 text-center">Your selected tours</h3>
                  </div>
                  <div className="flex flex-col items-center gap-2 w-full">
                    {/* Initial Airport Transport */}
                    <div className="w-full">
                      {
                      (() => {
                        // Find arrival transport
                        const arrivalKey = Object.keys(selectedTransport).find(key => key.startsWith('airport-initial'));
                        const arrivalTransport = arrivalKey ? selectedTransport[arrivalKey] : null;

                        if (arrivalTransport) {
                          return (
                            <div key={arrivalKey} className="w-full bg-blue-50 p-3 rounded-lg border border-blue-100 flex justify-between items-center mb-2">
                              <div className="flex items-center gap-2">
                                <div className="flex items-center gap-2">
                                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-blue-500 flex-shrink-0">
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
                                  </svg>
                                  <div className="flex flex-col">
                                    <span className="text-sm font-medium">Arrival: {arrivalTransport.titleDescriptor}</span>
                                    <span className="text-xs text-gray-600">
                                      {getCurrencySymbol(arrivalTransport.transportDetails?.[0]?.currency || '')}
                                      {arrivalTransport.transportDetails?.[0]?.finalPrice?.toLocaleString()}
                                    </span>
                                  </div>
                                </div>
                              </div>
                              <button 
                                onClick={(e) => {
                                  e.stopPropagation();
                                  const newTransports = {...selectedTransport};
                                  delete newTransports[arrivalKey as any];
                                  setSelectedTransport(newTransports);
                                }}
                                className="text-red-500 hover:text-red-600 p-1"
                                aria-label="Remove arrival transport"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            </div>
                          );
                        }
                        
                        // Show add button if no arrival transport
                        return (
                          <button 
                            className="inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-bold bg-orange-400 text-white rounded-lg hover:bg-orange-500 w-full mb-2"
                            onClick={() => handleAirportTransportClick(false)}
                            disabled={loadingTransport}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-4 h-4">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                            </svg>
                            Add Arrival Transport
                          </button>
                        );
                      })()
                    }
                    </div>
                  </div>
                  {selectedTrips.map((trip:any, index:any) => (
                    <>
                      <div key={trip?.packageCode} className="rounded-lg p-3 border border-blue-300 bg-white">
                        <div className="flex justify-between items-start">
                          <h4 className="text-sm font-medium text-gray-900 flex-1">{trip?.packageTitle}</h4>
                        </div>
                        <div className="mt-2">
                          <div className="text-xs text-gray-600">Starting from</div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-baseline gap-1">
                              {/* <span className="text-gray-900 font-bold">₹ {Math.floor(trip.price).toLocaleString()}</span> */}
                              <span className="text-gray-900 font-bold">₹ {Math.round(trip?.priceSummary?.netSellingPrice).toLocaleString()}</span>
                              {/* <span className="text-gray-900 text-sm font-bold">.{trip.price.toFixed(2).split('.')[1]}</span> */}
                              <span className="text-gray-600 text-sm">per person</span>
                            </div>
                            {/* <div className="text-xs text-gray-600 font-bold">{trip.duration}</div> */}
                            <div className="text-xs text-gray-600 font-bold">{trip.noOfDays && `${trip.noOfDays} ${trip.noOfDays > 1 ? 'Days' : 'Day'} ${trip.noOfNights && trip.noOfNights} ${trip.noOfNights && trip.noOfNights > 1 ? 'Nights' : 'Night'}`}</div>
                          </div>
                          <div className="flex items-center justify-between mt-2">
                            <div className="flex items-center gap-1">
                              <img src={`/${trip.selectDestination.toLowerCase()}.png`} alt={trip.selectDestination} className="w-4 h-3 object-cover" />
                              {/* <span className="text-gray-600 text-sm">{trip.location}</span> */}
                              <span className="text-gray-600 text-sm">{trip.selectDestination}</span>
                            </div>
                            <button 
                              onClick={() => removeTrip(trip?.packageCode)}
                              className="text-red-500 hover:text-red-600"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                      {index < selectedTrips.length - 1 && (
                        <div className="flex flex-col items-center gap-2">
                          {Object.entries(selectedTransport)
                            .filter(([key]) => key.startsWith(`intercity-${selectedTrips[index]?.packageCode}-`))
                            .map(([key, transport]: [string, any]) => (
                              <div key={key} className="w-full bg-blue-50 p-3 rounded-lg border border-blue-100 flex justify-between items-center">
                                <div className="flex items-center gap-2">
                                  <div className="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-blue-500 flex-shrink-0">
                                      <path strokeLinecap="round" strokeLinejoin="round" d="M8 7h8v10H8V7z" />
                                      <path strokeLinecap="round" strokeLinejoin="round" d="M6 17h12v2H6v-2z" />
                                      <path strokeLinecap="round" strokeLinejoin="round" d="M16 7l1-3h-3.5l-1-3h-4l-1 3H7l1 3h8z" />
                                    </svg>
                                    <div className="flex flex-col">
                                      <span className="text-sm font-medium">{transport.titleDescriptor}</span>
                                      <span className="text-xs text-gray-600">
                                        {getCurrencySymbol(transport.transportDetails[0]?.currency)}
                                        {transport.transportDetails[0]?.finalPrice?.toLocaleString()}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                                <button 
                                  onClick={() => {
                                    const newTransports = {...selectedTransport};
                                    delete newTransports[key];
                                    setSelectedTransport(newTransports);
                                  }}
                                  className="text-red-500 hover:text-red-600"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                                  </svg>
                                </button>
                              </div>
                            ))
                          }
                          {!Object.keys(selectedTransport).some(k => k.startsWith(`intercity-${selectedTrips[index]?.packageCode}-`)) && (
                            <button 
                              onClick={(e) => {
                                e.stopPropagation();
                                const startPackageCode = selectedTrips[index]?.packageCode;
                                const endPackageCode = selectedTrips[index + 1]?.packageCode;
                                if (startPackageCode && endPackageCode) {
                                  handleIntercityTransport(startPackageCode, endPackageCode);
                                }
                              }}
                              className="inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-bold bg-orange-400 text-white rounded-lg hover:bg-orange-500"
                              disabled={loadingTransport}
                            >
                              {loadingTransport ? (
                                ''
                              ) : (
                                <>
                                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-4 h-4">
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                                  </svg>
                                  Add Intercity Transport
                                </>
                              )}
                            </button>
                          )}
                        </div>
                      )}
                    </>
                  ))}
                  <div className="flex flex-col items-center gap-2 w-full">
                    {(() => {
                      // Find departure transport
                      const departureKey = Object.keys(selectedTransport).find(key => key.startsWith('airport-final'));
                      const departureTransport = departureKey ? selectedTransport[departureKey] : null;

                      if (departureTransport) {
                        return (
                          <div key={departureKey} className="w-full bg-blue-50 p-3 rounded-lg border border-blue-100 flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <div className="flex items-center gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-blue-500 flex-shrink-0">
                                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
                                </svg>
                                <div className="flex flex-col">
                                  <span className="text-sm font-medium">Departure: {departureTransport.titleDescriptor}</span>
                                  <span className="text-xs text-gray-600">
                                    {getCurrencySymbol(departureTransport.transportDetails?.[0]?.currency || '')}
                                    {departureTransport.transportDetails?.[0]?.finalPrice?.toLocaleString()}
                                  </span>
                                </div>
                              </div>
                            </div>
                            <button 
                              onClick={(e) => {
                                e.stopPropagation();
                                const newTransports = {...selectedTransport};
                                delete newTransports[departureKey as any];
                                setSelectedTransport(newTransports);
                              }}
                              className="text-red-500 hover:text-red-600 p-1"
                              aria-label="Remove departure transport"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        );
                      }
                      
                      // Show add button if no departure transport
                      if (selectedTrips.length > 0) {
                        return (
                          <button 
                            className="inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-bold bg-orange-400 text-white rounded-lg hover:bg-orange-500 w-full"
                            onClick={() => handleAirportTransportClick(true)}
                            disabled={loadingTransport}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-4 h-4">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                            </svg>
                            Add Departure Transport
                          </button>
                        );
                      }
                      
                      return null;
                    })()
                    }
                    </div>
                    
                    <button 
                      onClick={handleCreateFinalItinerary}
                      className="w-full bg-blue-600 text-white font-bold py-2 px-4 rounded-2xl mt-4 hover:bg-blue-700 transition-colors"
                    >
                      Create Final Itinerary
                    </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Airport Transport Modal */}
      {showTransportModal.isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] flex flex-col">
            {/* Modal Header */}
            <div className="p-6 border-b relative">
              <button 
                onClick={() => setShowTransportModal(prev => ({ ...prev, isOpen: false }))}
                className="absolute top-6 right-6 text-gray-400 hover:text-gray-600 transition-colors"
                aria-label="Close modal"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              <div className="flex flex-col items-center text-center px-8">
                <h3 className="text-2xl font-bold text-gray-900">Airport Transport</h3>
                <p className="text-gray-600 mt-1">Select your airport transport</p>
                <p className="text-blue-600 font-medium mt-2">Airport transport - {selectedCity}</p>
              </div>
            </div>
            
            {/* Modal Body */}
            <div className="overflow-y-auto p-6">
              {transportOptions.length > 0 ? (
                <div className="space-y-4">
                  {transportOptions.map((option: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4 relative">
                      <div className="absolute top-4 right-4">
                        <button 
                          className="px-4 py-2 bg-blue-600 text-white text-sm rounded-xl hover:bg-blue-700 transition-colors whitespace-nowrap"
                          onClick={() => {
                            const handleTransportSelect = (transport: any, isAirport: boolean = true) => {
                              const isFinal = showTransportModal.isFinalTransfer;
                              const transportKey = isAirport 
                                ? isFinal ? 'airport-final' : 'airport-initial'
                                : `intercity-${transport.packageCode}`;
                              
                              setSelectedTransport(prev => ({
                                ...prev,
                                [transportKey]: {
                                  ...transport,
                                  type: isAirport ? 'airport' : 'intercity',
                                  isFinalTransfer: isFinal,
                                  selectedAt: new Date().toISOString()
                                }
                              }));
                              
                              setShowTransportModal(prev => ({ ...prev, isOpen: false }));
                            };
                            handleTransportSelect(option);
                          }}
                        >
                          Select
                        </button>
                      </div>
                      <div className="flex items-start gap-4 pr-20">
                        <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center flex-shrink-0">
                          <svg width="24" height="24" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20.9987 19.8333H24.4987C24.8081 19.8333 25.1049 19.7104 25.3237 19.4916C25.5425 19.2728 25.6654 18.9761 25.6654 18.6666V14.7361C25.6654 14.5011 25.5945 14.2716 25.4619 14.0776C25.3293 13.8836 25.1412 13.7341 24.9222 13.6488L19.832 11.6666L16.332 5.83331H6.9987L4.66536 11.6666H3.4987C3.18928 11.6666 2.89253 11.7896 2.67374 12.0084C2.45495 12.2271 2.33203 12.5239 2.33203 12.8333V18.6666C2.33203 18.9761 2.45495 19.2728 2.67374 19.4916C2.89253 19.7104 3.18928 19.8333 3.4987 19.8333H6.9987" stroke="#175CD3" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M18.6654 22.1667C19.2842 22.1667 19.8777 21.9208 20.3153 21.4832C20.7529 21.0457 20.9987 20.4522 20.9987 19.8333C20.9987 19.2145 20.7529 18.621 20.3153 18.1834C19.8777 17.7458 19.2842 17.5 18.6654 17.5C18.0465 17.5 17.453 17.7458 17.0154 18.1834C16.5779 18.621 16.332 19.2145 16.332 19.8333C16.332 20.4522 16.5779 21.0457 17.0154 21.4832C17.453 21.9208 18.0465 22.1667 18.6654 22.1667Z" stroke="#175CD3" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M9.33333 22.1667C8.71449 22.1667 8.121 21.9208 7.68342 21.4832C7.24583 21.0457 7 20.4522 7 19.8333C7 19.2145 7.24583 18.621 7.68342 18.1834C8.121 17.7458 8.71449 17.5 9.33333 17.5C9.95217 17.5 10.5457 17.7458 10.9832 18.1834C11.4208 18.621 11.6667 19.2145 11.6667 19.8333C11.6667 20.4522 11.4208 21.0457 10.9832 21.4832C10.5457 21.9208 9.95217 22.1667 9.33333 22.1667Z" stroke="#175CD3" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M16.3346 19.8333H11.668" stroke="#175CD3" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{option.titleDescriptor}</h4>
                          {option.transportDetails?.[0]?.finalPrice && (
                            <p className="text-gray-600 font-medium">
                              {getCurrencySymbol(option.transportDetails[0].currency)}
                              {option.transportDetails[0].finalPrice}
                            </p>
                          )}
                          {option.transportDetails?.[0]?.description && (
                            <p className="text-sm text-gray-500 mt-1">
                              {option.transportDetails[0].description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No transport options available
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 