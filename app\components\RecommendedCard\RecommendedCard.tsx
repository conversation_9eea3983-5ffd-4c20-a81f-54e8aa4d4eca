import { useCurrencyStore } from '@/app/store/useCurrencyStore';
import React, { useEffect, useRef, useState } from 'react';

const RecommendedCard = ({tour,handleBookNow}:any) => {
	 const containerRef = useRef<HTMLDivElement>(null);
	 	const convertFromINR = useCurrencyStore(state => state.convertFromINR);
		const symbols = useCurrencyStore(state => state.symbols);
		const selectedCurrency = useCurrencyStore(state => state.selectedCurrency);
		const pillsRef = useRef<HTMLDivElement[]>([]);
		const [visibleCount, setVisibleCount] = useState(tour.country.split(',').length);
	
		const countries = tour.country.split(',').map((c: string) => c.trim());
	
		useEffect(() => {
			const containerWidth = containerRef.current?.offsetWidth || 0;
	
			// Get widths of each pill
			const pillWidths = pillsRef.current.map(pill => pill?.offsetWidth || 0);
	
			let total = 0;
			let countThatFit = 0;
	
			// We'll reserve space for "+X more" pill only if overflow happens
			// Let's assume approx width of that pill is 60px
	
			const morePillWidth = 70;
	
			for (let i = 0; i < pillWidths.length; i++) {
				total += pillWidths[i] + 8; // 8px gap approx
				if (total + (i < pillWidths.length - 1 ? morePillWidth : 0) <= containerWidth) {
					countThatFit = i + 1;
				} else {
					break;
				}
			}
	
			setVisibleCount(countThatFit);
		}, [tour.country]);
	
		const hiddenCount = countries.length - visibleCount;
		const max_discount_rule = tour?.applicableDiscountRules ? [...tour?.applicableDiscountRules || []].sort((a:any,b:any)=>b?.discountPercentage-a?.discountPercentage)[0] :null;
		let price_to_show = max_discount_rule? tour.netSellingPrice - (tour.netSellingPrice*(max_discount_rule?.discountPercentage/100)): tour.netSellingPrice;
		price_to_show = convertFromINR(price_to_show);
	return (
		<div className="px-4"> {/* more spacing */}
		<div className="bg-white rounded-2xl overflow-hidden h-[460px] flex flex-col justify-between">
			{tour.packageMainImage ? (
				<img
					src={tour.packageMainImage}
					alt={tour.packageTitle}
					className="w-full h-52 object-cover rounded-2xl"
					loading="lazy"
				/>
			) : (
				<div className="w-full h-52 bg-gray-200 flex items-center justify-center rounded-2xl">
					<span className="text-gray-500">No image available</span>
				</div>
			)}
			<div className="p-4 px-0 flex flex-col flex-1">
				<div>
					<h3 className="text-[16px] font-bold leading-[24px] line-clamp-2 min-h-[48px] mb-2 text-gray-800">
						{tour.packageTitle}
					</h3>
					<div className="flex justify-between items-end mt-2 text-gray-600">
						<div>
							<p className="text-[12px] text-[#667085]">Starting from</p>
							{convertFromINR(tour?.grossSellingPrice) > price_to_show? (
                  <p className="text-xs text-[#98A2B3] line-through decoration-[#98A2B3] my-1">
                    {symbols[selectedCurrency]}{' '}
                    {Math.round(convertFromINR(tour?.grossSellingPrice)).toLocaleString()}
                  </p>
                ) :
								<p className='text-xs text-[#98A2B3] line-through decoration-[#98A2B3] my-6'>
								</p>
								}
							<div className="flex items-center gap-2 text-sm">
								<p className="font-semibold text-black">{symbols[`${selectedCurrency}`]} {Math.round(price_to_show).toLocaleString() || Math.round(tour.priceSummary) || '₹10,000'}</p>
								<p className="text-[14px] font-semibold text-black">per person</p>
							</div>
						</div>
						<div className="text-right text-black">
							<p className="text-[12px]">
								<span className="font-bold">{tour.noOfDays}</span> Days{" "}
								<span className="font-bold">{tour.noOfNights}</span> Nights
							</p>
						</div>
					</div>
				{/* Countries pills container */}
          <div
            className="mt-3 flex flex-wrap gap-2 overflow-hidden"
            ref={containerRef}
            style={{ minHeight: '30px' }}
          >
            {countries.slice(0, visibleCount).map((country:any, index:any) => (
              <span
                key={index}
                className="inline-flex items-center bg-[#F2F4F7] text-[#344054] px-3 py-1 rounded-full text-[12px] font-medium leading-[18px]"
                ref={(el:any) => (pillsRef.current[index] = el!)}
              >
                <img
                  src={`/assets/flags/${country.split(' ').join('-').toLowerCase()}.svg`}
                  className="w-3 h-2.5 mr-1"
                  alt={country}
									loading="lazy"
                />{' '}
                {country}
              </span>
            ))}

            {hiddenCount > 0 && (
              <span className="inline-flex items-center bg-[#F2F4F7] text-[#344054] px-3 py-1 rounded-full text-[12px] font-medium leading-[18px] cursor-pointer">
                +{hiddenCount} more
              </span>
            )}
          </div>
				</div>
				<button
					onClick={() => handleBookNow(tour.packageCode)}
					className="bg-[#EFF8FF] text-[#175CD3] w-full font-semibold text-[14px] mt-2 py-2 rounded-full shadow-sm hover:bg-blue-200 transition"
				>
					Book Now
				</button>
			</div>
		</div>
	</div>
	)
}

export default RecommendedCard