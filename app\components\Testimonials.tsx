"use client"

import React, { useState, useEffect } from "react";
import axios from "axios";

const testimonials = [
  {
    name: "<PERSON>",
    location: "New York, USA",
    stars: 5,
    review: "It was sheer delight booking our trip with WiseYatra. The customization they did was so personalized, we are glad we chose them. We were surprised with the anniversary complimentary cake at our stay, even without us informing them. Their care and attention to detail remains unmatched to any other travel agents we have booked.The local recommendations and tips were so relevant and thoughtful (My wife is a vegetarian, we were recommended vegetarian restaurants too, besides recommending the must eat local dishes for me). To top it all, this was so much value for money, I couldn&apos;t have planned so many great perks so economically. Highly recommended. Will definitely travel with them again.",
    photo: "/assets/customer.png",
  },
  {
    name: "<PERSON>",
    location: "London, UK",
    stars: 4,
    review: "Great experience, I would definitely use this service again! I had a smooth interaction with the team.",
    photo: "/assets/customer.png",
  },
  {
    name: "<PERSON>",
    location: "Sydney, Australia",
    stars: 5,
    review: "Fantastic customer service and high-quality product! I'm extremely happy with my purchase and will return.",
    photo: "/assets/customer.png",
  },
];

const TestimonialSection: React.FC = () => {
  const NEXT_PUBLIC_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
  const [testimonials, setTestimonials] = useState<any[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const response = await axios.get(`${NEXT_PUBLIC_API_BASE_URL}/api/testimonials`);
        setTestimonials(response.data);
        setLoading(false);
      } catch (err) {
        setError("Failed to load testimonials");
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    if (!isHovered && testimonials.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex((prevIndex) => 
          prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
        );
      }, 5000); // Change slide every 5 seconds
  
      return () => clearInterval(interval);
    }
  }, [isHovered, testimonials.length]);

  const handleIndicatorClick = (index: number) => {
    setCurrentIndex(index);
  };

  if (loading) return <p>Loading testimonials...</p>;
  if (error) return <p className="text-red-500">{error}</p>;

  return (
    <div className="w-full py-8 sm:py-10 lg:py-10 overflow-hidden">
      {/* Content */}
      <div className="max-w-7xl mx-auto px-4  sm:py-10 lg:py-10 sm:px-6 lg:px-8 static z-30 bg-white">
        <div className="text-center mb-8 sm:mb-12">
          {/* <p className="text-black font-medium uppercase tracking-wide text-sm sm:text-base mb-2"> */}
          <p className="text-[#175CD3] text-sm sm:text-base lg:text-lg leading-relaxed font-semibold uppercase tracking-wide">
            TESTIMONIALS
          </p>
          <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight sm:leading-snug lg:leading-[60px] text-gray-900">
            Our customers love us
          </h2>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="relative">
            <div className="bg-white rounded-2xl relative sm:rounded-2xl p-4 sm:p-6 lg:px-20 lg:py-12 border border-blue-100 px-8  z-10 overflow-hidden" onMouseEnter={() => setIsHovered(true)}
  onMouseLeave={() => setIsHovered(false)}>
              <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 sm:gap-6">
                <img
                  src={testimonials[currentIndex]?.pictureUrl || "/assets/customer.png"}
                  alt={testimonials[currentIndex]?.name}
                  className="w-24 h-24 rounded-full object-cover"
                />
                <div className="flex-1 text-left">
                  <div className="flex flex-col  sm:gap-2 mb-4">
                    <h3 className="text-base sm:text-lg font-semibold text-black">
                      {testimonials[currentIndex]?.name}
                    </h3>
                    <span className="text-gray-600 text-sm sm:text-base block">
                      {testimonials[currentIndex]?.city}
                    </span>
                  </div>
                  <div className="flex items-center justify-center sm:justify-start gap-1 mb-3 sm:mb-4">
                    {Array.from({ length: testimonials[currentIndex]?.rating || 5 }).map((_, i) => (
                        <svg key={i} width="25" height="25" style={{ paddingRight: "5px" }} viewBox="0 0 20 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M15.9189 11.82C15.6599 12.071 15.5409 12.434 15.5999 12.79L16.4889 17.71C16.5639 18.127 16.3879 18.549 16.0389 18.79C15.6969 19.04 15.2419 19.07 14.8689 18.87L10.4399 16.56C10.2859 16.478 10.1149 16.434 9.93989 16.429H9.66889C9.57489 16.443 9.48289 16.473 9.39889 16.519L4.96888 18.84C4.74988 18.95 4.50188 18.989 4.25888 18.95C3.66688 18.838 3.27188 18.274 3.36888 17.679L4.25888 12.759C4.31788 12.4 4.19888 12.035 3.93988 11.78L0.328876 8.27998C0.0268757 7.98698 -0.0781242 7.54698 0.0598758 7.15C0.193876 6.754 0.535876 6.465 0.948876 6.4L5.91888 5.679C6.29688 5.64 6.62888 5.41 6.79888 5.07L8.98889 0.58C9.04089 0.48 9.10789 0.388 9.18889 0.31L9.27889 0.24C9.32589 0.188 9.37988 0.145 9.43989 0.11L9.54889 0.0700002L9.71889 0H10.1399C10.5159 0.039 10.8469 0.264 11.0199 0.6L13.2389 5.07C13.3989 5.397 13.7099 5.624 14.0689 5.679L19.0389 6.4C19.4589 6.46 19.8099 6.75 19.9489 7.15C20.0799 7.55098 19.9669 7.99098 19.6589 8.27998L15.9189 11.82Z" fill="#FACD49"/>
                      </svg>
                    ))}
                  </div>
                  
                </div>
              </div>
              <p className="text-black text-base font-normal leading-6 font-manrope">
                {testimonials[currentIndex]?.testimonial}
              </p>
            </div>
            {/* ================================================================================= */}
           {/* Right Airplane with Path */}
           {/* <div className="absolute right-48 -bottom-2 hidden sm:block z-0"> */}
          <div className="absolute hidden sm:block z-0 right-[-126px] top-[56px]">
          <svg width="306" height="299" viewBox="0 0 306 299" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M264.839 102.514C265.133 102.297 265.516 102.301 265.807 102.466C265.92 102.53 266.024 102.611 266.093 102.715C267.154 104.195 268.164 105.69 269.124 107.198C269.392 107.629 269.271 108.18 268.84 108.448C268.41 108.716 267.858 108.595 267.59 108.164C266.67 106.7 265.676 105.215 264.638 103.769C264.351 103.37 264.44 102.801 264.839 102.514ZM198.088 226.211C197.62 226.394 197.4 226.931 197.592 227.383C197.775 227.851 198.313 228.071 198.764 227.879C200.448 227.212 202.1 226.526 203.72 225.822C204.171 225.63 204.392 225.093 204.199 224.641C204.118 224.445 203.984 224.305 203.823 224.213C203.58 224.075 203.29 224.059 203.018 224.161C201.408 224.849 199.756 225.535 198.097 226.195L198.088 226.211ZM270.25 112.736C271.09 114.305 271.869 115.903 272.576 117.481C272.784 117.942 273.314 118.137 273.766 117.945C274.227 117.736 274.422 117.206 274.23 116.754C273.51 115.126 272.707 113.492 271.844 111.889C271.76 111.735 271.633 111.62 271.488 111.537C271.23 111.39 270.898 111.372 270.614 111.532C270.177 111.775 269.998 112.315 270.241 112.752L270.25 112.736ZM217.118 218.626C217.335 219.07 217.9 219.242 218.329 219.015C219.955 218.187 221.54 217.357 223.092 216.508C223.53 216.265 223.692 215.716 223.45 215.278C223.366 215.124 223.239 215.009 223.094 214.926C222.835 214.779 222.503 214.761 222.22 214.921C220.677 215.753 219.108 216.593 217.508 217.415C217.063 217.632 216.875 218.188 217.118 218.626ZM276.054 127.416C276.186 127.898 276.678 128.199 277.169 128.051C277.65 127.918 277.952 127.427 277.803 126.936C277.355 125.205 276.824 123.47 276.217 121.756C276.145 121.544 275.988 121.369 275.81 121.268C275.584 121.139 275.319 121.117 275.056 121.202C274.581 121.36 274.345 121.888 274.503 122.363C275.096 124.027 275.604 125.727 276.038 127.407L276.054 127.416ZM178.544 235.15C180.272 234.594 181.959 234.035 183.646 233.476C184.12 233.319 184.373 232.799 184.215 232.325C184.143 232.113 183.986 231.938 183.809 231.837C183.599 231.717 183.317 231.686 183.08 231.764C181.409 232.332 179.723 232.891 178.02 233.44C177.545 233.598 177.277 234.108 177.434 234.583C177.592 235.057 178.102 235.326 178.577 235.169L178.544 235.15ZM250.378 88.1262C251.712 89.3134 253.004 90.5399 254.253 91.7641C254.6 92.1324 255.192 92.106 255.535 91.7664C255.887 91.4107 255.877 90.8275 255.512 90.4915C254.255 89.2419 252.923 88.0132 251.581 86.8006C251.533 86.773 251.477 86.7201 251.429 86.6925C251.073 86.4903 250.587 86.5557 250.304 86.8652C249.968 87.23 250.004 87.8064 250.369 88.1423L250.378 88.1262ZM187.558 231.148C187.725 231.606 188.244 231.859 188.719 231.701C190.417 231.085 192.099 230.459 193.756 229.841C194.223 229.658 194.451 229.145 194.293 228.671C194.212 228.475 194.071 228.309 193.893 228.208C193.667 228.079 193.386 228.048 193.114 228.15C191.474 228.777 189.801 229.387 188.103 230.003C187.644 230.17 187.392 230.689 187.549 231.164L187.558 231.148ZM156.831 240.46C156.964 240.942 157.455 241.243 157.937 241.11C159.693 240.655 161.407 240.198 163.122 239.741C163.603 239.608 163.888 239.108 163.756 238.626C163.703 238.382 163.529 238.198 163.335 238.087C163.142 237.977 162.886 237.938 162.657 238.001C160.959 238.467 159.245 238.925 157.498 239.364C157.016 239.496 156.715 239.988 156.847 240.469L156.831 240.46ZM257.928 95.5662C259.138 96.8966 260.298 98.2409 261.4 99.5738C261.712 99.9652 262.284 100.013 262.676 99.7006C263.067 99.3886 263.114 98.8168 262.802 98.4254C261.693 97.0671 260.51 95.6883 259.268 94.3395C259.205 94.2612 259.124 94.2153 259.044 94.1693C258.704 93.9763 258.278 94.0116 257.988 94.2957C257.62 94.6422 257.598 95.2071 257.928 95.5662ZM172.891 235.013C171.206 235.53 169.506 236.038 167.773 236.528C167.291 236.66 167.023 237.17 167.139 237.642C167.271 238.124 167.781 238.393 168.253 238.277C169.996 237.771 171.712 237.272 173.397 236.755C173.871 236.597 174.147 236.113 173.999 235.622C173.936 235.394 173.779 235.219 173.585 235.108C173.375 234.989 173.119 234.95 172.882 235.029L172.891 235.013ZM270.486 172.794C270.049 172.546 269.517 172.692 269.253 173.119C268.389 174.638 267.435 176.127 266.448 177.597C266.168 178.015 266.279 178.57 266.697 178.851C267.108 179.106 267.67 179.02 267.951 178.602C268.972 177.108 269.937 175.562 270.836 174.02C271.084 173.584 270.947 173.036 270.511 172.787L270.486 172.794ZM273.842 163.626C273.222 165.241 272.511 166.868 271.734 168.458C271.518 168.912 271.706 169.447 272.161 169.663C272.615 169.879 273.15 169.691 273.366 169.236C274.16 167.614 274.884 165.93 275.531 164.266C275.687 163.842 275.529 163.367 275.141 163.146C275.109 163.128 275.061 163.101 275.028 163.082C274.555 162.899 274.035 163.137 273.86 163.594L273.842 163.626ZM264.595 181.718C264.595 181.718 264.514 181.672 264.482 181.654C264.094 181.433 263.59 181.531 263.32 181.891C262.265 183.258 261.133 184.645 259.962 185.989C259.642 186.363 259.678 186.94 260.052 187.26C260.426 187.579 261.002 187.544 261.322 187.17C262.511 185.793 263.669 184.399 264.743 183C265.056 182.6 264.969 182.038 264.586 181.734L264.595 181.718ZM257.656 189.853C257.656 189.853 257.546 189.747 257.481 189.71C257.126 189.508 256.681 189.576 256.391 189.86C255.192 191.102 253.901 192.357 252.596 193.56C252.227 193.907 252.205 194.472 252.552 194.84C252.898 195.208 253.463 195.23 253.832 194.884C255.155 193.648 256.455 192.377 257.689 191.112C258.041 190.756 258.031 190.173 257.666 189.837L257.656 189.853ZM213.558 219.808C213.316 219.67 212.993 219.636 212.719 219.779C211.138 220.527 209.533 221.282 207.886 222.034C207.425 222.242 207.23 222.773 207.438 223.234C207.647 223.694 208.177 223.889 208.638 223.681C210.301 222.938 211.907 222.183 213.505 221.403C213.95 221.186 214.145 220.655 213.937 220.195C213.863 220.024 213.729 219.884 213.567 219.792L213.558 219.808ZM277.786 137.79C277.826 138.284 278.256 138.656 278.765 138.625C279.258 138.585 279.631 138.156 279.6 137.646C279.463 135.857 279.236 134.039 278.94 132.266C278.889 131.981 278.718 131.755 278.492 131.626C278.314 131.525 278.1 131.489 277.89 131.519C277.394 131.601 277.061 132.074 277.152 132.553C277.45 134.284 277.663 136.052 277.795 137.774L277.786 137.79ZM241.504 203.845C241.419 203.732 241.331 203.66 241.218 203.596C240.911 203.421 240.535 203.443 240.249 203.644C238.838 204.659 237.385 205.671 235.891 206.681C235.476 206.959 235.371 207.519 235.649 207.934C235.926 208.348 236.486 208.453 236.901 208.176C238.421 207.159 239.899 206.139 241.32 205.108C241.718 204.822 241.807 204.252 241.52 203.854L241.504 203.845ZM275.534 158.622C275.392 159.098 275.656 159.611 276.14 159.737C276.616 159.879 277.113 159.606 277.255 159.131C277.773 157.394 278.21 155.611 278.562 153.865C278.633 153.478 278.445 153.093 278.122 152.909C278.042 152.863 277.961 152.817 277.862 152.804C277.384 152.703 276.901 153.027 276.8 153.504C276.45 155.209 276.025 156.934 275.534 158.622ZM244.341 201.716C244.653 202.107 245.215 202.171 245.597 201.875C247.032 200.745 248.424 199.613 249.776 198.478C250.151 198.157 250.198 197.585 249.877 197.21C249.815 197.132 249.727 197.061 249.63 197.005C249.307 196.822 248.89 196.841 248.593 197.1C247.267 198.227 245.899 199.352 244.474 200.466C244.083 200.778 244.019 201.341 244.315 201.723L244.341 201.716ZM277.621 148.304C277.568 148.809 277.92 149.244 278.425 149.296C278.93 149.348 279.365 148.997 279.417 148.492C279.605 146.695 279.719 144.878 279.751 143.057C279.752 142.716 279.577 142.423 279.286 142.258C279.156 142.184 279.009 142.143 278.859 142.143C278.352 142.133 277.956 142.528 277.945 143.035C277.909 144.79 277.806 146.549 277.63 148.288L277.621 148.304ZM226.515 213.536C226.758 213.974 227.316 214.121 227.763 213.862C229.336 212.939 230.867 212.014 232.374 211.096C232.804 210.828 232.925 210.276 232.657 209.846C232.581 209.717 232.47 209.611 232.341 209.537C232.066 209.381 231.709 209.37 231.416 209.546C229.926 210.473 228.404 211.382 226.856 212.298C226.419 212.541 226.272 213.099 226.54 213.53L226.515 213.536ZM121.228 46.331C123.017 46.3439 124.787 46.3891 126.558 46.4343C127.065 46.4449 127.477 46.0592 127.488 45.5519C127.49 45.2107 127.307 44.8929 127.032 44.7366C126.903 44.6631 126.755 44.6218 126.606 44.6221C124.835 44.5769 123.039 44.5387 121.234 44.5166C120.742 44.5152 120.321 44.9171 120.32 45.4082C120.318 45.8992 120.72 46.3204 121.211 46.3218L121.228 46.331ZM142.542 47.3598C144.331 47.5226 146.095 47.6922 147.85 47.8781C148.355 47.9302 148.79 47.579 148.842 47.074C148.878 46.7097 148.689 46.3665 148.382 46.1918C148.285 46.1367 148.163 46.0885 148.038 46.0818C146.267 45.8867 144.478 45.724 142.688 45.5612C142.199 45.5183 141.755 45.8857 141.712 46.3745C141.669 46.8634 142.037 47.3077 142.525 47.3506L142.542 47.3598ZM131.891 46.646C133.685 46.7257 135.46 46.8378 137.211 46.9568C137.709 46.9835 138.137 46.607 138.18 46.1181C138.207 45.7699 138.008 45.4429 137.717 45.2775C137.604 45.2131 137.472 45.1811 137.332 45.1652C135.556 45.0531 133.781 44.9411 131.971 44.8521C131.473 44.8254 131.061 45.2111 131.034 45.7092C131.007 46.2072 131.384 46.6354 131.891 46.646ZM174.161 52.114C175.914 52.4912 177.649 52.9008 179.358 53.3173C179.842 53.4432 180.333 53.1449 180.442 52.6513C180.539 52.2569 180.344 51.8469 180.005 51.6538C179.94 51.6171 179.859 51.5711 179.776 51.5666C178.051 51.1409 176.307 50.7476 174.538 50.3611C174.045 50.2514 173.57 50.5589 173.461 51.0525C173.351 51.546 173.658 52.0204 174.152 52.1302L174.161 52.114ZM153.144 48.4956C154.922 48.716 156.682 48.9687 158.432 49.2376C158.928 49.3059 159.396 48.973 159.464 48.4772C159.525 48.1059 159.329 47.7374 159.022 47.5627C158.925 47.5076 158.819 47.4686 158.703 47.4457C156.953 47.1769 155.168 46.9311 153.39 46.7107C152.894 46.6424 152.434 47.0006 152.391 47.4895C152.323 47.9853 152.681 48.4457 153.17 48.4886L153.144 48.4956ZM163.69 50.0696C165.464 50.373 167.203 50.6995 168.942 51.026C169.428 51.1105 169.903 50.803 170.013 50.3094C170.083 49.9219 169.896 49.5373 169.573 49.3534C169.492 49.3074 169.411 49.2615 169.287 49.2548C167.532 48.9191 165.777 48.5833 164.003 48.2799C163.516 48.1955 163.049 48.5284 162.965 49.015C162.88 49.5016 163.213 49.969 163.7 50.0534L163.69 50.0696ZM110.551 46.415C112.351 46.3702 114.117 46.3486 115.883 46.3269C116.374 46.3283 116.78 45.9172 116.781 45.4261C116.783 45.0849 116.598 44.8086 116.323 44.6523C116.194 44.5787 116.03 44.5283 115.871 44.5448C114.089 44.5572 112.297 44.5858 110.497 44.6306C109.997 44.6453 109.608 45.0656 109.606 45.5567C109.621 46.057 110.041 46.4459 110.533 46.4473L110.551 46.415ZM89.2457 47.7009C91.0275 47.5386 92.8002 47.3924 94.5729 47.2463C95.0662 47.2062 95.4389 46.7767 95.408 46.2672C95.3913 45.9583 95.1972 45.6981 94.9548 45.5602C94.8095 45.4775 94.6203 45.4341 94.4451 45.4413C92.6724 45.5875 90.8836 45.7244 89.0926 45.9029C88.5992 45.943 88.2335 46.3978 88.2736 46.8911C88.3137 47.3844 88.7685 47.7502 89.2618 47.7101L89.2457 47.7009ZM77.6289 48.084C77.6943 48.5703 78.1492 48.9361 78.6355 48.8706C80.4126 48.6414 82.1714 48.4446 83.9301 48.2477C84.4327 48.1915 84.7892 47.7528 84.733 47.2503C84.6909 46.9484 84.5291 46.7066 84.2868 46.5687C84.1252 46.4767 83.9361 46.4333 83.7355 46.4475C81.9768 46.6443 80.1927 46.8482 78.4155 47.0773C77.9292 47.1428 77.5634 47.5976 77.6289 48.084ZM99.8957 46.8746C101.691 46.763 103.453 46.6745 105.23 46.5952C105.73 46.5805 106.119 46.1602 106.088 45.6507C106.081 45.3256 105.896 45.0493 105.637 44.9022C105.492 44.8195 105.328 44.7691 105.153 44.7763C103.376 44.8556 101.589 44.9511 99.7932 45.0627C99.2838 45.0936 98.9111 45.5231 98.9511 46.0164C98.9912 46.5097 99.4115 46.8986 99.9048 46.8585L99.8957 46.8746ZM233.356 75.3822C234.87 76.3293 236.35 77.2996 237.811 78.3021C238.227 78.6241 238.784 78.471 239.056 78.0693C239.336 77.6515 239.224 77.0961 238.823 76.8247C237.362 75.8222 235.85 74.8336 234.319 73.8773L234.287 73.8589C233.867 73.6199 233.344 73.7499 233.073 74.1515C232.808 74.5785 232.936 75.1432 233.347 75.3983L233.356 75.3822ZM126.169 246.238C125.674 246.319 125.356 246.802 125.438 247.297C125.529 247.777 126.002 248.11 126.498 248.029C128.272 247.691 130.021 247.361 131.755 247.021C132.243 246.914 132.567 246.457 132.47 245.952C132.409 245.682 132.254 245.466 132.028 245.337C131.851 245.236 131.62 245.191 131.401 245.237C129.668 245.577 127.919 245.907 126.144 246.245L126.169 246.238ZM137.023 245.955C138.791 245.593 140.542 245.22 142.261 244.83C142.75 244.723 143.067 244.241 142.96 243.752C142.9 243.482 142.736 243.282 142.526 243.163C142.332 243.052 142.118 243.016 141.882 243.053C140.163 243.444 138.421 243.799 136.653 244.162C136.165 244.269 135.857 244.736 135.954 245.24C136.061 245.729 136.544 246.046 137.032 245.939L137.023 245.955ZM184.522 54.652C186.245 55.1192 187.959 55.6026 189.648 56.0929C190.123 56.235 190.62 55.962 190.762 55.4868C190.884 55.0854 190.708 54.6431 190.352 54.4409C190.288 54.4041 190.239 54.3765 190.166 54.3559C188.461 53.8564 186.731 53.3638 184.992 52.8874C184.507 52.7615 184.01 53.0345 183.884 53.5189C183.758 54.0032 184.047 54.5099 184.515 54.6267L184.522 54.652ZM224.152 70.0593C225.366 70.7073 226.554 71.3622 227.718 72.0241L228.8 72.64C229.167 72.9344 229.785 72.7512 230.033 72.315C230.281 71.8788 230.144 71.3303 229.708 71.0821L228.609 70.457C227.43 69.7859 226.234 69.1057 224.995 68.4647C224.559 68.2165 224.008 68.395 223.776 68.8404C223.528 69.2766 223.7 69.8019 224.152 70.0593ZM242.123 81.3975C243.564 82.4739 244.954 83.5642 246.318 84.6614C246.702 84.965 247.278 84.9294 247.591 84.53C247.92 84.1398 247.859 83.5704 247.459 83.2576C246.088 82.135 244.666 81.0263 243.209 79.9408C243.177 79.9224 243.144 79.904 243.112 79.8856C242.724 79.665 242.219 79.7627 241.941 80.139C241.645 80.5476 241.731 81.11 242.123 81.3975ZM147.486 243.654C149.24 243.24 150.993 242.827 152.706 242.411C153.187 242.279 153.479 241.803 153.372 241.315C153.303 241.061 153.155 240.87 152.945 240.751C152.751 240.64 152.52 240.595 152.292 240.657C150.58 241.073 148.842 241.496 147.098 241.893C146.609 242 146.308 242.492 146.415 242.98C146.522 243.469 147.014 243.77 147.502 243.663L147.486 243.654ZM204.772 61.2344C206.435 61.8815 208.09 62.5448 209.719 63.2151C210.182 63.4148 210.71 63.2018 210.893 62.7288C211.075 62.2973 210.901 61.8135 210.513 61.5929C210.481 61.5745 210.449 61.5561 210.416 61.5377C208.781 60.8421 207.126 60.1788 205.421 59.5294C204.948 59.3459 204.428 59.5843 204.253 60.0411C204.07 60.514 204.292 61.0255 204.765 61.2091L204.772 61.2344ZM194.732 57.6814C196.425 58.2385 198.118 58.7957 199.784 59.4014C200.25 59.5596 200.771 59.3212 200.929 58.8552C201.085 58.4307 200.893 57.9791 200.521 57.7677C200.473 57.7401 200.424 57.7125 200.376 57.685C198.701 57.0955 197.001 56.513 195.282 55.9628C194.816 55.8045 194.302 56.0683 194.135 56.5505C193.977 57.0165 194.241 57.5301 194.707 57.6883L194.732 57.6814ZM114.891 249.145C114.973 249.64 115.446 249.974 115.926 249.883C117.698 249.587 119.47 249.291 121.21 248.977C121.706 248.895 122.039 248.422 121.942 247.917C121.89 247.631 121.719 247.406 121.493 247.277C121.316 247.176 121.101 247.139 120.891 247.17C119.151 247.484 117.404 247.773 115.632 248.069C115.136 248.151 114.803 248.624 114.894 249.103L114.891 249.145ZM214.615 65.3595C216.248 66.0967 217.848 66.8569 219.428 67.6494C219.883 67.8653 220.417 67.6776 220.64 67.2484C220.847 66.8099 220.692 66.2938 220.288 66.064L220.239 66.0364C218.649 65.26 217.018 64.4814 215.368 63.7351C214.913 63.5192 214.386 63.7323 214.17 64.1869C213.954 64.6414 214.176 65.1529 214.622 65.3849L214.615 65.3595Z" fill="#84CAFF"/>
          <path d="M77.3506 252.805C72.6487 253.551 67.0195 255.288 67.1896 256.305C67.3598 257.321 73.241 257.246 77.9521 256.483C78.3808 256.407 78.8003 256.346 79.2036 256.276C79.7139 256.695 80.7829 257.56 82.1475 258.657C82.1313 258.648 82.099 258.629 82.0828 258.62L80.8152 258.818C80.6561 258.835 80.555 259.012 80.6037 259.19L80.8042 260.416C80.8276 260.6 80.9708 260.725 81.139 260.692L82.4067 260.494C82.4067 260.494 82.5265 260.434 82.5794 260.378L84.0568 260.15C85.2136 261.086 86.4743 262.103 87.7188 263.11L86.4764 263.301C86.3174 263.318 86.2163 263.496 86.265 263.673L86.4655 264.899C86.4889 265.084 86.6321 265.208 86.8003 265.175L88.068 264.977C88.068 264.977 88.1877 264.917 88.2407 264.861L89.6674 264.647C92.8352 267.198 95.5019 269.314 95.6172 269.337C95.9055 269.394 97.7562 269.036 97.7562 269.036C98.201 268.968 98.5398 268.861 98.542 268.82C98.5281 268.769 98.0348 268.809 97.6153 268.87L92.5346 262.878C92.8802 262.796 93.1198 262.676 93.1081 262.584C93.0894 262.466 92.7159 262.446 92.2872 262.523L92.2365 262.537L91.243 261.352C91.5633 261.277 91.8029 261.157 91.7912 261.065C91.7725 260.947 91.399 260.927 90.9703 261.004L90.0531 259.947L89.6434 259.051C90.0306 258.972 90.337 258.847 90.3253 258.755C90.3066 258.637 89.9331 258.617 89.5044 258.694L89.4883 258.685L88.6666 256.934C89.0538 256.855 89.3602 256.73 89.3324 256.629C89.3137 256.511 88.9402 256.491 88.5115 256.568L87.7662 254.946C90.9169 254.408 92.1614 254.175 94.4962 253.793C96.8471 253.42 99.0895 252.899 100.958 252.359C102.699 253.734 106.105 256.506 106.105 256.506L107.442 256.411L105.156 250.919C105.557 250.741 105.873 250.6 106.08 250.461C107.041 250.238 107.633 250.062 107.621 249.97C107.61 249.877 107.01 249.878 106.056 249.977C105.809 249.922 105.436 249.902 104.963 249.868L105.443 243.875L104.137 244.18C104.137 244.18 101.594 248.122 100.407 249.907C98.5216 249.988 96.3501 250.121 94.0496 250.331C91.1362 250.79 89.8454 250.954 87.1602 251.351L87.3714 249.589C87.8255 249.505 88.1572 249.373 88.1386 249.256C88.1269 249.163 87.804 249.129 87.3937 249.174L87.6231 247.23L87.6392 247.239C88.0933 247.155 88.4342 247.007 88.4064 246.906C88.3947 246.814 88.0557 246.77 87.6615 246.824L87.7635 245.856L88.2914 244.552C88.7363 244.484 89.0772 244.336 89.0586 244.219C89.0469 244.126 88.7909 244.088 88.4474 244.128L89.0327 242.686L89.065 242.704C89.5191 242.62 89.86 242.472 89.8413 242.354C89.8296 242.262 89.5483 242.23 89.2049 242.27L92.1842 235.005C92.5968 234.919 93.0853 234.812 93.0876 234.77C93.0737 234.72 92.7232 234.734 92.2784 234.802C92.2784 234.802 90.3929 235.033 90.1603 235.179C90.0659 235.232 88.1898 238.078 85.9596 241.471L84.5328 241.686C84.5328 241.686 84.4036 241.613 84.3367 241.617L83.0691 241.816C82.91 241.832 82.8089 242.01 82.8576 242.187L83.0581 243.413C83.0815 243.598 83.2247 243.722 83.3929 243.689L84.6353 243.498C83.7639 244.842 82.8926 246.185 82.0672 247.448L80.5898 247.676C80.5898 247.676 80.4605 247.603 80.3937 247.607L79.126 247.806C78.9669 247.822 78.8658 248 78.9146 248.177L79.1151 249.403C79.1384 249.588 79.2816 249.712 79.4499 249.679L80.7175 249.481C80.7175 249.481 80.7359 249.449 80.7866 249.435C79.8348 250.882 79.0991 252.025 78.7358 252.588C78.3325 252.658 77.913 252.719 77.4681 252.786L77.3506 252.805Z" fill="#84CAFF"/>
          </svg>
          </div>

           {/* Left Airplane with Path */}
          <div className="absolute hidden sm:block z-0 left-[-107px] top-[-144px]">
            <svg width="306" height="299" viewBox="0 0 306 299" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M40.1852 195.579C39.8905 195.796 39.5078 195.792 39.217 195.627C39.1039 195.562 39 195.482 38.9307 195.378C37.8697 193.898 36.8595 192.403 35.9 190.895C35.6318 190.464 35.753 189.913 36.1836 189.645C36.6143 189.377 37.1655 189.498 37.4337 189.928C38.354 191.393 39.348 192.878 40.3858 194.324C40.6724 194.723 40.5836 195.292 40.1852 195.579ZM106.936 71.882C107.404 71.699 107.624 71.1614 107.432 70.7099C107.249 70.2422 106.711 70.0219 106.26 70.214C104.576 70.8811 102.924 71.5665 101.304 72.2703C100.853 72.4625 100.632 73.0001 100.825 73.4516C100.906 73.6475 101.04 73.7878 101.201 73.8798C101.443 74.0177 101.734 74.0333 102.006 73.9313C103.616 73.2437 105.268 72.5582 106.927 71.8982L106.936 71.882ZM34.7741 185.357C33.9343 183.788 33.1545 182.19 32.4484 180.612C32.2401 180.151 31.7095 179.956 31.2579 180.148C30.7972 180.356 30.6022 180.887 30.7944 181.339C31.5143 182.967 32.3173 184.6 33.1802 186.203C33.2635 186.358 33.3905 186.473 33.5359 186.556C33.7944 186.703 34.1264 186.72 34.4097 186.561C34.8473 186.318 35.0261 185.778 34.7833 185.341L34.7741 185.357ZM87.9061 79.4671C87.6886 79.0225 87.1234 78.8507 86.695 79.0774C85.0688 79.9057 83.4842 80.7362 81.9318 81.5852C81.4942 81.8281 81.3315 82.3771 81.5743 82.8147C81.6576 82.969 81.7846 83.0841 81.93 83.1668C82.1885 83.3139 82.5206 83.3317 82.8038 83.1721C84.347 82.3393 85.9156 81.4996 87.5164 80.6782C87.961 80.4607 88.149 79.9047 87.9061 79.4671ZM28.9698 170.677C28.8375 170.195 28.3459 169.894 27.8551 170.042C27.3735 170.175 27.0723 170.666 27.2208 171.157C27.6689 172.888 28.2 174.623 28.8072 176.337C28.879 176.549 29.0361 176.724 29.2138 176.825C29.44 176.954 29.7052 176.976 29.9678 176.89C30.4425 176.733 30.679 176.204 30.5214 175.73C29.9281 174.066 29.4201 172.366 28.986 170.686L28.9698 170.677ZM126.48 62.9426C124.752 63.4991 123.065 64.0578 121.378 64.6165C120.904 64.7741 120.651 65.2933 120.809 65.768C120.88 65.9799 121.038 66.1549 121.215 66.256C121.425 66.3755 121.707 66.4073 121.944 66.3284C123.614 65.7606 125.301 65.2019 127.004 64.6524C127.478 64.4947 127.747 63.9847 127.59 63.5101C127.432 63.0354 126.922 62.7666 126.447 62.9243L126.48 62.9426ZM54.6462 209.967C53.3114 208.779 52.0204 207.553 50.7709 206.329C50.4244 205.96 49.832 205.987 49.489 206.326C49.137 206.682 49.1472 207.265 49.5121 207.601C50.7686 208.851 52.1011 210.08 53.4428 211.292C53.4913 211.32 53.5467 211.373 53.5952 211.4C53.9506 211.602 54.4369 211.537 54.7199 211.228C55.0559 210.863 55.0202 210.286 54.6554 209.95L54.6462 209.967ZM117.465 66.9452C117.299 66.4867 116.779 66.2341 116.305 66.3917C114.607 67.0081 112.925 67.6336 111.268 68.2522C110.801 68.4352 110.573 68.9474 110.731 69.4221C110.812 69.6179 110.953 69.7836 111.131 69.8847C111.357 70.0134 111.638 70.0452 111.91 69.9433C113.55 69.3155 115.223 68.7061 116.921 68.0898C117.38 67.9229 117.632 67.4037 117.475 66.9291L117.465 66.9452ZM148.193 57.6328C148.06 57.1512 147.569 56.8501 147.087 56.9824C145.331 57.4374 143.617 57.8947 141.902 58.352C141.421 58.4843 141.136 58.9851 141.268 59.4667C141.321 59.711 141.495 59.8952 141.688 60.0055C141.882 60.1158 142.138 60.1545 142.366 60.0918C144.065 59.6254 145.779 59.1681 147.526 58.7291C148.008 58.5969 148.309 58.1052 148.176 57.6236L148.193 57.6328ZM47.0959 202.527C45.8854 201.196 44.7256 199.852 43.6235 198.519C43.3115 198.128 42.7397 198.08 42.3483 198.392C41.9569 198.704 41.9095 199.276 42.2215 199.667C43.3306 201.026 44.5135 202.404 45.7564 203.753C45.8187 203.832 45.8995 203.877 45.9803 203.923C46.3196 204.116 46.746 204.081 47.036 203.797C47.4043 203.451 47.4263 202.886 47.0959 202.527ZM132.133 63.0799C133.817 62.5627 135.518 62.0547 137.251 61.5651C137.732 61.4328 138.001 60.9228 137.885 60.4504C137.753 59.9688 137.243 59.7 136.77 59.8161C135.028 60.3219 133.312 60.8207 131.627 61.3379C131.153 61.4955 130.877 61.9802 131.025 62.471C131.088 62.6991 131.245 62.874 131.439 62.9844C131.649 63.1039 131.905 63.1426 132.142 63.0638L132.133 63.0799ZM34.5383 125.298C34.9744 125.547 35.5068 125.4 35.7711 124.973C36.6352 123.455 37.5893 121.966 38.5757 120.496C38.8562 120.078 38.7446 119.522 38.3268 119.242C37.9159 118.987 37.3535 119.073 37.073 119.491C36.0521 120.984 35.0866 122.531 34.1879 124.073C33.9397 124.509 34.0767 125.057 34.5129 125.305L34.5383 125.298ZM31.1819 134.467C31.8015 132.852 32.5132 131.225 33.2896 129.635C33.5055 129.181 33.3178 128.646 32.8632 128.43C32.4086 128.214 31.8741 128.402 31.6582 128.857C30.8634 130.479 30.1403 132.163 29.4931 133.827C29.3371 134.251 29.4948 134.726 29.8825 134.946C29.9148 134.965 29.9633 134.992 29.9956 135.011C30.4685 135.194 30.9892 134.956 31.1635 134.499L31.1819 134.467ZM40.4288 116.375C40.4288 116.375 40.5096 116.421 40.5419 116.439C40.9296 116.66 41.4343 116.562 41.7034 116.202C42.7592 114.835 43.891 113.447 45.062 112.103C45.3818 111.729 45.3462 111.153 44.9721 110.833C44.5981 110.513 44.0217 110.549 43.702 110.923C42.5125 112.299 41.3554 113.694 40.2812 115.093C39.9684 115.492 40.0547 116.055 40.438 116.359L40.4288 116.375ZM47.3675 108.24C47.3675 108.24 47.4783 108.346 47.5429 108.382C47.8984 108.585 48.3432 108.517 48.6332 108.233C49.8321 106.99 51.1232 105.736 52.4283 104.533C52.7965 104.186 52.8185 103.621 52.472 103.253C52.1255 102.885 51.5607 102.863 51.1924 103.209C49.869 104.445 48.5687 105.715 47.3352 106.981C46.9831 107.337 46.9934 107.92 47.3583 108.256L47.3675 108.24ZM91.466 78.2848C91.7083 78.4227 92.0312 78.4567 92.3053 78.3133C93.8855 77.5657 95.4911 76.8112 97.1381 76.0589C97.5989 75.8506 97.7939 75.32 97.5855 74.8593C97.3772 74.3985 96.8466 74.2035 96.3859 74.4119C94.7226 75.155 93.1171 75.9095 91.5184 76.6893C91.0739 76.9069 90.8789 77.4375 91.0872 77.8982C91.1613 78.0687 91.2953 78.2091 91.4568 78.301L91.466 78.2848ZM27.238 160.303C27.1979 159.809 26.7684 159.437 26.2589 159.467C25.7656 159.508 25.3929 159.937 25.4238 160.446C25.5608 162.235 25.7877 164.054 26.0837 165.826C26.135 166.112 26.306 166.338 26.5322 166.466C26.7099 166.568 26.9243 166.604 27.1341 166.574C27.6296 166.492 27.9631 166.019 27.8723 165.54C27.574 163.809 27.361 162.041 27.2288 160.319L27.238 160.303ZM63.5199 94.2482C63.6054 94.361 63.6931 94.4324 63.8062 94.4967C64.1131 94.6714 64.4889 94.6499 64.7744 94.4488C66.1859 93.4341 67.6388 92.4217 69.1333 91.4115C69.5477 91.1341 69.6528 90.5736 69.3754 90.1592C69.0979 89.7447 68.5375 89.6396 68.123 89.917C66.6032 90.9342 65.1249 91.9536 63.7043 92.9845C63.306 93.2711 63.2171 93.8407 63.5037 94.239L63.5199 94.2482ZM29.4895 139.47C29.6316 138.995 29.3679 138.482 28.8835 138.356C28.4083 138.214 27.9108 138.487 27.7687 138.962C27.2508 140.699 26.8137 142.482 26.4618 144.228C26.391 144.615 26.5785 145 26.9016 145.184C26.9824 145.23 27.0632 145.276 27.1623 145.289C27.6397 145.39 28.1233 145.066 28.2239 144.589C28.5735 142.884 28.9992 141.159 29.4895 139.47ZM60.6833 96.3768C60.3714 95.9854 59.8087 95.9218 59.4265 96.2177C57.9923 97.3477 56.5995 98.4799 55.2482 99.6144C54.873 99.9355 54.8256 100.507 55.1467 100.883C55.2091 100.961 55.2969 101.032 55.3938 101.087C55.7169 101.271 56.1342 101.252 56.4311 100.993C57.757 99.8657 59.1245 98.7404 60.5496 97.6266C60.9409 97.3146 61.0045 96.752 60.7087 96.3698L60.6833 96.3768ZM27.4033 149.789C27.4554 149.284 27.1042 148.849 26.5992 148.797C26.0942 148.745 25.6591 149.096 25.607 149.601C25.4189 151.397 25.3046 153.214 25.2733 155.036C25.2716 155.377 25.4474 155.669 25.7382 155.835C25.8674 155.908 26.0151 155.95 26.1649 155.949C26.6722 155.96 27.068 155.565 27.0786 155.058C27.1146 153.303 27.2175 151.544 27.3941 149.805L27.4033 149.789ZM78.5089 84.5563C78.266 84.1187 77.7078 83.9722 77.261 84.2312C75.688 85.1539 74.1565 86.0789 72.6504 86.9969C72.2197 87.2652 72.0985 87.8164 72.3668 88.2471C72.4431 88.376 72.5539 88.4819 72.6832 88.5554C72.9578 88.7117 73.3152 88.7226 73.6077 88.5468C75.0977 87.6196 76.62 86.7108 78.1676 85.795C78.6052 85.5521 78.7517 84.9939 78.4835 84.5633L78.5089 84.5563ZM183.796 251.762C182.007 251.749 180.237 251.704 178.466 251.658C177.959 251.648 177.547 252.034 177.536 252.541C177.534 252.882 177.717 253.2 177.992 253.356C178.121 253.43 178.268 253.471 178.418 253.471C180.189 253.516 181.985 253.554 183.79 253.576C184.281 253.578 184.703 253.176 184.704 252.685C184.705 252.194 184.304 251.772 183.812 251.771L183.796 251.762ZM162.482 250.733C160.693 250.57 158.929 250.401 157.174 250.215C156.669 250.163 156.234 250.514 156.182 251.019C156.146 251.383 156.335 251.726 156.642 251.901C156.739 251.956 156.861 252.004 156.986 252.011C158.757 252.206 160.546 252.369 162.336 252.532C162.825 252.574 163.269 252.207 163.312 251.718C163.355 251.229 162.987 250.785 162.498 250.742L162.482 250.733ZM173.133 251.447C171.339 251.367 169.563 251.255 167.813 251.136C167.315 251.109 166.887 251.486 166.844 251.975C166.817 252.323 167.016 252.65 167.307 252.815C167.42 252.88 167.551 252.912 167.692 252.928C169.468 253.04 171.243 253.152 173.053 253.241C173.551 253.267 173.963 252.882 173.99 252.384C174.017 251.886 173.64 251.457 173.133 251.447ZM130.863 245.979C129.11 245.602 127.375 245.192 125.666 244.775C125.182 244.65 124.691 244.948 124.582 245.441C124.485 245.836 124.68 246.246 125.019 246.439C125.084 246.476 125.164 246.522 125.247 246.526C126.973 246.952 128.716 247.345 130.485 247.732C130.979 247.841 131.453 247.534 131.563 247.04C131.673 246.547 131.365 246.072 130.872 245.963L130.863 245.979ZM151.879 249.597C150.101 249.377 148.342 249.124 146.592 248.855C146.096 248.787 145.628 249.12 145.56 249.616C145.498 249.987 145.695 250.355 146.002 250.53C146.099 250.585 146.205 250.624 146.32 250.647C148.071 250.916 149.856 251.162 151.634 251.382C152.13 251.45 152.59 251.092 152.633 250.603C152.701 250.108 152.343 249.647 151.854 249.604L151.879 249.597ZM141.333 248.023C139.56 247.72 137.821 247.393 136.082 247.067C135.595 246.982 135.121 247.29 135.011 247.783C134.941 248.171 135.128 248.556 135.451 248.739C135.532 248.785 135.613 248.831 135.737 248.838C137.492 249.174 139.247 249.509 141.021 249.813C141.507 249.897 141.975 249.564 142.059 249.078C142.144 248.591 141.811 248.124 141.324 248.039L141.333 248.023ZM194.473 251.678C192.672 251.723 190.907 251.744 189.141 251.766C188.649 251.764 188.244 252.176 188.243 252.667C188.241 253.008 188.426 253.284 188.701 253.44C188.83 253.514 188.994 253.564 189.153 253.548C190.935 253.536 192.726 253.507 194.527 253.462C195.027 253.447 195.416 253.027 195.418 252.536C195.403 252.036 194.982 251.647 194.491 251.646L194.473 251.678ZM215.778 250.392C213.996 250.554 212.224 250.7 210.451 250.847C209.958 250.887 209.585 251.316 209.616 251.826C209.633 252.134 209.827 252.395 210.069 252.533C210.214 252.615 210.404 252.659 210.579 252.651C212.351 252.505 214.14 252.368 215.931 252.19C216.425 252.15 216.79 251.695 216.75 251.202C216.71 250.708 216.255 250.343 215.762 250.383L215.778 250.392ZM227.395 250.009C227.33 249.522 226.875 249.157 226.388 249.222C224.611 249.451 222.853 249.648 221.094 249.845C220.591 249.901 220.235 250.34 220.291 250.842C220.333 251.144 220.495 251.386 220.737 251.524C220.899 251.616 221.088 251.659 221.288 251.645C223.047 251.448 224.831 251.245 226.608 251.015C227.095 250.95 227.46 250.495 227.395 250.009ZM205.128 251.218C203.332 251.33 201.571 251.418 199.794 251.498C199.294 251.512 198.905 251.933 198.936 252.442C198.943 252.767 199.128 253.043 199.386 253.191C199.532 253.273 199.696 253.324 199.871 253.316C201.648 253.237 203.435 253.142 205.231 253.03C205.74 252.999 206.113 252.57 206.073 252.076C206.033 251.583 205.612 251.194 205.119 251.234L205.128 251.218ZM71.6679 222.711C70.1537 221.763 68.6741 220.793 67.2129 219.791C66.7974 219.469 66.2397 219.622 65.9684 220.023C65.6879 220.441 65.7995 220.997 66.2012 221.268C67.6624 222.271 69.1743 223.259 70.7046 224.216L70.7369 224.234C71.1569 224.473 71.6801 224.343 71.9514 223.941C72.2157 223.514 72.0879 222.95 71.6771 222.694L71.6679 222.711ZM178.855 51.8549C179.35 51.7733 179.668 51.2908 179.586 50.7953C179.495 50.3159 179.022 49.9825 178.526 50.0641C176.752 50.4016 175.002 50.7322 173.269 51.0719C172.781 51.1789 172.457 51.6359 172.554 52.1407C172.615 52.4103 172.77 52.6267 172.996 52.7554C173.173 52.8565 173.404 52.9022 173.623 52.8557C175.356 52.516 177.105 52.1854 178.88 51.8479L178.855 51.8549ZM168.001 52.1374C166.233 52.5003 164.482 52.8723 162.762 53.2628C162.274 53.3697 161.957 53.8521 162.064 54.3407C162.124 54.6104 162.288 54.8106 162.498 54.9301C162.692 55.0405 162.906 55.077 163.141 55.0396C164.861 54.6492 166.603 54.2933 168.371 53.9304C168.859 53.8235 169.167 53.3572 169.07 52.8525C168.963 52.3639 168.48 52.0466 167.992 52.1536L168.001 52.1374ZM120.502 243.441C118.779 242.974 117.065 242.49 115.376 242C114.901 241.858 114.404 242.131 114.261 242.606C114.14 243.007 114.316 243.45 114.671 243.652C114.736 243.689 114.785 243.716 114.858 243.737C116.563 244.236 118.293 244.729 120.032 245.205C120.517 245.331 121.014 245.058 121.14 244.574C121.266 244.09 120.977 243.583 120.509 243.466L120.502 243.441ZM80.8719 228.033C79.6581 227.385 78.4695 226.731 77.3063 226.069L76.2239 225.453C75.8568 225.158 75.2393 225.342 74.9911 225.778C74.7429 226.214 74.8799 226.762 75.3161 227.011L76.4146 227.636C77.594 228.307 78.7895 228.987 80.0287 229.628C80.4649 229.876 81.0156 229.698 81.2476 229.252C81.4958 228.816 81.3243 228.291 80.8719 228.033ZM62.9006 216.695C61.46 215.619 60.0701 214.529 58.7055 213.431C58.3223 213.128 57.746 213.163 57.4332 213.563C57.1042 213.953 57.1652 214.522 57.5646 214.835C58.9361 215.958 60.3583 217.066 61.815 218.152C61.8474 218.17 61.8797 218.189 61.912 218.207C62.2997 218.428 62.8044 218.33 63.0827 217.954C63.3794 217.545 63.2931 216.983 62.9006 216.695ZM157.538 54.4389C155.784 54.8524 154.03 55.266 152.318 55.6818C151.837 55.8141 151.545 56.2895 151.652 56.7781C151.721 57.0316 151.869 57.2227 152.079 57.3422C152.273 57.4525 152.504 57.4982 152.732 57.4355C154.444 57.0197 156.182 56.597 157.926 56.1996C158.415 56.0926 158.716 55.601 158.609 55.1124C158.502 54.6238 158.01 54.3227 157.522 54.4297L157.538 54.4389ZM100.252 236.858C98.5885 236.211 96.9343 235.548 95.3053 234.878C94.8416 234.678 94.314 234.891 94.1304 235.364C93.9491 235.795 94.1229 236.279 94.5106 236.5C94.5429 236.518 94.5752 236.537 94.6075 236.555C96.2434 237.251 97.8977 237.914 99.6026 238.563C100.076 238.747 100.596 238.508 100.771 238.052C100.954 237.579 100.732 237.067 100.259 236.884L100.252 236.858ZM110.292 240.411C108.599 239.854 106.906 239.297 105.24 238.691C104.774 238.533 104.253 238.772 104.095 239.238C103.939 239.662 104.131 240.114 104.503 240.325C104.551 240.353 104.6 240.38 104.648 240.408C106.323 240.997 108.023 241.58 109.742 242.13C110.208 242.288 110.721 242.024 110.889 241.542C111.047 241.076 110.783 240.563 110.317 240.404L110.292 240.411ZM190.133 48.9479C190.051 48.4524 189.578 48.1189 189.098 48.2097C187.326 48.5057 185.554 48.8017 183.814 49.1161C183.318 49.1977 182.985 49.671 183.082 50.1757C183.134 50.4615 183.305 50.6871 183.531 50.8158C183.708 50.9169 183.923 50.9534 184.133 50.9231C185.873 50.6087 187.62 50.3196 189.392 50.0236C189.888 49.942 190.221 49.4688 190.13 48.9894L190.133 48.9479ZM90.4092 232.733C88.7755 231.996 87.1764 231.236 85.5957 230.443C85.1411 230.227 84.6066 230.415 84.3837 230.844C84.177 231.283 84.3324 231.799 84.7363 232.029L84.7848 232.056C86.3747 232.833 88.0061 233.611 89.6559 234.358C90.1105 234.574 90.6381 234.36 90.8539 233.906C91.0698 233.451 90.8476 232.94 90.4022 232.708L90.4092 232.733Z" fill="#84CAFF"/>
            <path d="M227.673 45.2879C232.375 44.5416 238.004 42.8045 237.834 41.7881C237.664 40.7717 231.783 40.8469 227.072 41.6094C226.643 41.6862 226.224 41.7469 225.82 41.8168C225.31 41.3982 224.241 40.5332 222.876 39.436C222.893 39.4452 222.925 39.4635 222.941 39.4727L224.209 39.2745C224.368 39.258 224.469 39.0803 224.42 38.9029L224.22 37.6767C224.196 37.4923 224.053 37.3681 223.885 37.4007L222.617 37.5989C222.617 37.5989 222.497 37.6591 222.445 37.7145L220.967 37.9432C219.81 37.0069 218.55 35.9902 217.305 34.9826L218.547 34.7913C218.707 34.7749 218.808 34.5972 218.759 34.4197L218.558 33.1935C218.535 33.0092 218.392 32.8849 218.224 32.9175L216.956 33.1158C216.956 33.1158 216.836 33.1759 216.783 33.2314L215.357 33.4461C212.189 30.895 209.522 28.7788 209.407 28.7559C209.118 28.6988 207.268 29.0572 207.268 29.0572C206.823 29.1249 206.484 29.2315 206.482 29.273C206.496 29.3237 206.989 29.2836 207.409 29.2229L212.489 35.215C212.144 35.2963 211.904 35.4166 211.916 35.5088C211.934 35.6264 212.308 35.6464 212.737 35.5696L212.787 35.5557L213.781 36.7412C213.461 36.8155 213.221 36.9359 213.233 37.0281C213.251 37.1456 213.625 37.1657 214.054 37.0888L214.971 38.1454L215.381 39.0415C214.993 39.1206 214.687 39.2456 214.699 39.3378C214.717 39.4554 215.091 39.4755 215.519 39.3986L215.536 39.4078L216.357 41.1585C215.97 41.2376 215.664 41.3626 215.692 41.464C215.71 41.5816 216.084 41.6016 216.512 41.5248L217.258 43.1465C214.107 43.6848 212.863 43.9176 210.528 44.3C208.177 44.6731 205.934 45.1936 204.066 45.7342C202.325 44.3587 198.919 41.5867 198.919 41.5867L197.582 41.6813L199.868 47.1735C199.467 47.3518 199.151 47.493 198.944 47.6317C197.983 47.8548 197.391 48.0311 197.402 48.1233C197.414 48.2155 198.014 48.2144 198.968 48.1158C199.215 48.1707 199.588 48.1908 200.061 48.2245L199.581 54.2174L200.887 53.913C200.887 53.913 203.43 49.9706 204.617 48.1863C206.502 48.1045 208.674 47.9714 210.974 47.762C213.888 47.3026 215.178 47.1388 217.864 46.742L217.652 48.5038C217.198 48.5876 216.867 48.7197 216.885 48.8372C216.897 48.9294 217.22 48.9634 217.63 48.9188L217.401 50.8628L217.385 50.8536C216.931 50.9374 216.59 51.0856 216.618 51.187C216.629 51.2792 216.968 51.3224 217.362 51.2687L217.26 52.2372L216.733 53.5407C216.288 53.6084 215.947 53.7566 215.965 53.8741C215.977 53.9663 216.233 54.005 216.577 53.9652L215.991 55.4072L215.959 55.3889C215.505 55.4727 215.164 55.6208 215.183 55.7384C215.194 55.8306 215.476 55.8624 215.819 55.8225L212.84 63.088C212.427 63.1741 211.939 63.281 211.936 63.3225C211.95 63.3732 212.301 63.3587 212.746 63.2911C212.746 63.2911 214.631 63.0594 214.864 62.9137C214.958 62.8605 216.834 60.0144 219.064 56.6213L220.491 56.4066C220.491 56.4066 220.62 56.4801 220.687 56.4754L221.955 56.2771C222.114 56.2607 222.215 56.083 222.166 55.9055L221.966 54.6794C221.942 54.495 221.799 54.3707 221.631 54.4033L220.389 54.5946C221.26 53.2512 222.131 51.9078 222.957 50.6452L224.434 50.4166C224.434 50.4166 224.563 50.4902 224.63 50.4854L225.898 50.2872C226.057 50.2707 226.158 50.093 226.109 49.9156L225.909 48.6894C225.885 48.505 225.742 48.3808 225.574 48.4134L224.306 48.6116C224.306 48.6116 224.288 48.6439 224.237 48.6579C225.189 47.2106 225.925 46.068 226.288 45.5048C226.691 45.4349 227.111 45.3742 227.556 45.3065L227.673 45.2879Z" fill="#84CAFF"/>
            </svg>
          </div>
            {/* ================================================================================= */}

          </div>

          {/* Indicators */}
          <div className="flex justify-center mt-6 sm:mt-8 gap-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => handleIndicatorClick(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  currentIndex === index ? "w-6 bg-blue-600" : "bg-gray-300"
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialSection;

