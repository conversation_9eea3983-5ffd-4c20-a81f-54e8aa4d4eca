import { useQueryStore } from '@/app/store/queryStore';
import axios from 'axios'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'

const DestinationToursDropdown: React.FC<any> = ({ setShowDestinationsDropdown, cb_function = null }) => {
  const [data, setData] = useState([]);
  const setQuery = useQueryStore((state) => state.setQuery);
  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/cities/destination-tree-map`);
        setData(response.data.continents);
      } catch (error) {
        console.log(error);
      }
    };
    fetchDestinations();
  }, []);

  return (
    <div className="flex flex-col md:flex-row px-4 mt-2 md:mt-0 md:p-8 md:gap-5 flex-wrap gap-10 z-50">
      
      {/* World Regions */}
      <div className="min-w-[150px]">
        <h4 className="text-[#1570EF] text-sm font-semibold mb-3">World Regions</h4>
        <ul className="space-y-2">
          {data.map((element: any) =>
            element.regions && element.regions.sort((x:any,y:any)=>{
               return x.regionName.localeCompare(y.regionName);
            })?.map((region: any, region_index: number) => (
              <li key={`${region_index}-world-region`}>
                <Link
                  onClick={() => {
                    setQuery(region.regionName);
                    setShowDestinationsDropdown(false);
                      
                    if (cb_function) cb_function();
                  }}
                  href={`/vacation/tours?query=${region.regionName}`}
                  className="text-[#475467] hover:text-[#1570EF] text-sm font-medium"
                >
                  {region.regionName}
                </Link>
              </li>
            ))
          )}
        </ul>
      </div>

      {/* Continents */}
      {data.map((element: any, index: number) => (
        element.continentName && (
          <div key={`continent-${index}`} className="min-w-[150px]">
            <h4 className="text-[#1570EF] text-sm font-semibold mb-3">{element.continentName}</h4>
            <ul className="columns-1 sm:columns-2 lg:columns-3 gap-4 [column-fill:_balance]">
              {
                // Array(...new Set(element.regions.map((x)=>x.countries).flat().map((x)=>x.countryName))).sort((x,y)=>x.localeCompare(y))
                Array.from(
                  new Set(
                  element.regions
                  .flatMap((region: any) => region.countries || [])
                  .map((x:any)=>x.countryName))
                )
                .sort((x:any,y:any)=>x.localeCompare(y)).map((x:any,index)=>{
                  return(
                    <li key={`${index}-country-${x}`} className="break-inside-avoid mb-2">
                    <Link
                      onClick={() => {
                        setQuery(x);
                        setShowDestinationsDropdown(false)}
                      }
                      href={`/vacation/tours?query=${x}`}
                      className="text-[#475467] hover:text-[#1570EF] text-sm font-medium"
                    >
                      {x}
                    </Link>
                  </li> 
                  )
                })
              }
                  {/* <li key={`${country_index}-country-${region.regionName}`} className="break-inside-avoid mb-2">
                    <Link
                      onClick={() => setShowDestinationsDropdown(false)}
                      href={`/vacation/tours?query=${country.countryName}`}
                      className="text-[#475467] hover:text-[#1570EF] text-sm font-medium"
                    >
                      {country.countryName}
                    </Link>
                  </li> */}
            </ul>
            {/* <ul className="columns-1 sm:columns-2 lg:columns-3 gap-4 [column-fill:_balance]">
              {element.regions?.sort((x:any,y:any)=>{
                let test= element.regions
                console.log("element.regions",element.regions)
              }).flatMap((region: any) =>
                region.countries?.map((country: any, country_index: number) => (
                  <li key={`${country_index}-country-${region.regionName}`} className="break-inside-avoid mb-2">
                    <Link
                      onClick={() => setShowDestinationsDropdown(false)}
                      href={`/vacation/tours?query=${country.countryName}`}
                      className="text-[#475467] hover:text-[#1570EF] text-sm font-medium"
                    >
                      {country.countryName}
                    </Link>
                  </li>
                ))
              )}
            </ul> */}
          </div>
        )
      ))}
      
    </div>
  );
};

export default DestinationToursDropdown;
