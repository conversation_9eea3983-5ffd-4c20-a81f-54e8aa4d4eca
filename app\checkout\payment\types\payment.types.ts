export interface UserDetails {
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  customer_id?: number;
}

export interface PaymentInitData {
  packageId: string;
  customerId: number;
  bookingId: number;
  customerEmail: string;
  installmentId: number;
  extra: string;
}

export interface PaymentResponseData {
  merchantKey?: string;
  transactionId?: string;
  payUHash?: string;
  productName?: string;
  PAYU_API_URL?: string;
  [key: string]: any;
}

export interface PaymentSubmissionData {
  key: string;
  txnid: string;
  hash: string;
  amount: string;
  productinfo: string;
  firstname: string;
  lastname: string;
  email: string;
  phone: string;
  surl: string;
  furl: string;
  [key: string]: any;
}

export interface CardDetails {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardName: string;
}

export interface PaymentMethodProps {
  selectedMethod: string | null;
  onMethodChange: (method: string) => void;
  amount: string;
  packageId: string;
  bookingId: string;
  installmentId: string;
  title: string;
  duration: string;
}
