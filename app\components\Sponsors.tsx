import { FC, useState, useEffect } from "react";

interface SponsorImage {
  id: number;
  image: string;
}

interface SponsorSectionProps {
  sponsors?: SponsorImage[];
}

// Default sponsors as fallback
const defaultSponsors = [
  { id: 1, src: "/assets/sponsors/Traveloka.png", alt: "Traveloka" },
  { id: 2, src: "/assets/sponsors/tiket.com.png", alt: "Tiket.com" },
  { id: 3, src: "/assets/sponsors/Booking.png", alt: "Booking.com" },
  { id: 4, src: "/assets/sponsors/Tripadvisor.png", alt: "Tripadvisor" },
  { id: 5, src: "/assets/sponsors/Airbnb.png", alt: "Airbnb" },
  { id: 6, src: "/assets/sponsors/Expedia.png", alt: "Expedia" },
  { id: 7, src: "/assets/sponsors/Agoda.png", alt: "Agoda" },
  { id: 8, src: "/assets/sponsors/Hotels.png", alt: "Hotels.com" },
];

const SponsorSection: FC<SponsorSectionProps> = ({ sponsors }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [itemsPerView, setItemsPerView] = useState(5);

  // Transform sponsors to include alt text, or use defaults if no sponsors provided
  const displaySponsors = sponsors?.map(sponsor => ({
    id: sponsor.id,
    src: sponsor.image,
    alt: `Partner ${sponsor.id}`
  })) || defaultSponsors;

  // Update items per view based on screen size
  useEffect(() => {
    const updateItemsPerView = () => {
      const width = window.innerWidth;
      if (width < 480) {
        setItemsPerView(2); // Mobile: 2 items
      } else if (width < 640) {
        setItemsPerView(3); // Small mobile: 3 items
      } else if (width < 768) {
        setItemsPerView(3); // Tablet: 3 items
      } else if (width < 1024) {
        setItemsPerView(4); // Small desktop: 4 items
      } else {
        setItemsPerView(5); // Desktop: 5 items
      }
    };

    updateItemsPerView();
    window.addEventListener('resize', updateItemsPerView);
    return () => window.removeEventListener('resize', updateItemsPerView);
  }, []);

  // Auto-scroll functionality for mobile
  useEffect(() => {
    if (displaySponsors.length <= itemsPerView) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const maxIndex = displaySponsors.length - itemsPerView;
        return prevIndex >= maxIndex ? 0 : prevIndex + 1;
      });
    }, 3000);

    return () => clearInterval(interval);
  }, [displaySponsors.length, itemsPerView]);

  return (
    <section className="bg-white">
      {/* Desktop version - shows all sponsors */}
      <div 
        className="hidden lg:flex items-center justify-between h-40 max-w-screen-xl mx-auto"
        style={{ 
          maxWidth: '1440px',
          paddingLeft: '185px',
          paddingRight: '185px'
        }}
      >
        {displaySponsors.slice(0, 5).map((sponsor) => (
          <div
            key={sponsor.id}
            className="flex items-center justify-center flex-shrink-0"
          >
            <img
              src={sponsor.src}
              alt={sponsor.alt}
              className="max-h-12 object-contain opacity-60 grayscale"
              loading="lazy"
            />
          </div>
        ))}
      </div>

      {/* Mobile/Tablet version - carousel */}
      <div className="lg:hidden">
        <div 
          className="flex items-center  h-32 sm:h-36 px-4 sm:px-6" style={{ justifyContent: "space-evenly" }}
        >
          {displaySponsors.slice(currentIndex, currentIndex + itemsPerView).map((sponsor) => (
            <div
              key={sponsor.id}
              className="flex items-center justify-center flex-shrink-0"
            >
              <img
                src={sponsor.src}
                alt={sponsor.alt}
                className="max-h-6 sm:max-h-8 md:max-h-10 object-contain opacity-60 grayscale"
                loading="lazy"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SponsorSection;