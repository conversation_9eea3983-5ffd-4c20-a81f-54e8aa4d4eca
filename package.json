{"name": "wise-yatra-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@heroicons/react": "^2.2.0", "@react-google-maps/api": "^2.20.6", "@types/axios": "^0.14.4", "@types/node": "22.10.7", "@types/react": "19.0.7", "@types/react-datepicker": "^7.0.0", "@types/react-dom": "19.0.3", "autoprefixer": "10.4.20", "axios": "^1.8.4", "date-fns": "^4.1.0", "eslint": "9.18.0", "eslint-config-next": "15.1.5", "framer-motion": "^12.0.6", "leaflet": "^1.9.4", "leaflet-defaulticon-compatibility": "^0.1.2", "next": "^15.2.4", "postcss": "8.5.1", "puppeteer": "^24.10.1", "react": "19.0.0", "react-datepicker": "^8.2.1", "react-dom": "19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-leaflet": "^5.0.0", "react-loading-skeleton": "^3.5.0", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "tailwindcss": "3.4.17", "typescript": "5.7.3", "zustand": "^5.0.5"}, "devDependencies": {"@types/leaflet": "^1.9.18", "@types/react-slick": "^0.23.13", "daisyui": "^4.12.23"}}