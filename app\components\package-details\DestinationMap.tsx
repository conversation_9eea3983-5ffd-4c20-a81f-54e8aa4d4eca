"use client";

import Image from 'next/image';

interface DestinationMapProps {
  mapImage?: string;
  altText?: string;
}

const DestinationMap: React.FC<DestinationMapProps> = ({
  mapImage = "/assets/tours/map.png",
  altText = "Destination route map"
}) => {
  return (
    <section id="destinations" className="space-y-4 mb-8">
      <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Destination Route</h2>
      <div className="relative w-full h-[250px] sm:h-[350px] md:h-[450px] lg:h-[500px] rounded-xl overflow-hidden">
        <Image
          src={mapImage}
          alt={altText}
          fill
          className="object-contain"
          priority
        />
      </div>
    </section>
  );
};

export default DestinationMap;
