"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from 'next/navigation';

// Define the tour type
interface Tour {
  id: number;
  title: string;
  image: string;
  description: string;
  packageCode: string;
  packageTitle: string;
  packageMainImage: string;
  status?: string;
}

const CuratedTours: React.FC = () => {
  const router = useRouter();
  const [data, setData] = useState<Tour[] | null>(null);
  const [loading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    const API_BASE_URL_DEV = process.env.NEXT_PUBLIC_API_BASE_URL;
    
    console.log("🔍 Debug Info:");
    console.log("API_BASE_URL_DEV:", API_BASE_URL_DEV);
    console.log("Full URL:", `${API_BASE_URL_DEV}/api/packages/website/best-deals`);
    
    const fetchTours = async () => {
      try {
        setIsLoading(true);
        
        // Check if API URL is configured
        if (!API_BASE_URL_DEV) {
          throw new Error('API base URL is not configured. Please check your environment variables.');
        }

        console.log("🚀 Making API request...");
        const response = await fetch(`${API_BASE_URL_DEV}/api/packages/website/best-deals`);
        
        console.log("📊 Response status:", response.status);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch tour data: ${response.status} ${response.statusText}`);
        }
        
        const apiData = await response.json();
        console.log("✅ API Data received:", apiData);
        console.log("📦 Data length:", apiData.length);
        
        // Filter for active tours if status field exists
        const filteredData = apiData.filter((tour: Tour) => 
          !tour.status || tour.status.toLowerCase() === 'active'
        );
        
        console.log("🔍 Filtered Data:", filteredData);
        console.log("📦 Filtered length:", filteredData.length);
        
        setData(filteredData);
      } catch (err) {
        console.error('Error fetching tours:', err);
        setError(err instanceof Error ? err.message : 'An error occurred while fetching tours');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTours();
  }, []);

  const handleSeeAll = (): void => {
    router.push('/destination');
  };

  const handleKnowMore = (packageCode: string): void => {
    router.push(`/tours/package-details?query=${packageCode}`);
  };

  if (loading) {
    return (
      <section className="container mx-auto lg:px-8 py-8 sm:py-10 lg:py-12 mt-16">
        <div className="text-center">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-300 rounded w-32 mx-auto mb-4"></div>
            <div className="h-8 bg-gray-300 rounded w-64 mx-auto mb-8"></div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2 sm:gap-6">
              {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                <div key={i} className="bg-gray-300 rounded-lg h-64 animate-pulse"></div>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="container mx-auto lg:px-8 py-8 sm:py-10 lg:py-12 mt-16">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </section>
    );
  }

  if (!data || data.length === 0) {
    return (
      <section className="container mx-auto lg:px-8 py-8 sm:py-10 lg:py-12 mt-16">
        <div className="text-center">
          <p className="text-gray-600">No tours available at the moment.</p>
        </div>
      </section>
    );
  }

  return (
    <section className="container  px-4 sm:px-6 lg:px-8 py-6 sm:py-8 mt-16">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0 mb-6 sm:mb-8">
        <div className="flex flex-col gap-[14px]">
          <p className="text-[#175CD3] text-[18px] leading-[28px] font-semibold uppercase">
            Curated Tours
          </p>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold leading-tight sm:leading-[60px]">
            Crafted Just for You
          </h2>
        </div>
        <button 
          onClick={handleSeeAll}
          className="bg-[#EFF8FF] text-[#175CD3] px-5 py-4 font-semibold rounded-full shadow-sm flex items-center hover:bg-blue-200 transition-colors duration-300"
        >
          <span className="mr-3">See All</span>
          <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path 
              d="M5.5 12H19.5M19.5 12L12.5 5M19.5 12L12.5 19" 
              stroke="#175CD3" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
          </svg>
        </button>
      </div>

      {/* Grid Layout */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2 sm:gap-6">
        {data.map((tour: Tour) => (
          <div 
            key={tour.id} 
            className="bg-white rounded-lg overflow-hidden  transition-all duration-300 hover:shadow-lg cursor-pointer group"
            onClick={() => handleKnowMore(tour.packageCode)}
          >
            <div className="relative overflow-hidden rounded-2xl">
              <img 
                src={tour.packageMainImage || tour.image || "/assets/dummy_image_new.jpg"} 
                onError={(e: any) => {
                  e.target.onerror = null;
                  e.target.src = "/assets/dummy_image_new.jpg";
                }}
                alt={tour.packageTitle || tour.title} 
                className="w-full h-32 sm:h-40 object-cover group-hover:scale-105 transition-transform duration-300" 
              />
            </div>
            
            <h3 className="text-base sm:text-base font-bold mt-3 sm:mt-4 text-black line-clamp-2 leading-[24px] min-h-[48px]">
              {tour.packageTitle || tour.title}
            </h3>
            
            <div 
              className="text-xs sm:text-xs text-gray-400 mt-2 line-clamp-2 min-h-[30px]" 
              dangerouslySetInnerHTML={{ __html: tour.description }}
            />

            {/* Know More Link */}
            <button 
              onClick={(e) => {
                e.stopPropagation();
                handleKnowMore(tour.packageCode);
              }} 
              className="text-[#175CD3] font-semibold flex items-center text-xs mt-5 hover:text-blue-800 transition-colors duration-300 group"
            >
              Know more 
              <svg 
                width="25" 
                height="24" 
                className="ml-2 transition-transform duration-300 group-hover:translate-x-1"
                viewBox="0 0 25 24" 
                fill="none" 
                xmlns="http://www.w3.org/2000/svg"
              >
                <path 
                  d="M5.5 12H19.5M19.5 12L12.5 5M19.5 12L12.5 19" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        ))}
      </div>
    </section>
  );
};

export default CuratedTours;