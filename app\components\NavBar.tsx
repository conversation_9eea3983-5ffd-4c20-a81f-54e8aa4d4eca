"use client";

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import axios from 'axios';
import DestinationToursDopdown from './NavBarDropDownComponent/DestinationToursDopdown';
import CurrencyDropdown from './CurrencyDropdown/CurrencyDropdown';

const NavigationBar = () => {
  const pathname = usePathname();
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [showDropdown, setShowDropdown] = useState(false);
  const [isSticky, setIsSticky] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const [showDestinationsDropdown, setShowDestinationsDropdown] = useState(false);
  const [showDestinationsDropdownMob, setShowDestinationsDropdownMob] = useState(false);
  

  useEffect(() => {
    // Check authentication status on component mount
    const token = localStorage.getItem('authToken');
    const email = localStorage.getItem('userEmail');
    if (token) {
      setIsAuthenticated(true);
      setUserEmail(email);
      // Set the token in axios default headers
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }
  }, []);

  useEffect(() => {
    // Handle scroll event for sticky navbar
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsSticky(scrollPosition > 0);
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    // Close dropdown when clicking outside
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleMenu = () => setIsMenuOpen((prev) => !prev);
  const toggleDropdown = () => setShowDropdown((prev) => !prev);

  const handleLogout = () => {
    // Clear authentication data
    localStorage.removeItem('authToken');
    localStorage.removeItem('userEmail');
    delete axios.defaults.headers.common['Authorization'];
    setIsAuthenticated(false);
    setUserEmail(null);
    router.push('/login');
  };

  // Function to handle destination clicks
  const handleDestinationClick = (destination: string) => {
    setShowDestinationsDropdown(false);
    router.push(`/vacation/tours?query=${destination}`);
  };

  // Function to handle region clicks
  const handleRegionClick = (region: string) => {
    setShowDestinationsDropdown(false);
    router.push(`/vacation/tours?query=${region.replace(' ', '%20')}`);
  };

  return (
    <header className={`w-full bg-white/80 border-b border-black/10 backdrop-blur-[12px] transition-all duration-300 ${isSticky ? 'fixed top-0 left-0 right-0 z-[1000]' : 'relative z-[1000]'}`}>
      <div className="w-full max-w-[1440px] mx-auto h-16 px-4 sm:px-6 md:px-8 lg:px-16 xl:px-28 flex justify-between items-center">
        {/* Left: Brand Logo */}
        <Link href="/" className="flex-shrink-0">
          <span className="block">
            <img src="/assets/logo.png" className="h-6 sm:h-8 w-auto object-contain" alt="Logo" />
          </span>
        </Link>

        {/* Desktop Navigation Links */}
        <div className="hidden lg:flex items-center gap-4 xl:gap-8">
          {[
            { name: "Home", path: "/" },
            { name: "Vacation", path: "/vacation/tours" },
            { name: "Destinations", path: "/destination", show_dropdown_element: true, component_name: DestinationToursDopdown },
            { name: "Customised Tours", path: "/customizedtours" },
            { name: "Blogs", path: "/blogs" },
            { name: "Contact Us", path: "/contact" }
          ].map((link) => (
            link.show_dropdown_element ? 
            <div key={link.path} className="relative group" 
              onMouseEnter={() => setShowDestinationsDropdown(true)}
              onMouseLeave={() => setShowDestinationsDropdown(false)}
            >
              <button
                onClick={() => {
                  router.push(link.path);
                  setShowDestinationsDropdown(prev => !prev);
                }}
                className={`text-sm font-semibold leading-5 flex items-center gap-1 transition-colors whitespace-nowrap ${
                  pathname.startsWith('/destination') 
                    ? 'text-[#175CD3] font-bold hover:text-[#175CD3]' 
                    : 'text-black font-semibold hover:text-[#175CD3]'
                }`}
              >
                <span>Destinations</span>
                <svg
                  className={`w-4 h-4 transition-transform ${showDestinationsDropdown ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* Dropdown Menu */}
              {showDestinationsDropdown && (
              <div
                className="absolute top-full left-1/2 transform -translate-x-1/2 w-[900px] mt-0 bg-white rounded-xl z-[9999] border border-gray-200 shadow-xl overflow-hidden"
                onMouseEnter={() => setShowDestinationsDropdown(true)}
                onMouseLeave={() => setShowDestinationsDropdown(false)}
                style={{ marginTop: '-1px' }}
              >
                <div className="p-8">
                  <div className="grid grid-cols-4 gap-8">
                    {/* World Regions Column */}
                    <div>
                      <h3 className="text-[#175CD3] text-lg font-semibold mb-6">World Regions</h3>
                      <div className="space-y-3">
                        <div 
                          onClick={() => handleRegionClick('Eastern Europe')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Eastern Europe
                        </div>
                        <div 
                          onClick={() => handleRegionClick('Northern Europe')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Northern Europe
                        </div>
                        <div 
                          onClick={() => handleRegionClick('Southern Europe')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Southern Europe
                        </div>
                        <div 
                          onClick={() => handleRegionClick('Western Europe')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Western Europe
                        </div>
                        <div 
                          onClick={() => handleRegionClick('Central Europe')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Central Europe
                        </div>
                      </div>
                    </div>

                    {/* Europe Column 1 */}
                    <div>
                      <h3 className="text-[#175CD3] text-lg font-semibold mb-6">Europe</h3>
                      <div className="space-y-3">
                        <div 
                          onClick={() => handleDestinationClick('Austria')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Austria
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Belgium')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Belgium
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Croatia')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Croatia
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Czech Republic')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Czech Republic
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Denmark')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Denmark
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Finland')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Finland
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('France')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          France
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Germany')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Germany
                        </div>
                      </div>
                    </div>

                    {/* Europe Column 2 */}
                    <div>
                      <h3 className="text-transparent text-lg font-semibold mb-6">.</h3>
                      <div className="space-y-3">
                        <div 
                          onClick={() => handleDestinationClick('Greece')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Greece
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Hungary')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Hungary
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Iceland')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Iceland
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Ireland')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Ireland
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Italy')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Italy
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Luxembourg')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Luxembourg
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Netherlands')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Netherlands
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Norway')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Norway
                        </div>
                      </div>
                    </div>

                    {/* Europe Column 3 */}
                    <div>
                      <h3 className="text-transparent text-lg font-semibold mb-6">.</h3>
                      <div className="space-y-3">
                        <div 
                          onClick={() => handleDestinationClick('Portugal')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Portugal
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Slovakia')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Slovakia
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Slovenia')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Slovenia
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Spain')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Spain
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Sweden')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Sweden
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('Switzerland')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          Switzerland
                        </div>
                        <div 
                          onClick={() => handleDestinationClick('United Kingdom')}
                          className="text-gray-700 hover:text-[#175CD3] cursor-pointer py-1 text-sm transition-colors"
                        >
                          United Kingdom
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              )}
            </div>
            :
            (
              <div key={link.path}>
                <Link href={link.path}>
                  <span
                    className={`text-sm font-semibold leading-5 hover:text-[#175CD3] transition-colors cursor-pointer whitespace-nowrap ${
                      pathname === link.path ? 'text-[#175CD3] font-bold' : 'text-black font-semibold'
                    }`}
                  >
                    {link.name}
                  </span>
                </Link>
              </div>
            )
          ))}
        </div>

        {/* Desktop Right: Currency + Auth Buttons */}
        <div className="hidden lg:flex items-center gap-2 xl:gap-2.5">
          <div className="">
            <CurrencyDropdown/>
          </div>

          {isAuthenticated ? (
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={toggleDropdown}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
              >
                <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                  <span className="text-blue-600 font-medium text-sm">
                    {userEmail?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <span className="hidden xl:block font-medium">{userEmail}</span>
                <svg
                  className={`w-4 h-4 transition-transform ${showDropdown ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {showDropdown && (
                <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-[9999]">
                  <div className="px-4 py-3 border-b border-gray-100">
                    <p className="text-sm font-medium text-gray-900">{userEmail}</p>
                  </div>
                  <Link
                    href="/myprofile"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    onClick={() => setShowDropdown(false)}
                  >
                    My Profile
                  </Link>
                  <Link
                    href="/mytrips"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    onClick={() => setShowDropdown(false)}
                  >
                    My Bookings
                  </Link>
                  <Link
                    href="/change-password"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    onClick={() => setShowDropdown(false)}
                  >
                    Change Password
                  </Link>
                  <div className="border-t border-gray-100 my-1"></div>
                  <button
                    onClick={handleLogout}
                    className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                  >
                    Logout
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center gap-2 xl:gap-2.5">
              {/* Login Button */}
              <Link href="/login">
                <div className="rounded-lg flex">
                  <div className="px-3 xl:px-[18px] py-2 xl:py-2.5 bg-white shadow-sm rounded-full border border-[#D0D5DD] flex justify-center items-center gap-2">
                    <div className="text-[#344054] text-xs xl:text-sm font-semibold leading-5">Login</div>
                  </div>
                </div>
              </Link>
              
              {/* Sign Up Button */}
              <Link href="/signup">
                <div className="rounded-lg flex">
                  <div className="px-3 xl:px-[18px] py-2 xl:py-2.5 bg-[#1570EF] shadow-sm rounded-full border border-[#1570EF] flex justify-center items-center gap-2">
                    <div className="text-white text-xs xl:text-sm font-semibold leading-5">Sign Up</div>
                  </div>
                </div>
              </Link>
            </div>
          )}
        </div>

        {/* Mobile Menu Button */}
        <button
          className={`lg:hidden flex flex-col items-center justify-center w-10 h-10 space-y-1 bg-white rounded-lg border ${isMenuOpen ? 'hidden' : 'block'}`}
          onClick={toggleMenu}
          aria-label="Open menu"
        >
          <span className="block w-5 h-0.5 bg-gray-800"></span>
          <span className="block w-5 h-0.5 bg-gray-800"></span>
          <span className="block w-5 h-0.5 bg-gray-800"></span>
        </button>
      </div>

      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="lg:hidden fixed inset-0 bg-white z-[9999] min-h-screen">
          <div className="h-full flex flex-col">
            {/* Mobile Header */}
            <div className="flex justify-between items-center p-4 border-b border-gray-200 bg-white">
              <Link href="/" className="flex-shrink-0" onClick={toggleMenu}>
                <img src="/assets/logo.png" className="h-8 w-auto object-contain" alt="Logo" />
              </Link>
              <button
                onClick={toggleMenu}
                className="text-gray-800 hover:text-gray-600 p-2 rounded-lg bg-gray-100"
                aria-label="Close menu"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Mobile Navigation Content */}
            <div className="flex-1 overflow-y-auto">
              <nav className="p-4 space-y-6">
                <ul className="space-y-2">
                  {[
                    { name: "Home", path: "/" },
                    { name: "Vacation", path: "/vacation/tours" },
                    { name: "Destinations", path: "/destination", show_dropdown_element: true, component_name: DestinationToursDopdown },
                    { name: "Customised Tours", path: "/customizedtours" },
                    { name: "Blogs", path: "/blogs" },
                    { name: "Contact Us", path: "/contact" }
                  ].map((link) => (
                    link.show_dropdown_element ? 
                    <li key={link.path} className="">
                      <button
                        onClick={() => {
                          if(showDestinationsDropdownMob){
                            router.push(link.path);
                            setShowDestinationsDropdownMob(false);
                            toggleMenu();
                            return;
                          }
                          setShowDestinationsDropdownMob(prev => !prev);
                        }}
                        className="w-full px-4 py-3 text-base font-medium text-gray-700 hover:text-[#175CD3] hover:bg-gray-50 rounded-lg transition-colors flex justify-between items-center"
                      >
                        <span>Destinations</span>
                        <svg
                          className={`w-5 h-5 transition-transform ${showDestinationsDropdownMob ? 'rotate-180' : ''}`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>

                      {showDestinationsDropdownMob && (
                        <div className="mt-2 ml-4 p-4 bg-gray-50 rounded-lg">
                          <div className="space-y-3">
                            <div>
                              <h4 className="text-[#175CD3] font-semibold mb-2">World Regions</h4>
                              <div className="space-y-1">
                                <div 
                                  onClick={() => {
                                    handleRegionClick('Eastern Europe');
                                    setShowDestinationsDropdownMob(false);
                                    toggleMenu();
                                  }}
                                  className="text-gray-600 py-1 cursor-pointer hover:text-[#175CD3] transition-colors"
                                >
                                  Eastern Europe
                                </div>
                                <div 
                                  onClick={() => {
                                    handleRegionClick('Northern Europe');
                                    setShowDestinationsDropdownMob(false);
                                    toggleMenu();
                                  }}
                                  className="text-gray-600 py-1 cursor-pointer hover:text-[#175CD3] transition-colors"
                                >
                                  Northern Europe
                                </div>
                                <div 
                                  onClick={() => {
                                    handleRegionClick('Southern Europe');
                                    setShowDestinationsDropdownMob(false);
                                    toggleMenu();
                                  }}
                                  className="text-gray-600 py-1 cursor-pointer hover:text-[#175CD3] transition-colors"
                                >
                                  Southern Europe
                                </div>
                                <div 
                                  onClick={() => {
                                    handleRegionClick('Western Europe');
                                    setShowDestinationsDropdownMob(false);
                                    toggleMenu();
                                  }}
                                  className="text-gray-600 py-1 cursor-pointer hover:text-[#175CD3] transition-colors"
                                >
                                  Western Europe
                                </div>
                              </div>
                            </div>
                            <div>
                              <h4 className="text-[#175CD3] font-semibold mb-2">Popular Countries</h4>
                              <div className="space-y-1">
                                <div 
                                  onClick={() => {
                                    handleDestinationClick('Switzerland');
                                    setShowDestinationsDropdownMob(false);
                                    toggleMenu();
                                  }}
                                  className="text-gray-600 py-1 cursor-pointer hover:text-[#175CD3] transition-colors"
                                >
                                  Switzerland
                                </div>
                                <div 
                                  onClick={() => {
                                    handleDestinationClick('Germany');
                                    setShowDestinationsDropdownMob(false);
                                    toggleMenu();
                                  }}
                                  className="text-gray-600 py-1 cursor-pointer hover:text-[#175CD3] transition-colors"
                                >
                                  Germany
                                </div>
                                <div 
                                  onClick={() => {
                                    handleDestinationClick('France');
                                    setShowDestinationsDropdownMob(false);
                                    toggleMenu();
                                  }}
                                  className="text-gray-600 py-1 cursor-pointer hover:text-[#175CD3] transition-colors"
                                >
                                  France
                                </div>
                                <div 
                                  onClick={() => {
                                    handleDestinationClick('Italy');
                                    setShowDestinationsDropdownMob(false);
                                    toggleMenu();
                                  }}
                                  className="text-gray-600 py-1 cursor-pointer hover:text-[#175CD3] transition-colors"
                                >
                                  Italy
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </li> 
                    :
                    <li key={link.path}>
                      <Link
                        href={link.path}
                        className="block px-4 py-3 text-base font-medium text-gray-700 hover:text-[#175CD3] hover:bg-gray-50 rounded-lg transition-colors"
                        onClick={toggleMenu}
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
                
                {/* Currency Dropdown Mobile */}
                <div className="px-4 py-3 border-t border-gray-200">
                  <CurrencyDropdown/>
                </div>
                
                {/* Auth Section Mobile */}
                <div className="px-4 border-t border-gray-200 pt-4">
                  {isAuthenticated ? (
                    <div className="space-y-3">
                      <div className="px-4 py-3 text-base font-medium text-gray-900 bg-gray-100 rounded-lg">
                        {userEmail}
                      </div>
                      <Link
                        href="/myprofile"
                        className="block px-4 py-3 text-base font-medium text-gray-700 hover:text-[#175CD3] hover:bg-gray-50 rounded-lg transition-colors"
                        onClick={toggleMenu}
                      >
                        My Profile
                      </Link>
                      <Link
                        href="/mytrips"
                        className="block px-4 py-3 text-base font-medium text-gray-700 hover:text-[#175CD3] hover:bg-gray-50 rounded-lg transition-colors"
                        onClick={toggleMenu}
                      >
                        My Bookings
                      </Link>
                      <Link
                        href="/change-password"
                        className="block px-4 py-3 text-base font-medium text-gray-700 hover:text-[#175CD3] hover:bg-gray-50 rounded-lg transition-colors"
                        onClick={toggleMenu}
                      >
                        Change Password
                      </Link>
                      <button
                        onClick={() => {
                          handleLogout();
                          toggleMenu();
                        }}
                        className="w-full px-4 py-3 text-base font-medium text-red-600 hover:bg-red-50 rounded-lg transition-colors text-left"
                      >
                        Logout
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <Link
                        href="/login"
                        className="block w-full px-4 py-3 text-base font-medium text-[#344054] border border-[#D0D5DD] hover:bg-gray-50 rounded-lg transition-colors text-center"
                        onClick={toggleMenu}
                      >
                        Login
                      </Link>
                      <Link
                        href="/signup"
                        className="block w-full px-4 py-3 text-base font-medium text-white bg-[#1570EF] hover:bg-blue-700 rounded-lg transition-colors text-center"
                        onClick={toggleMenu}
                      >
                        Sign Up
                      </Link>
                    </div>
                  )}
                </div>
              </nav>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default NavigationBar;