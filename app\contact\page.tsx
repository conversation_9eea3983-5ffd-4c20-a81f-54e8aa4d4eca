"use client";

import { useEffect, useState } from 'react';
import NavigationBar from '../components/NavBar';
import Footer from '../components/Footer';
import Breadcrumb from '../components/BreadCrumbs';
import axios from 'axios';
import toast from 'react-hot-toast';

const ContactPage = () => {
  const [showPopup, setShowPopup] = useState(false);
const [code, setCode] = useState<string | null>(null);

useEffect(() => {
  if (typeof window !== 'undefined') {
    const searchParams = new URLSearchParams(window.location.search);
    const queryCode = searchParams.get('code');
    setCode(queryCode);
  }
}, []);
  const initial_data = {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    travelingWith: '',
    destination: '',
    travelDate: '',
    subject: '',
    message: '',
    howFar: '',
    whereToStay: ''
  };
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    travelingWith: '',
    destination: '',
    travelDate: '',
    subject: '',
    message: '',
    howFar: '',
    whereToStay: ''
  });
  const [isLoading,setIsLoading] = useState(false);
  const handleSubmit = async (e: React.FormEvent) => {
    setIsLoading(true);
    e.preventDefault();
    try {
          let data_payload:any ={
              "name": formData.firstName + ' ' + formData.lastName,
              "email": formData.email,
              "contactNo": formData.phone,
              "howFarReached": formData.howFar,
              "destination": formData.destination,
              // "travelDate": "2023-12-25",
              "travelDate": formData.travelDate,
              "travelingWith": formData.travelingWith,
              "stayPreference": formData.whereToStay,
              "subject": formData.subject,
              "additionalInfo": formData.message,  
              // "code": code,
          };
          if(code){
            data_payload.code = code;
          }
          const response = await axios.post(
            // `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/change-password`,
            `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/travel-requests`,
            data_payload
            // {
            //   // formData
            //   "name": formData.firstName + ' ' + formData.lastName,
            //   "email": formData.email,
            //   "contactNo": formData.phone,
            //   "howFarReached": formData.howFar,
            //   "destination": formData.destination,
            //   // "travelDate": "2023-12-25",
            //   "travelDate": formData.travelDate,
            //   "travelingWith": formData.travelingWith,
            //   "stayPreference": formData.whereToStay,
            //   "subject": formData.subject,
            //   "additionalInfo": formData.message,  
            // }
          );
          console.log(response.data);
          if(response.status == 200){
            setShowPopup(true);
            // toast.success('Thank you for reaching out! We’ll get back to you soon!');
            setIsLoading(false);
            setFormData(initial_data);
          } else {
            alert("Failed to submit request. Please try again.");
          }
          setIsLoading(false);
        } catch (error) {
        alert("Failed to submit request. Please try again.");
        setIsLoading(false);
    }
    console.log(formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };
  
  const inputClassName = "mt-1 block w-full px-4 py-2 rounded-xl border border-gray-300 focus:border-[#1570EF] focus:ring-[#1570EF] bg-white";
  const selectClassName = "mt-1 block w-full px-4 py-2 rounded-xl border border-gray-300 focus:border-[#1570EF] focus:ring-[#1570EF] bg-white";
  const textareaClassName = "mt-1 block w-full px-4 py-2 rounded-xl border border-gray-300 focus:border-[#1570EF] focus:ring-[#1570EF] bg-white";
 if (isLoading) {
    return (
      <div className="py-12 px-4 sm:px-6 md:px-8 lg:px-12 mt-4">
        <div className="text-center">
          <p className="text-lg text-gray-600">Completing your request...</p>
        </div>
      </div>
    );
  }
  return (
    <>
    <main className="min-h-screen bg-white">
      <NavigationBar />
      <Breadcrumb />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Get in touch</h1>
          <p className="text-lg text-gray-600">Our friendly team would love to hear from you.</p>
        </div>

        {/* Contact Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {/* Visit Us Card */}
          <div className="bg-white p-6 rounded-xl shadow-sm border flex items-center space-x-4">
            <div className="w-12 h-12 bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900">Visit Us</h3>
              <p className="text-gray-600">New Delhi, Delhi, India 110070</p>
            </div>
          </div>

          {/* Mail Us Card */}
          <div className="bg-white p-6 rounded-xl shadow-sm border flex items-center space-x-4">
            <div className="w-12 h-12 bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900">Mail Us</h3>
              <p className="text-gray-600"><EMAIL></p>
            </div>
          </div>

          {/* Call Us Card */}
          <div className="bg-white p-6 rounded-xl shadow-sm border flex items-center space-x-4">
            <div className="w-12 h-12 bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900">Call Us</h3>
              <p className="text-gray-600">+91 9717559499</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">First Name</label>
                  <input
                    type="text"
                    placeholder='Your First Name'
                    id="firstName"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    className={inputClassName}
                    required
                  />
                </div>
                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">Last Name</label>
                  <input
                    type="text"
                    placeholder='Your Last Name'
                    id="lastName"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    className={inputClassName}
                    required
                  />
                </div>
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email</label>
                <input
                  type="email"
                  placeholder='Your Email'
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={inputClassName}
                  required
                />
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700">Phone Number</label>
                <input
                  type="tel"
                  id="phone"
                  placeholder='Mobile Number'
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  className={inputClassName}
                  required
                />
              </div>

              {
              !code &&
              <>
              <div>
                <label htmlFor="travelingWith" className="block text-sm font-medium text-gray-700">Whom are you traveling with?</label>
                <select
                  id="travelingWith"
                  name="travelingWith"
                  value={formData.travelingWith}
                  onChange={handleChange}
                  className={selectClassName}
                >
                  <option value="">Select an option</option>
                  <option value="Travelling Alone">Travelling Alone</option>
                  <option value="With a spouse/partner">With a spouse/partner</option>
                  <option value="Family with kids">Family with kids</option>
                  <option value="Elders are traveling">Elders are traveling</option>
                  <option value="Group more than 6">Group more than 6</option>
                </select>
              </div>

              <div>
                <label htmlFor="whereToStay" className="block text-sm font-medium text-gray-700">Where would you like to stay?</label>
                <select
                  id="whereToStay"
                  name="whereToStay"
                  value={formData.whereToStay}
                  onChange={handleChange}
                  className={selectClassName}
                >
                  <option value="">Select an option</option>
                  <option value="Hostels (neat and clean shared accommodation)">Hostels (neat and clean shared accommodation)</option>
                  <option value="1 or 2 star (simple, basic, neat and clean)">1 or 2 star (simple, basic, neat and clean)</option>
                  <option value="3 star (deluxe amenities, quality service)">3 star (deluxe amenities, quality service)</option>
                  <option value="4 star (comfort, care, superior amenities)">4 star (comfort, care, superior amenities)</option>
                  <option value="5 star">5 star</option>
                </select>
              </div>

              <div>
                <label htmlFor="howFar" className="block text-sm font-medium text-gray-700">How far have you reached?</label>
                <select
                  id="howFar"
                  name="howFar"
                  value={formData.howFar}
                  onChange={handleChange}
                  className={selectClassName}
                >
                  <option value="">Select an option</option>
                  <option value="Searching for a destination">Searching for a destination</option>
                  <option value="Decided my destination">Decided my destination</option>
                  <option value="Booked the flight">Booked the flight</option>
                  <option value="Confused, help me">Confused, help me</option>
                 
                </select>
              </div>

              <div>
                <label htmlFor="destination" className="block text-sm font-medium text-gray-700">Your Destination</label>
                <input
                  type="text"
                  id="destination"
                  name="destination"
                  value={formData.destination}
                  onChange={handleChange}
                  className={inputClassName}
                />
              </div>

              <div>
                <label htmlFor="travelDate" className="block text-sm font-medium text-gray-700">When do you want to travel?</label>
                <input
                  type="date"
                  id="travelDate"
                  name="travelDate"
                  value={formData.travelDate}
                  onChange={handleChange}
                  className={inputClassName}
                />
              </div>

              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700">Subject</label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  className={inputClassName}
                />
              </div>
              </>
              }
              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700">Anything else you want us to know?</label>
                <textarea
                  id="message"
                  name="message"
                  rows={4}
                  value={formData.message}
                  onChange={handleChange}
                  className={textareaClassName}
                />
              </div>

              <div>
                <button
                  type="submit"
                  className="w-full flex justify-center py-3 px-4 border border-transparent rounded-full shadow-sm text-sm font-medium text-white bg-[#1570EF] hover:bg-[#1565DF] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1570EF] transition-all duration-300"
                >
                  Send message
                </button>
              </div>
            </form>
          </div>

          {/* Image Section */}
          <div className="hidden lg:block">
            <div className="relative h-full">
              <img
                src="https://images.unsplash.com/photo-1542435503-956c469947f6?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=774&q=80"
                alt="Contact us - Travel planning"
                className="w-full h-full object-cover rounded-xl shadow-lg"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-xl"></div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </main>
    {showPopup && (
  <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
    <div className="bg-white rounded-2xl p-8 max-w-md w-full shadow-lg text-center">
      <h2 className="text-2xl font-bold text-green-600 mb-4">Thank You!</h2>
      <p className="text-gray-700">Your request has been submitted successfully.<br />We’ll get back to you soon!</p>
      <button
        className="mt-6 bg-blue-600 text-white px-6 py-2 rounded-xl hover:bg-blue-700"
        onClick={() => setShowPopup(false)}
      >
        Close
      </button>
    </div>
  </div>
)}
    
    </>
  );
};

export default ContactPage; 