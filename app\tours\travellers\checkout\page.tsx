"use client";

import { useEffect, useState, useContext } from 'react';
import { useSearchParams } from 'next/navigation';
import NavigationBar from '@/app/components/NavBar';
import Breadcrumb from '@/app/components/BreadCrumbs';
import Footer from '@/app/components/Footer';
import Checkout from '@/app/components/Checkout';
import { AppContext } from '@/app/context/useAppContext';

import { Suspense } from 'react';
// import CheckoutPage from './CheckoutPage'; // move logic to this file

function ItalyTourPageRender() {
  const { bookingData } = useContext(AppContext);
  const searchParams = useSearchParams();
  const [breadcrumbs, setBreadcrumbs] = useState<any>([]);

  useEffect(() => {
    const packageCode = bookingData?.packageData?.packageCode || "";
    const custom = searchParams.get('custom');

    const url_packagedetail = custom === "true"
      ? `/tours/package-details?custom=true&query=${packageCode}`
      : `/tours/package-details?query=${packageCode}`;

    const url_traveller = custom === "true"
      ? `/tours/package-details/travellers?custom=true`
      : `/tours/package-details/travellers`;

    const breadCrumb = [
      {
        label: "Tours",
        link: "/vacation/tours"
      },
      {
        label: "Package-Details",
        link: url_packagedetail
      },
      {
        label: "Travellers",
        link: url_traveller
      },
      {
        label: "Checkout",
        link: "/last"
      }
    ];

    setBreadcrumbs(breadCrumb);
  }, [searchParams, bookingData]);

  return (
    <main className="min-h-screen">
      <NavigationBar />
      <Breadcrumb breadCrumb={breadcrumbs} />
      <Checkout />
      <Footer />
    </main>
  );
}

export default function ItalyTourPage() {
  return (
    <Suspense fallback={<div></div>}>
      <ItalyTourPageRender />
    </Suspense>
  );
}

 
