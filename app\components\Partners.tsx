import React from "react";

const partners = [
  "/assets/partners/imageone.png",
  "/assets/Estimated_partners_2_cropped.png",
  "/assets/partners/imagethree.png",
  "/assets/partners/imagefour.png",
  "/assets/partners/imagefive.png",
  "/assets/partners/imagesix.png",
];

const EsteemedPartners = () => {
  return (
    <div className="bg-[#EFF8FF] py-10 sm:py-12 lg:py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h3 className="text-[#175CD3] text-base sm:text-lg leading-7 font-medium mb-8 sm:mb-10 lg:mb-12 uppercase text-center tracking-wide">
          OUR ESTEEMED PARTNERS
        </h3>
        
        {/* Partner logos grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-6 sm:gap-8 lg:gap-4 xl:gap-6 justify-items-center items-center">
          {partners.map((logo, index) => (
            <div 
              key={index} 
              className="w-full max-w-[120px] sm:max-w-[140px] lg:max-w-[160px] flex justify-center items-center p-3 sm:p-4 bg-white/50 hover:bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"
            >
              <img 
                src={logo} 
                alt={`Partner ${index + 1}`} 
                className="h-8 sm:h-10 lg:h-12 w-auto object-contain filter grayscale hover:grayscale-0 transition-all duration-300 group-hover:scale-105" 
              />
            </div>
          ))}
        </div>
        
        {/* Optional: Add a subtle animation or additional content */}
        <div className="mt-8 sm:mt-10 lg:mt-12 text-center">
          <p className="text-[#175CD3]/70 text-sm font-light">
            Trusted by leading organizations worldwide
          </p>
        </div>
      </div>
    </div>
  );
};

export default EsteemedPartners;