import dynamic from 'next/dynamic';
import { routeData } from './constaants';
const MapComponent = dynamic(() => import('./MapComponent'), {
  ssr: false,
});
// https://nominatim.openstreetmap.org/search?q=paris&format=json
// https://api.opencagedata.com/geocode/v1/json?q=prague&key=********************************

export default function MapPage({packageData,data_to_plot}:any) {
	const result: any[] = [];
  const days = packageData.days || [];
	days.forEach((day:any, dayIndex:any) => {
    day.cities.forEach((city:any, cityIndex:any) => {
      const name = city.title.split(";")[0].trim();
      const excursions = city.itineraries;

      // Fallback coordinates
      let lat = 0, lng = 0;

      for (const ex of excursions) {
        const selected = ex.selected_excursion;
        if (selected?.meetingPointLocationLatitude && selected?.meetingPointLocationLongitude) {
          lat = parseFloat(selected.meetingPointLocationLatitude);
          lng = parseFloat(selected.meetingPointLocationLongitude);
          break;
        }
        if (selected?.hotelLocationLatitude && selected?.hotelLocationLongitude) {
          lat = parseFloat(selected.hotelLocationLatitude);
          lng = parseFloat(selected.hotelLocationLongitude);
          break;
        }
      }

      const isFirst = dayIndex === 0 && cityIndex === 0;
      const isLast = dayIndex === days.length - 1;

      const type = isFirst
        ? 'start'
        : isLast
        ? 'end'
        : city.title.toLowerCase().includes('self guided') || city.title.toLowerCase().includes('optional')
        ? 'optional'
        : 'visited';

      // Look for transport
      let transport = undefined;
      for (const ex of excursions) {
        if (ex.excursionType === 'intercity' && ex.selected_excursion?.transportDetails?.[0]?.transferMode) {
          transport = ex.selected_excursion.transportDetails[0].transferMode.toLowerCase();
          break;
        }
      }

      result.push({
        name,
        lat,
        lng,
        type,
        ...(transport ? { transport } : {}),
      });
    });
  });
  let data_render = data_to_plot?.length > 0 ? data_to_plot : routeData;
  if(data_to_plot.length==0){
    return null;
  }
  return (
    <div className="p-4">
			<h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-5">Destination Route</h2>
      <MapComponent routeData={data_to_plot} />
    </div>
  );
}