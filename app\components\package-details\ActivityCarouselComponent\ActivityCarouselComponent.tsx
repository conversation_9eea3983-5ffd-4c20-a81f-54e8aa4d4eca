import React, { useEffect } from 'react'
import Slider from 'react-slick'
import { NextArrow, PrevArrow } from '../../BlogSection';
import ImageCarousel from './ActivityCarousal';

const ActivityCarouselComponent = ({images,youtubelink=[]}:any) => {
    useEffect(()=>{
         
        console.log("images",images);
    },[])
    
  return (
    <ImageCarousel
        images={images}
        fallbackImage={ '/assets/tours/default-image.jpg'}
        altText={ 'Tour package'}
        youtubelink={youtubelink}
        />
        
  )
}

export default ActivityCarouselComponent