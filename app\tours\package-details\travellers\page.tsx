"use client";

import { useEffect, useState, Suspense, useContext } from 'react';
import { useSearchParams } from 'next/navigation';
import NavigationBar from '@/app/components/NavBar';
import Breadcrumb from '@/app/components/BreadCrumbs';
import Footer from '@/app/components/Footer';
import TravellerSection from '@/app/components/TravellerSection';
import { AppContext } from '@/app/context/useAppContext';

// Inner component that uses `useSearchParams`
function TravellerPageRender({bookingData}:any) {
  const searchParams = useSearchParams();
  const [breadcrumbs, setBreadcrumbs] = useState<{ label: string; link: string }[]>([]);

  useEffect(() => {
    const packageCode = bookingData?.packageCode || bookingData?.packageData?.packageCode;
    const custom = searchParams.get('custom');

    const url_packagedetail = custom === "true"
      ? `/tours/package-details?custom=true&query=${packageCode}`
      : `/tours/package-details?query=${packageCode}`;

    const breadCrumb = [
      {
        label: "Tours",
        link: "/vacation/tours"
      },
      {
        label: "Package-Details",
        link: url_packagedetail
      },
      {
        label: "Travellers",
        link: "/last"
      }
    ];

    setBreadcrumbs(breadCrumb);
  }, [searchParams]);

  return (
    <main className="min-h-screen">
      <NavigationBar />
      <Breadcrumb breadCrumb={breadcrumbs} />
      <TravellerSection />
      <Footer />
    </main>
  );
}

// Suspense wrapper
export default function ItalyTourPage() {
  const {bookingData,setBookingData} = useContext(AppContext);
  return (
    <Suspense fallback={<div></div>}>
      <TravellerPageRender bookingData={bookingData}/>
    </Suspense>
  );
}
