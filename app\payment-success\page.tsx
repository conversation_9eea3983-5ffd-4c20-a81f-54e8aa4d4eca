"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import crypto from 'crypto';

const PaymentSuccess = () => {
  const router = useRouter();

  useEffect(() => {
    const verifyPayment = () => {
      try {
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        
        // PayU response parameters
        const status = urlParams.get('status');
        const txnid = urlParams.get('txnid');
        const amount = urlParams.get('amount');
        const productinfo = urlParams.get('productinfo');
        const firstname = urlParams.get('firstname');
        const email = urlParams.get('email');
        const hash = urlParams.get('hash');
        const key = urlParams.get('key');
        
        // Verify the response hash
        const PAYU_SALT = "6ACACh";
        const hashString = `${PAYU_SALT}|${status}|||||${urlParams.get('udf5')}|${urlParams.get('udf4')}|${urlParams.get('udf3')}|${urlParams.get('udf2')}|${urlParams.get('udf1')}|${email}|${firstname}|${productinfo}|${amount}|${txnid}|${key}`;
        const calculatedHash = crypto.createHash('sha512').update(hashString).digest('hex');

        if (hash === calculatedHash && status === 'success') {
          // Payment is verified
          // You can make an API call here to update the booking status in your backend
          
          // Redirect to bookings page after 3 seconds
          setTimeout(() => {
            router.push('/my-bookings');
          }, 3000);
        } else {
          // Invalid hash or failed payment
          router.push('/payment-failure');
        }
      } catch (error) {
        console.error('Error verifying payment:', error);
        router.push('/payment-failure');
      }
    };

    verifyPayment();
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
          <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Payment Successful!</h3>
        <p className="text-sm text-gray-500 mb-6">Your booking has been confirmed. You will be redirected to your bookings page shortly.</p>
        <button
          onClick={() => router.push('/my-bookings')}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
        >
          View My Bookings
        </button>
      </div>
    </div>
  );
};

export default PaymentSuccess; 