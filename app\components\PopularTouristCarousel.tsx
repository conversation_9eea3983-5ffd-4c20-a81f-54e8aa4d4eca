"use client";
import React, { useRef, useState, useEffect } from "react";
import Slider from "react-slick";
import axios from "axios";

type Tour = {
  packageCode: number;
  packageTitle: string;
  packageMainImage: string;
  priceSummary: string;
  duration: string;
  country: string;
  flag: string;
};

type PopularToursProps = {
  tours: Tour[];
};

const PopularTours: React.FC<PopularToursProps> = () => {
  const NEXT_PUBLIC_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
  const sliderRef = useRef<Slider | null>(null);
  const [tours, setTours] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTours = async () => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/packages/website/featured`); // API call using Axios
      const filtered_response = response?.data?.filter((ele:any)=>ele.status.toLowerCase() == "active");
      setTours(filtered_response);
      setError(null); // Clear error if successful
    } catch (err) {
      setError("Error loading tours. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTours(); // Fetch data on mount
  }, []);
  

  if (!tours.length) return <div></div>; // Show loading state
  if (error) return <div className="text-red-500">{error}</div>;

  const settings = {
    infinite: true,
    speed: 500,
    slidesToShow: 4.5, // 4 full images + 2 half images
    slidesToScroll: 1,
    centerMode: true,
    centerPadding: "2%",
    focusOnSelect: true,
    arrows: false,
    responsive: [
      {
        breakpoint: 1280, // Large screens
        settings: {
          slidesToShow: 3.5,
          centerPadding: "5%",
        },
      },
      {
        breakpoint: 1024, // Medium screens
        settings: {
          slidesToShow: 2.5,
          centerPadding: "10%",
        },
      },
      {
        breakpoint: 768, // Tablets
        settings: {
          slidesToShow: 1.5,
          centerPadding: "15%",
        },
      },
      {
        breakpoint: 480, // Mobile
        settings: {
          slidesToShow: 1,
          centerPadding: "10%",
        },
      },
    ],
  };

  return (
    <section className="relative w-full overflow-hidden bg-white py-10">
      {/* Title & Arrows */}
      <div className="flex justify-between items-center px-8 mb-6">
        <div>
          <p className="text-gray-500 text-lg">Popular Tours</p>
          <h2 className="text-3xl font-bold text-gray-800">Our Customers Loved The Most</h2>
        </div>
        <div className="flex space-x-4">
          <button
            onClick={() => sliderRef.current?.slickPrev()}
            className="border border-blue-600 p-2 rounded-full hover:text-white transition"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
              <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" className="text-blue-600" />
            </svg>
          </button>
          <button
            onClick={() => sliderRef.current?.slickNext()}
            className="border border-blue-600 p-2 rounded-full hover:text-white transition"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
              <path strokeLinecap="round" strokeLinejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" className="text-blue-600" />
            </svg>
          </button>
        </div>
      </div>

      {/* Carousel */}
      <div className="w-screen">
        <Slider ref={sliderRef} {...settings}>
          {tours.map((tour) => (
            <div key={tour.id} className="px-3">
              {/* Tour Card */}
              <div className="rounded-2xl overflow-hidden cursor-pointer">
                {/* Image */}
                <img
                  src={tour.image}
                  alt={tour.title}
                  className="w-full h-60 object-cover rounded-2xl"
                />

                {/* Content Below Image */}
                <div className="py-3 sm:py-4">
                    <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold text-gray-800 line-clamp-2 leading-tight">
                        {tour.title}
                    </h3>
                    
                    <div className="flex justify-between items-center mt-2 text-gray-600">
                        <div className="flex-1 min-w-0">
                        <p className="text-xs sm:text-sm">Starting from</p>
                        <p className="text-sm sm:text-base md:text-lg font-semibold text-gray-900 truncate">
                            {tour.price} / person
                        </p>
                        </div>
                        <p className="text-xs sm:text-sm font-medium ml-2 flex-shrink-0">
                        {tour.duration}
                        </p>
                    </div>
                    
                    <div className="mt-2 sm:mt-3">
                        <span className="inline-flex items-center bg-gray-200 text-gray-800 px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm whitespace-nowrap">
                        <span className="mr-1">{tour.flag}</span>
                        <span className="truncate">{tour.country}</span>
                        </span>
                    </div>
                    
                    <button className="bg-blue-100 text-blue-700 w-full font-bold mt-3 sm:mt-4 py-2 rounded-full hover:bg-blue-200 transition text-xs sm:text-sm md:text-base">
                        Book Nothing
                    </button>
                    </div>
              </div>
            </div>
          ))}
        </Slider>
      </div>
    </section>
  );
};

export default PopularTours;
