# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.*
*.pem

# Testing
/coverage

# Next.js
/.next/
/out/
/build

# Environment variables
.env*.local
.env.development
.env.staging
.env.production
.env.test

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.DS_Store
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Build files
.next/
out/
build/
dist/

# Cache directories
.cache/
.temp/
.tmp/

# Misc
.vercel
.vercel_build_output
.next/cache/

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Local development
.env.local

# Production
.next/cache/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# System Files
.DS_Store
Thumbs.db

# Lock files (if not committing them)
# package-lock.json
yarn.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Production
build

# Debug logs from npm
npm-debug.log*

# Local development
.env.development.local
.env.test.local
.env.production.local


#custom file
update-env-vars.js
update-localhost-vars.js