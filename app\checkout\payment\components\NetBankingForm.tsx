import { useState, ChangeEvent } from 'react';

const BANKS = [
  { code: 'HDFC', name: 'HDFC Bank' },
  { code: 'ICICI', name: 'ICICI Bank' },
  { code: 'SBI', name: 'State Bank of India' },
  { code: 'AXIS', name: 'Axis Bank' },
  { code: 'KOTAK', name: 'Kotak Mahindra Bank' },
  { code: 'YES', name: 'Yes Bank' },
  { code: 'CITI', name: 'Citibank' },
  { code: 'BO<PERSON>', name: 'Bank of Baroda' },
  { code: 'PNB', name: 'Punjab National Bank' },
  { code: 'OTHER', name: 'Other Banks' },
];

interface NetBankingFormProps {
  onSubmit: (bankCode: string) => void;
  isSubmitting: boolean;
}

export const NetBankingForm = ({ onSubmit, isSubmitting }: NetBankingFormProps) => {
  const [selectedBank, setSelectedBank] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (!selectedBank) {
      setError('Please select a bank');
      return;
    }
    
    onSubmit(selectedBank);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Bank
        </label>
        <div className="space-y-2 max-h-60 overflow-y-auto p-2 border rounded-lg">
          {BANKS.map((bank) => (
            <div key={bank.code} className="flex items-center p-2 hover:bg-gray-50 rounded">
              <input
                id={`bank-${bank.code}`}
                type="radio"
                name="bank"
                value={bank.code}
                checked={selectedBank === bank.code}
                onChange={(e) => setSelectedBank(e.target.value)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <label
                htmlFor={`bank-${bank.code}`}
                className="ml-3 block text-sm font-medium text-gray-700"
              >
                {bank.name}
              </label>
            </div>
          ))}
        </div>
        {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
      </div>

      <button
        type="submit"
        disabled={isSubmitting || !selectedBank}
        className={`w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium ${
          isSubmitting ? 'opacity-70 cursor-not-allowed' : 'hover:bg-green-700'
        }`}
      >
        {isSubmitting ? 'Processing...' : 'Proceed to Net Banking'}
      </button>
    </form>
  );
};
