"use client";

import NavigationBar from '@/app/components/NavBar';
import Breadcrumb from '@/app/components/BreadCrumbs';
import PackageDetails from '@/app/components/PackageDetails';
import Footer from '@/app/components/Footer';
import ParentComponent from '@/app/components/PopularToursParents';
import { LoadScript } from '@react-google-maps/api';
import { Suspense, useRef, useState } from 'react';

const PageContent = () => {
  const componentRef = useRef<any>(null); 
  const breadCrumb = [
    {
      label:"Tours",
      link:"/vacation/tours"
    },
    {
      label:"Package-Details",
      link:"/last"
    },
  ];
  const [loading,setLoading] = useState(false);
  return (
  // <LoadScript googleMapsApiKey="AIzaSyDVYB1GofLKV56yqxseuasNRX0nNLKWOQg">
    <main className="min-h-screen" ref={componentRef}>
      <NavigationBar />
      <Breadcrumb breadCrumb={breadCrumb}/>
      <PackageDetails componentRefData={componentRef} setLoadingForDownload={setLoading}/>
      <ParentComponent />
      <Footer />
      {loading &&
      <div className="fixed inset-0 z-50 bg-gray-200/50 flex items-center justify-center w-screen h-screen">
      <div className="text-xl font-semibold text-black">Downloading in progress..</div>
    </div>}
    </main>
  // {/* </LoadScript> */}
  );
};

const Page = () => {
  return (
    <Suspense fallback={<div></div>}>
      <PageContent />
    </Suspense>
  );
};

export default Page;