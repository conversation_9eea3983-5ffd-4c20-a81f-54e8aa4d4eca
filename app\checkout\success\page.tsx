"use client";

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { usePathname } from 'next/navigation';
import Layout from '@/app/components/Layout';
import Image from 'next/image';
import ParentComponent from '@/app/components/PopularToursParents';
import axios from 'axios';

// Get the base URL based on the environment
const getApiBaseUrl = () => {
  if (typeof window !== 'undefined') {
    // Client-side: use the current origin
    return window.location.origin;
  }
  // Server-side: use environment variable or fallback
  return process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';
};

const API_BASE_URL = getApiBaseUrl();

// Helper function to safely parse URL parameters
const getSearchParams = (search: string) => {
  try {
    return new URLSearchParams(search);
  } catch (error) {
    console.error('Error parsing search params:', error);
    return new URLSearchParams();
  }
};

interface PackageDetails {
  packageCode: string;
  packageTitle: string;
  packageMainImage: string;
  priceSummary: {
    grossSellingPrice: number;
    netSellingPrice: number;
  };
  noOfDays: number;
  noOfNights: number;
}

const PaymentStatus = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const [paymentStatus, setPaymentStatus] = useState<'success' | 'failure' | 'pending'>('pending');
  const [paymentDetails, setPaymentDetails] = useState<any>(null);
  const [packageDetails, setPackageDetails] = useState<PackageDetails | null>(null);
  const [hasRefreshed, setHasRefreshed] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasProcessedCallback, setHasProcessedCallback] = useState(false);

  // Safe navigation helper
  const handleNavigation = (path: string) => {
    try {
      router.push(path);
    } catch (err) {
      console.error('Navigation error:', err);
      window.location.href = path;
    }
  };

  const handleContinue = () => {
    handleNavigation('/');
  };

  const handleTryAgain = () => {
    try {
      router.back();
    } catch (error) {
      console.error('Error going back:', error);
      handleNavigation('/checkout/payment');
    }
  };

  const checkPaymentStatus = async (txnid: string) => {
    try {
      console.log('Checking payment status for transaction ID:', txnid);
      const response = await axios.get(`${API_BASE_URL}/api/payments/status/${txnid}`, {
        timeout: 10000, // 10 second timeout
        validateStatus: (status) => status < 500 // Don't throw for 4xx errors
      });
      console.log('Payment status received:', response.data);
      if (response.status === 200) {
        const status = response.data.status;
        if (status === 'success') {
          setPaymentStatus('success');
        } else if (status === 'failure') {
          setPaymentStatus('failure');
        } else {
          setPaymentStatus('pending');
        }
      } else {
        console.error('Error checking payment status:', response.status, response.statusText);
      }
    } catch (err) {
      console.error('Error checking payment status:', err);
    }
  };

  // Effect 1: Handle URL parameters and initial status
  useEffect(() => {
    try {
      console.log('Effect 1: Checking URL parameters');
      
      // Get search params from URL if not available from Next.js router
      const urlParams = new URLSearchParams(window.location.search);
      const status = urlParams.get('status') || searchParams?.get('status') || '';
      const txnid = urlParams.get('txnid') || searchParams?.get('txnid');
      
      console.log('Status from URL:', status);
      console.log('Transaction ID:', txnid);

      // If we have a transaction ID but no status, try to get status from server
      if (txnid && !status) {
        console.log('Found transaction ID but no status, checking payment status...');
        checkPaymentStatus(txnid);
        return;
      }

      if (!hasRefreshed && !status && !txnid) {
        console.log('No status or transaction ID found, refreshing...');
        setHasRefreshed(true);
        window.location.reload();
        return;
      }

      // Set initial payment status
      if (status === 'success') {
        setPaymentStatus('success');
      } else if (status === 'failure') {
        setPaymentStatus('failure');
      } else {
        setPaymentStatus('pending');
      }

      // Set payment details from URL
      const details = {
        txnid: urlParams.get('txnid') || searchParams?.get('txnid') || '',
        amount: urlParams.get('amount') || searchParams?.get('amount') || '',
        productinfo: urlParams.get('productinfo') || searchParams?.get('productinfo') || '',
        firstname: urlParams.get('firstname') || searchParams?.get('firstname') || '',
        email: urlParams.get('email') || searchParams?.get('email') || '',
        phone: urlParams.get('phone') || searchParams?.get('phone') || ''
      };
      
      console.log('Payment details:', details);
      setPaymentDetails(details);

    } catch (err) {
      console.error('Error in Effect 1:', err);
      setError('Error processing payment status: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  }, [searchParams, hasRefreshed]);

  // Effect 2: Handle payment callback
  useEffect(() => {
    const processPaymentCallback = async () => {
      if (paymentStatus === 'pending' || hasProcessedCallback) return;
      
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const txnid = urlParams.get('txnid');
        const amount = urlParams.get('amount');
        const email = urlParams.get('email');
        const hash = urlParams.get('hash');
        const errorParam = urlParams.get('error');
        const errorMessage = urlParams.get('error_Message');

        if (!txnid) {
          console.log('No transaction ID found for callback');
          return;
        }

        console.log('Processing payment callback for transaction:', txnid);
        
        const callbackData = {
          txnid,
          status: paymentStatus,
          ...(paymentStatus === 'success' 
            ? {
                hash: hash || '',
                amount: amount || '',
                email: email || ''
              } 
            : {
                error: errorParam || 'payment_failed',
                error_Message: errorMessage || 'Payment failed'
              })
        };

        const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4000';
        const response = await fetch(`${API_BASE}/api/payment/callback`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(callbackData),
        });

        if (!response.ok) {
          throw new Error(`Callback failed with status ${response.status}`);
        }

        console.log('Payment callback successful');
        setHasProcessedCallback(true);
      } catch (err) {
        console.error('Error in payment callback:', err);
        // Don't show error to user, just log it
      }
    };

    if (typeof window !== 'undefined') {
      // processPaymentCallback();
    }
  }, [paymentStatus, hasProcessedCallback]);

  // Effect 3: Handle package details fetch
  useEffect(() => {
    const fetchPackageDetails = async () => {
      try {
        console.log('Effect 2: Checking package details requirements');
        if (paymentStatus !== 'success') {
          console.log('Skipping package fetch - payment not successful');
          return;
        }

        const urlParams = new URLSearchParams(window.location.search);
        const packageCode = urlParams.get('packageCode') || searchParams?.get('packageCode');
        
        if (!packageCode) {
          console.log('Skipping package fetch - no package code');
          return;
        }

        console.log('Fetching package details for:', packageCode);
        console.log('Using API base URL:', API_BASE_URL);
        
        const response = await axios.get(`${API_BASE_URL}/api/packages/website/${packageCode}`, {
          timeout: 10000, // 10 second timeout
          validateStatus: (status) => status < 500 // Don't throw for 4xx errors
        });
        
        console.log('Package details received:', response.data);
        
        if (response.status === 200) {
          setPackageDetails(response.data);
        } else {
          console.error('Error fetching package details:', response.status, response.statusText);
        }

      } catch (err) {
        console.error('Error in Effect 2:', err);
        if (axios.isAxiosError(err)) {
          console.error('Axios error details:', {
            message: err.message,
            code: err.code,
            status: err.response?.status,
            statusText: err.response?.statusText,
            data: err.response?.data
          });
        }
      }
    };

    // Only run on client side
    if (typeof window !== 'undefined') {
      fetchPackageDetails();
    }
  }, [searchParams, paymentStatus, hasProcessedCallback]);

  // Show error state if critical error occurred
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold mb-2">Something went wrong</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => router.push('/')}
            className="bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors"
          >
            Return Home
          </button>
        </div>
      </div>
    );
  }

  // Show loading state while processing
  if (paymentStatus === 'pending') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#175CD3] mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Processing your payment...</p>
        </div>
      </div>
    );
  }

  console.log('Rendering PaymentStatus component with:', {
    paymentStatus,
    hasPaymentDetails: !!paymentDetails,
    hasPackageDetails: !!packageDetails
  });

  return (
    <div className="w-full px-4 md:px-20 py-8">
      <div className="space-y-6">
        {paymentStatus === 'success' && (
          <>
            {/* Success Message */}
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 rounded-full bg-green-400 flex items-center justify-center flex-shrink-0 mt-4">

              </div>
              <div>
                <h1 className="text-2xl font-semibold text-gray-900 mb-1">
                  Congratulations! Your payment was successful.
                </h1>
                <p className="text-black text-sm">
                  You will soon receive the booking confirmation and other details on your registered email address.
                </p>
              </div>
            </div>

            {/* Payment Details Card */}
            <div className="bg-white rounded-2xl overflow-hidden">
              <div className="flex flex-col md:flex-row">
                {/* Left - Image */}
                <div className="relative w-full md:w-80 h-52 overflow-hidden rounded-2xl">
                  <Image 
                    src={packageDetails?.packageMainImage || '/assets/tours/imagetwo.png'}
                    alt={packageDetails?.packageTitle || 'Tour Package'}
                    fill
                    className="object-cover"
                    sizes="320px"
                    priority
                  />
                </div>
                
                {/* Middle - Payment Details */}
                <div className="flex-1 p-6">
                  {/* Title */}
                  <h2 className="text-lg font-semibold text-gray-900 mb-3">
                    {packageDetails?.packageTitle || paymentDetails?.productinfo || 'Tour Package'}
                  </h2>

                  {/* Package Info */}
                  <div className="flex items-center gap-4 mb-4 text-sm text-gray-600">
                    <span>{packageDetails?.noOfDays || 0} Days</span>
                    <span>•</span>
                    <span>{packageDetails?.noOfNights || 0} Nights</span>
                  </div>

                  {/* Payment Info Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <div className="text-xs text-gray-500">Transaction ID</div>
                      <div className="text-sm font-medium text-gray-900">{paymentDetails?.txnid}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Amount Paid</div>
                      <div className="text-sm font-medium text-gray-900">₹{paymentDetails?.amount}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Name</div>
                      <div className="text-sm font-medium text-gray-900">{paymentDetails?.firstname}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Email</div>
                      <div className="text-sm font-medium text-gray-900">{paymentDetails?.email}</div>
                    </div>
                  </div>
                </div>

                {/* Right - Continue Button */}
                <div className="flex items-center justify-end p-6">
                  <button
                    onClick={handleContinue}
                    className="bg-blue-700 text-white px-8 py-4 rounded-full text-sm hover:bg-blue-800 whitespace-nowrap transition-colors"
                  >
                    Continue to Home
                  </button>
                </div>
              </div>
            </div>
          </>
        )}

        {paymentStatus === 'failure' && (
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Payment Failed</h1>
            <p className="text-gray-600 mb-6">
              {paymentDetails?.error_Message || 'We were unable to process your payment. Please try again.'}
            </p>
            
            {paymentDetails && (
              <div className="bg-gray-50 p-6 rounded-lg max-w-md mx-auto mb-6">
                <h2 className="text-lg font-semibold mb-4">Error Details</h2>
                <div className="space-y-2">
                  <p><span className="font-medium">Error Code:</span> {paymentDetails.error}</p>
                  <p><span className="font-medium">Error Message:</span> {paymentDetails.error_Message}</p>
                  <p><span className="font-medium">Transaction ID:</span> {paymentDetails.txnid}</p>
                </div>
              </div>
            )}

            <button
              onClick={handleTryAgain}
              className="bg-[#175CD3] text-white px-8 py-3 rounded-full font-medium hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

const SuccessPage = () => {
  return (
    <Layout>
      <div className="max-w-7xl mx-auto">
        <Suspense fallback={
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 mx-auto mb-4"></div>
              <p className="text-lg text-gray-600">Loading payment status...</p>
            </div>
          </div>
        }>
          <PaymentStatus />
        </Suspense>
      </div>
      <ParentComponent />
    </Layout>
  );
};

export default SuccessPage; 