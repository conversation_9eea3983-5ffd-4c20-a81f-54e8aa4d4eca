"use client";

import NavigationBar from '@/app/components/NavBar';
import Breadcrumb from '@/app/components/BreadCrumbs';
import PackageDetails from '@/app/components/PackageDetails';
import Footer from '@/app/components/Footer';
import ParentComponent from '@/app/components/PopularToursParents';
import { Suspense } from 'react';

const PageContent = () => {
  return (
    <main className="min-h-screen">
      <NavigationBar />
      <Breadcrumb />
      <PackageDetails />
      <ParentComponent />
      <Footer />
    </main>
  );
};

const Page = () => {
  return (
    <Suspense fallback={<div></div>}>
      <PageContent />
    </Suspense>
  );
};

export default Page;