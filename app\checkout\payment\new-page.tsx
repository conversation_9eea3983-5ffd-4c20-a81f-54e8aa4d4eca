"use client";

import { Suspense, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { FaArrowLeft } from 'react-icons/fa';
import { PaymentMethods } from './components/PaymentMethods';
import { fetchUserDetails } from './services/paymentService';

const PaymentPageContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  
  // Get payment details from URL parameters
  const amount = searchParams?.get('amount') || '0';
  const packageId = searchParams?.get('packageId') || '';
  const bookingId = searchParams?.get('bookingId') || '';
  const installmentId = searchParams?.get('installmentId') || '';
  const title = decodeURIComponent(searchParams?.get('title') || '');
  const duration = searchParams?.get('duration') || '';

  // Validate required parameters on component mount
  useEffect(() => {
    const validateParams = async () => {
      try {
        if (!searchParams) {
          throw new Error('Search params not available');
        }

        if (!amount || amount === '0') {
          throw new Error('Invalid amount');
        }

        if (!packageId) {
          throw new Error('Package ID is required');
        }

        if (!bookingId) {
          throw new Error('Booking ID is required');
        }

        if (!installmentId) {
          throw new Error('Installment ID is required');
        }

        // Verify user is authenticated
        const token = localStorage.getItem('authToken');
        if (!token) {
          router.push(`/login?redirect=/checkout/payment?${searchParams.toString()}`);
          return;
        }

        // Pre-fetch user details to ensure user exists
        await fetchUserDetails();
        
      } catch (err) {
        const error = err as Error;
        console.error('Payment validation error:', error);
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    validateParams();
  }, [searchParams, amount, packageId, bookingId, installmentId, router]);

  const handlePaymentSuccess = () => {
    // Handle successful payment (e.g., show success message, redirect to confirmation)
    console.log('Payment successful!');
    // router.push(`/booking/confirmation?bookingId=${bookingId}`);
  };

  const handlePaymentError = (error: Error) => {
    console.error('Payment error:', error);
    setError(error.message || 'An error occurred during payment');
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  
  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-4">
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-700" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">
                {error}
              </p>
              <button
                onClick={() => router.back()}
                className="mt-2 inline-flex items-center text-sm font-medium text-red-700 hover:text-red-600"
              >
                <FaArrowLeft className="mr-1" /> Go back
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }


  return (
    <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
      <div className="mb-6">
        <button
          onClick={() => router.back()}
          className="flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium mb-4"
        >
          <FaArrowLeft className="mr-2" /> Back to Checkout
        </button>
        <h1 className="text-2xl font-bold text-gray-900">Complete Your Payment</h1>
        <p className="mt-1 text-sm text-gray-500">
          Secure payment processed through our trusted payment partners
        </p>
      </div>

      <PaymentMethods
        amount={amount}
        packageId={packageId}
        bookingId={bookingId}
        installmentId={installmentId}
        title={title}
        duration={duration}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentError={handlePaymentError}
      />
    </div>
  );
};

const PaymentPage = () => {
  return (
    <Suspense
      fallback={
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      }
    >
      <PaymentPageContent />
    </Suspense>
  );
};

export default PaymentPage;
