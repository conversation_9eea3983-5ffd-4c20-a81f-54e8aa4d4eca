import { useCurrencyStore } from "@/app/store/useCurrencyStore";
import { useEffect } from "react";

export default function CurrencyDropdown() {
  const setRates = useCurrencyStore(state => state.setRates);
  const rates = useCurrencyStore(state => state.rates);
  const selectedCurrency = useCurrencyStore(state => state.selectedCurrency);
  const setSelectedCurrency = useCurrencyStore(state => state.setSelectedCurrency);
  let high_priority_rates =['INR', 'USD', 'EUR', 'AUD', 'NZD', 'SGD', 'GBP', 'CHF'];
  useEffect(() => {
    fetch("https://api.fxratesapi.com/latest")
      .then(res => res.json())
      .then(data => {
        if (data?.success) setRates(data.rates);
      });
  }, []);

  return (
    <div className="inline-block w-full  sm:max-w-none">
      <select
        value={selectedCurrency}
        onChange={e => setSelectedCurrency(e.target.value)}
        className="appearance-none bg-white text-sm px-4 py-2 w-full border border-gray-300 shadow-sm hover:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200 transition cursor-pointer
          rounded-md sm:rounded-full"
      >
        {[...high_priority_rates,...Object.keys(rates).filter((ele)=>!high_priority_rates.includes(ele))].map(currency => (
          <option key={currency} value={currency}>
            {currency}
          </option>
        ))}
      </select>
    </div>
  );
}




// import { useCurrencyStore } from "@/app/store/useCurrencyStore";
// import { useEffect } from "react";

// export default function CurrencyDropdown() {
//   const setRates = useCurrencyStore(state => state.setRates);
//   const rates = useCurrencyStore(state => state.rates);
//   const selectedCurrency = useCurrencyStore(state => state.selectedCurrency);
//   const setSelectedCurrency = useCurrencyStore(state => state.setSelectedCurrency);
//   // const convertFromINR = useCurrencyStore(state => state.convertFromINR);
// 	// if(typeof(window)!="undefined"){
// 	// 	(window as any).convertFromINR = convertFromINR;
// 	// }

//   useEffect(() => {
//     fetch("https://api.fxratesapi.com/latest")
//       .then(res => res.json())
//       .then(data => {
//         if (data?.success) setRates(data.rates);
//       });
//   }, []);

//   return (
//     <div className="inline-block">
//       <select
//         value={selectedCurrency}
//         onChange={e => setSelectedCurrency(e.target.value)}
//         className="appearance-none bg-white text-sm px-4 py-1.5 rounded-full border border-gray-300 shadow-sm hover:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200 transition cursor-pointer"
//       >
//         {Object.keys(rates).map(currency => (
//           <option key={currency} value={currency}>
//             {currency}
//           </option>
//         ))}
//       </select>
//     </div>
//   );
// }
