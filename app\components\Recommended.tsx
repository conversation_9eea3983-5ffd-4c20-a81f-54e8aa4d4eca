"use client";

import React, { useEffect, useRef } from "react";
import Slider from "react-slick";
import { useRouter } from "next/navigation";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { countryCodeToEmoji, countryFlags } from "./DestinationTours";
import RecommendedCard from "./RecommendedCard/RecommendedCard";

interface CardCarouselProps {
  tours: any;
  loading?: boolean;
  error?: string;
}

const CardCarousel: React.FC<CardCarouselProps> = ({ tours, loading, error }) => {
  const sliderRef = useRef<Slider | null>(null);
  const router = useRouter();

  const handleBookNow = (packageCode: number) => {
    router.push(`/tours/package-details?query=${packageCode}`);
  };

  const settings = {
    infinite: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    arrows: false,
    centerMode: true,
    centerPadding: "100px", // controls half-visible cards
    responsive: [
      {
        breakpoint: 1280,
        settings: {
          slidesToShow: 3,
          centerPadding: "80px",
        },
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          centerPadding: "60px",
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
          centerPadding: "40px",
        },
      },
    ],
  };

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!sliderRef.current) return;
      if (event.key === "ArrowRight" || event.key === "d") {
        sliderRef.current.slickNext();
      } else if (event.key === "ArrowLeft" || event.key === "a") {
        sliderRef.current.slickPrev();
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  if (loading) {
    return (
      <section className="relative py-8">
        <div className="flex justify-between items-start pb-16 px-8">
          <div className="flex flex-col gap-[14px]">
            <p className="text-gray-500 text-lg">TOP DESTINATION</p>
            <h2 className="text-3xl font-bold text-gray-800 font-size-48">Explore top destinations</h2>
          </div>
        </div>
        <div className="flex justify-center items-center h-72">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="relative py-8 px-8">
        <div className="flex justify-between items-start pb-16">
          <div className="px-[80px]">
            <p className="text-[#175CD3] text-sm sm:text-lg font-semibold mb-2 sm:mb-4">RECOMMENDED FOR YOU</p>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800">We think {"you'll"} love this</h2>
          </div>
        </div>
        <div className="flex justify-center items-center h-72">
          <p className="text-red-600">{error}</p>
        </div>
      </section>
    );
  }

  return (
    <>
      {/* <section className="px-4 container mx-auto lg:px-32 py-8 sm:py-10 lg:py-5 mt-[100px]"> */}
      <section className="p-4 container mx-auto max-w-7xl pt-0 py-8 sm:py-10 lg:py-5 mt-[100px]">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0">
          <div>
            <p className="text-[#175CD3] text-lg font-semibold mb-4">RECOMMENDED FOR YOU</p>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800">We think {"you'll"} love this</h2>
          </div>
          <div className="flex space-x-4">
            <button
              onClick={() => sliderRef.current?.slickPrev()}
              className="border border-[#175CD3] p-2 rounded-full"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-[#175CD3]" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
              </svg>
            </button>
            <button
              onClick={() => sliderRef.current?.slickNext()}
              className="border border-[#175CD3] p-2 rounded-full"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-[#175CD3]" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
              </svg>
            </button>
          </div>
        </div>
      </section>

      <section className="relative py-8 mt-6 -mx-2 pb-0">
        <Slider ref={sliderRef} {...settings}>
          {tours.map((tour: any) => (
            <RecommendedCard key={tour.packageCode} tour={tour} handleBookNow={handleBookNow}/>
          ))}
        </Slider>
      </section>
    </>
  );
};

export default CardCarousel;




// "use client";

// import React, { useEffect, useRef } from "react";
// import Slider from "react-slick";
// import { useRouter } from "next/navigation";
// import "slick-carousel/slick/slick.css";
// import "slick-carousel/slick/slick-theme.css";

// interface CardCarouselProps {
//   tours: any;
//   loading?: boolean;
//   error?: string;
// }

// const CardCarousel: React.FC<CardCarouselProps> = ({ tours, loading, error }) => {
//   const sliderRef = useRef<Slider | null>(null);
//   const router = useRouter();

//   const handleBookNow = (packageCode: number) => {
//     router.push(`/tours/package-details?query=${packageCode}`);
//   };

//   const settings = {
//     infinite: true,
//     speed: 500,
//     slidesToShow: 5,
//     slidesToScroll: 1,
//     arrows: false,
//     centerMode: false,
//     variableWidth: false,
//     responsive: [
//       {
//         breakpoint: 1024,
//         settings: {
//           slidesToShow: 3,
//         },
//       },
//       {
//         breakpoint: 768,
//         settings: {
//           slidesToShow: 2,
//         },
//       },
//       {
//         breakpoint: 480,
//         settings: {
//           slidesToShow: 1,
//         },
//       },
//     ],
//   };

//   useEffect(() => {
//     const handleKeyDown = (event: KeyboardEvent) => {
//       if (!sliderRef.current) return;
//       if (event.key === "ArrowRight" || event.key === "d") {
//         sliderRef.current.slickNext();
//       } else if (event.key === "ArrowLeft" || event.key === "a") {
//         sliderRef.current.slickPrev();
//       }
//     };
//     window.addEventListener("keydown", handleKeyDown);
//     return () => window.removeEventListener("keydown", handleKeyDown);
//   }, []);

//   if (loading) {
//     return (
//       <section className="relative py-8">
//         <div className="flex justify-between items-start pb-16 px-8">
//           <div className="flex flex-col gap-[14px]">
//             <p className="text-gray-500 text-lg">TOP DESTINATION</p>
//             <h2 className="text-3xl font-bold text-gray-800 font-size-48">Explore top destinations</h2>
//           </div>
//         </div>
//         <div className="flex justify-center items-center h-72">
//           <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
//         </div>
//       </section>
//     );
//   }

//   if (error) {
//     return (
//       <section className="relative py-8 px-8">
//         <div className="flex justify-between items-start pb-16">
//           <div className="px-[80px]">
//             <p className="text-[#175CD3] text-[18px] font-semibold mb-2">RECOMMENDED FOR YOU</p>
//             <h2 className="text-5xl font-bold text-gray-800 font-size-48">We think you'll love this</h2>
//           </div>
//         </div>
//         <div className="flex justify-center items-center h-72">
//           <p className="text-red-600">{error}</p>
//         </div>
//       </section>
//     );
//   }

//   return (
//     <>
//       <section className="px-4 container mx-auto lg:px-32 py-8 sm:py-10 lg:py-5 mt-[100px]">
//         <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0">
//           <div>
//             <p className="text-[#175CD3] text-lg font-semibold mb-4">RECOMMENDED FOR YOU</p>
//             <h2 className="text-5xl font-bold text-gray-800">We think you'll love this</h2>
//           </div>
//           <div className="flex space-x-4">
//             <button
//               onClick={() => sliderRef.current?.slickPrev()}
//               className="border border-[#175CD3] p-2 rounded-full"
//             >
//               <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-[#175CD3]" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor">
//                 <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
//               </svg>
//             </button>
//             <button
//               onClick={() => sliderRef.current?.slickNext()}
//               className="border border-[#175CD3] p-2 rounded-full"
//             >
//               <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-[#175CD3]" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor">
//                 <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
//               </svg>
//             </button>
//           </div>
//         </div>
//       </section>

//       <section className="relative py-8 mt-6 -mx-2 pb-0">
//         <Slider ref={sliderRef} {...settings}>
//           {tours.map((tour: any) => (
//             <div key={tour.packageCode} className="px-2">
//               <div className="bg-white rounded-2xl overflow-hidden h-[460px] flex flex-col justify-between">
//                 {tour.packageMainImage ? (
//                   <img
//                     src={tour.packageMainImage}
//                     alt={tour.packageTitle}
//                     className="w-full h-52 object-cover rounded-2xl"
//                   />
//                 ) : (
//                   <div className="w-full h-52 bg-gray-200 flex items-center justify-center rounded-2xl">
//                     <span className="text-gray-500">No image available</span>
//                   </div>
//                 )}
//                 {/* <div className="p-4 flex flex-col justify-between flex-1"> */}
//                 <div className="p-4 px-0 flex flex-col flex-1">
//                   <div>
//                     <h3 className="text-[16px] font-bold leading-[24px] line-clamp-2 min-h-[48px] mb-2 text-gray-800">
//                       {tour.packageTitle}
//                     </h3>
//                     <div className="flex justify-between items-start mt-2 text-gray-600">
//                       <div>
//                         <p className="text-[12px] text-[#667085]">Starting from</p>
//                         <div className="flex items-center gap-2 text-sm">
//                           <p className="font-semibold text-black">{tour.priceSummary || '₹10,000'}</p>
//                           <p className="text-[14px] font-semibold text-black">per person</p>
//                         </div>
//                       </div>
//                       <div className="text-right text-black">
//                         <p className="text-[12px]">
//                           <span className="font-bold">{tour.noOfDays}</span> Days{" "}
//                           <span className="font-bold">{tour.noOfNights}</span> Nights
//                         </p>
//                       </div>
//                     </div>
//                     <div className="mt-3 flex flex-wrap gap-2">
//                       {tour.country.split(",").map((country: any, index: any) => (
//                         <span
//                           key={index}
//                           className="inline-flex items-center bg-[#F2F4F7] text-[#344054] px-3 py-1 rounded-full text-[12px] font-medium"
//                         >
//                           {tour.flag} {country.trim()}
//                         </span>
//                       ))}
//                     </div>
//                   </div>
//                   <button
//                     onClick={() => handleBookNow(tour.packageCode)}
//                     className="bg-[#EFF8FF] text-[#175CD3] w-full font-semibold text-[14px] mt-2 py-2 rounded-full shadow-sm hover:bg-blue-200 transition"
//                   >
//                     Book Now
//                   </button>
//                 </div>
//               </div>
//             </div>
//           ))}
//         </Slider>

//         {/* <div className="flex justify-center mt-6">
//           <button
//             onClick={() => router.push("/vacation")}
//             className="bg-[#EFF8FF] text-[#175CD3] px-5 py-4 font-semibold rounded-full shadow-sm flex items-center hover:bg-blue-200 transition text-base"
//           >
//             See All
//             <svg style={{ marginLeft: 10 }} width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
//               <path d="M5.5 12H19.5M19.5 12L12.5 5M19.5 12L12.5 19" stroke="#175CD3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
//             </svg>
//           </button>
//         </div> */}
//       </section>
//     </>
//   );
// };

// export default CardCarousel;
