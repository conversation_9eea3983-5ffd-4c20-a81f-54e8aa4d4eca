"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { countryCodeToEmoji, countryFlags } from "./DestinationTours";
import PopularTouristCard from "./PopularTouristCard/PopularTouristCard";

type Tour = {
  packageCode: number;
  packageTitle: string;
  packageMainImage: string;
  priceSummary: string;
  noOfDays: number;
  noOfNights: number;
  country: string;
  flag: string;
};

type PopularToursProps = {
  tours?: Tour[];
  heading_test?: any;
  title_test?: any;
};

const PopularTours: React.FC<PopularToursProps> = ({ 
  tours = [],
  heading_test = "",
  title_test = "" 
}: any) => {
  const router = useRouter();

  const handleBookNow = (packageCode: number) => {
    router.push(`/tours/package-details?query=${packageCode}`);
  };
  
  return (
    <section className="relative w-full px-[20px] mt-16">
      <div className="container mx-auto  lg:px-8">
        {/* Header Section - Single Line Layout */}
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-end mb-8 lg:mb-12">
          {/* Title Section */}
          <div className="flex-1 mb-6 lg:mb-0">
            <p className="text-[#175CD3] text-sm sm:text-lg font-semibold mb-2 sm:mb-4">
              {heading_test !== "" ? heading_test : "Popular Tours"}
            </p>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800">
              {title_test !== "" ? title_test : "Our customers loved the most"}
            </h2>
          </div>

          {/* See All Button */}
          <div className="flex-shrink-0">
            <button 
              onClick={() => router.push('/destination')}
              className="bg-[#EFF8FF] text-[#175CD3] px-6 py-3 font-semibold rounded-full shadow-sm flex items-center hover:bg-blue-200 transition text-base" 
              style={{ fontWeight: 500 }}
            >
              <span className="mr-3">See All</span>
              <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5.5 12H19.5M19.5 12L12.5 5M19.5 12L12.5 19" stroke="#175CD3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>
        </div>

        {/* Grid Layout */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 sm:gap-6 lg:gap-6">
          {[...tours].sort(() => Math.random() - 0.5).slice(0, 8).map((tour: any) => (
            <PopularTouristCard key={tour.packageCode} tour={tour} handleBookNow={handleBookNow}/>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PopularTours;