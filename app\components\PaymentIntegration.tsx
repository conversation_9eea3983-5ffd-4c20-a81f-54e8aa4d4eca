"use client";

import React, { useState } from 'react';
import axios from 'axios';

interface PaymentIntegrationProps {
  amount: number;
  tourPackage: string;
  packageId: string;
  duration: string;
  onSuccess: () => void;
  onError: (error: string) => void;
}

const PaymentIntegration: React.FC<PaymentIntegrationProps> = ({
  amount,
  tourPackage,
  packageId,
  duration,
  onSuccess,
  onError,
}) => {
  const [loading, setLoading] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null);
  const [upiId, setUpiId] = useState('');

  const initiatePayment = async () => {
    setLoading(true);
    try {
      // Generate a unique order ID using timestamp and random string
      const orderId = `ORDER_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Get user details from localStorage
      const userEmail = localStorage.getItem('userEmail') || '';
      const customerMobile = localStorage.getItem('userMobile') || '';
      const customerName = localStorage.getItem('userName') || '';

      // Store orderId in localStorage for verification
      localStorage.setItem('currentOrderId', orderId);

      // Prepare the request payload for Paysharp API
      const paymentData = {
        orderId: orderId,
        amount: amount,
        customerId: userEmail,
        customerName: customerName,
        customerMobileNo: customerMobile,
        customerEmail: userEmail,
        remarks: `Payment for ${tourPackage} Tour Package`,
        callbackUrl: `${window.location.origin}/payment-callback`
      };

      // Make API call to Paysharp
      const response = await axios.post(
        'https://api.paysharp.in/order/intent',
        paymentData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer v238b43a922108716c0d19b17eb766d855a4e6cb9229a2db3c372213e63a34760c'
          }
        }
      );

      if (response.data.code === 200) {
        window.open(response.data.data.intentUrl, '_blank');
        startPaymentStatusCheck(orderId);
      } else {
        onError('Payment initialization failed');
      }
    } catch (error: any) {
      onError(error.response?.data?.message || 'Payment initialization failed');
    } finally {
      setLoading(false);
    }
  };

  const startPaymentStatusCheck = async (orderId: string) => {
    let attempts = 0;
    const maxAttempts = 12;
    
    const checkStatus = async () => {
      try {
        const response = await axios.get(
          `https://api.paysharp.in/order/status/${orderId}`,
          {
            headers: {
              'Authorization': 'Bearer v238b43a922108716c0d19b17eb766d855a4e6cb9229a2db3c372213e63a34760c'
            }
          }
        );

        if (response.data.code === 200) {
          const status = response.data.data.status;
          if (status === 'SUCCESS') {
            onSuccess();
            return;
          } else if (status === 'FAILED') {
            onError('Payment failed');
            return;
          }
        }

        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(checkStatus, 10000);
        } else {
          onError('Payment timeout. Please check your bookings for status.');
        }
      } catch (error) {
        onError('Error checking payment status');
      }
    };

    checkStatus();
  };

  return (
    <div className="max-w-7xl mx-auto p-4">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Left Column - Booking Details */}
        <div className="md:w-1/3">
          <h2 className="text-lg font-medium mb-4">Booking Details</h2>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">Package ID: {packageId}</p>
            <h3 className="text-lg font-medium">{tourPackage}</h3>
            <p className="text-sm text-gray-600">{duration}</p>
            <div className="mt-6">
              <p className="text-sm text-gray-600">Total Payable</p>
              <p className="text-3xl font-bold">₹ {amount.toLocaleString()}</p>
            </div>
            <div className="flex items-center gap-2 text-blue-600">
              <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <span className="text-sm">100% Safe and Secure Payment</span>
            </div>
          </div>
        </div>

        {/* Right Column - Payment Methods */}
        <div className="md:w-2/3">
          <h2 className="text-lg font-medium mb-4">Select Payment Method</h2>
          
          {/* Debit/Credit Card Accordion */}
          <div className="mb-3">
            <button
              onClick={() => setSelectedPaymentMethod(selectedPaymentMethod === 'card' ? null : 'card')}
              className="w-full flex items-center justify-between p-4 bg-white border rounded-full hover:border-blue-500"
            >
              <div className="flex items-center gap-3">
                
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M28 6H4C3.46957 6 2.96086 6.21071 2.58579 6.58579C2.21071 6.96086 2 7.46957 2 8V24C2 24.5304 2.21071 25.0391 2.58579 25.4142C2.96086 25.7893 3.46957 26 4 26H28C28.5304 26 29.0391 25.7893 29.4142 25.4142C29.7893 25.0391 30 24.5304 30 24V8C30 7.46957 29.7893 6.96086 29.4142 6.58579C29.0391 6.21071 28.5304 6 28 6ZM28 8V11H4V8H28ZM28 24H4V13H28V24ZM26 21C26 21.2652 25.8946 21.5196 25.7071 21.7071C25.5196 21.8946 25.2652 22 25 22H21C20.7348 22 20.4804 21.8946 20.2929 21.7071C20.1054 21.5196 20 21.2652 20 21C20 20.7348 20.1054 20.4804 20.2929 20.2929C20.4804 20.1054 20.7348 20 21 20H25C25.2652 20 25.5196 20.1054 25.7071 20.2929C25.8946 20.4804 26 20.7348 26 21ZM18 21C18 21.2652 17.8946 21.5196 17.7071 21.7071C17.5196 21.8946 17.2652 22 17 22H15C14.7348 22 14.4804 21.8946 14.2929 21.7071C14.1054 21.5196 14 21.2652 14 21C14 20.7348 14.1054 20.4804 14.2929 20.2929C14.4804 20.1054 14.7348 20 15 20H17C17.2652 20 17.5196 20.1054 17.7071 20.2929C17.8946 20.4804 18 20.7348 18 21Z" fill="#175CD3"/>
</svg>

                <span className="font-medium">Debit / Credit Card</span>
              </div>
              <svg className={`w-5 h-5 transition-transform ${selectedPaymentMethod === 'card' ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>

          {/* Net Banking Accordion */}
          <div className="mb-3">
            <button
              onClick={() => setSelectedPaymentMethod(selectedPaymentMethod === 'netbanking' ? null : 'netbanking')}
              className="w-full flex items-center justify-between p-4 bg-white border rounded-full hover:border-blue-500"
            >
              <div className="flex items-center gap-3">
                <svg className="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                <span className="font-medium">Net Banking</span>
              </div>
              <svg className={`w-5 h-5 transition-transform ${selectedPaymentMethod === 'netbanking' ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>

          {/* UPI Accordion */}
          <div className="mb-3">
            <button
              onClick={() => setSelectedPaymentMethod(selectedPaymentMethod === 'upi' ? null : 'upi')}
              className="w-full flex items-center justify-between p-4 bg-white border rounded-lg hover:border-blue-500"
            >
              <div className="flex items-center gap-3">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M26 10C26 10.2652 25.8946 10.5196 25.7071 10.7071C25.5196 10.8946 25.2652 11 25 11H20.9813C20.9925 11.165 21 11.3313 21 11.5C20.9977 13.4884 20.2068 15.3947 18.8007 16.8007C17.3947 18.2068 15.4884 18.9977 13.5 19H11.5863L20.6725 27.26C20.7715 27.3478 20.8521 27.4544 20.9096 27.5736C20.9671 27.6928 21.0003 27.8222 21.0074 27.9543C21.0145 28.0865 20.9953 28.2187 20.951 28.3434C20.9066 28.4681 20.8379 28.5827 20.7489 28.6806C20.6599 28.7785 20.5524 28.8578 20.4325 28.9139C20.3126 28.97 20.1828 29.0017 20.0506 29.0072C19.9184 29.0127 19.7864 28.9919 19.6623 28.9461C19.5381 28.9002 19.4244 28.8302 19.3275 28.74L8.3275 18.74C8.17814 18.6042 8.07336 18.4265 8.02693 18.2301C7.9805 18.0336 7.99459 17.8278 8.06735 17.6395C8.14011 17.4512 8.26814 17.2894 8.4346 17.1753C8.60106 17.0611 8.79817 17 9 17H13.5C14.9582 16.9983 16.3562 16.4184 17.3873 15.3873C18.4184 14.3562 18.9983 12.9582 19 11.5C19 11.3313 18.9913 11.165 18.9762 11H9C8.73478 11 8.48043 10.8946 8.29289 10.7071C8.10536 10.5196 8 10.2652 8 10C8 9.73478 8.10536 9.48043 8.29289 9.29289C8.48043 9.10536 8.73478 9 9 9H18.3962C17.9357 8.09745 17.2347 7.33968 16.3707 6.8103C15.5067 6.28092 14.5133 6.0005 13.5 6H9C8.73478 6 8.48043 5.89464 8.29289 5.70711C8.10536 5.51957 8 5.26522 8 5C8 4.73478 8.10536 4.48043 8.29289 4.29289C8.48043 4.10536 8.73478 4 9 4H25C25.2652 4 25.5196 4.10536 25.7071 4.29289C25.8946 4.48043 26 4.73478 26 5C26 5.26522 25.8946 5.51957 25.7071 5.70711C25.5196 5.89464 25.2652 6 25 6H18.5925C19.4845 6.82542 20.1631 7.85483 20.57 9H25C25.2652 9 25.5196 9.10536 25.7071 9.29289C25.8946 9.48043 26 9.73478 26 10Z" fill="#175CD3"/>
                </svg>
                <span className="font-medium">UPI</span>
              </div>
              <svg className={`w-5 h-5 transition-transform ${selectedPaymentMethod === 'upi' ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            {selectedPaymentMethod === 'upi' && (
              <div className="mt-4 p-4 border-t">
                <p className="text-sm text-gray-600 mb-4">Choose App</p>
                <div className="flex gap-6 mb-4">
                  <img src="/assets/gpay.png" alt="Google Pay" className="h-8 cursor-pointer" />
                  <img src="/assets/paytm.png" alt="Paytm" className="h-8 cursor-pointer" />
                  <img src="/assets/phonepe.png" alt="PhonePe" className="h-8 cursor-pointer" />
                  <img src="/assets/amazonpay.png" alt="Amazon Pay" className="h-8 cursor-pointer" />
                </div>
                <div className="text-center text-sm text-gray-500 my-4">or</div>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={upiId}
                    onChange={(e) => setUpiId(e.target.value)}
                    placeholder="eg. 9876543210@axis"
                    className="flex-1 p-3 border rounded-lg text-sm"
                  />
                  <button 
                    disabled={!upiId.trim()}
                    className={`px-12 py-3 rounded-full text-sm font-medium transition-colors
                      ${upiId.trim() 
                        ? 'border border-gray-300 hover:border-blue-500 text-gray-900' 
                        : 'border border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed'}`}
                  >
                    Verify
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentIntegration; 