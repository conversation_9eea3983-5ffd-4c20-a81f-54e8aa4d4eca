"use client";

import React, { useEffect, useRef } from "react";
import Slider from "react-slick";
import { useRouter } from "next/navigation";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { useQueryStore } from "../store/queryStore";

type CardProps = {
  title: string;
  description: string;
  imageUrl: string;
  onClick?: () => void;
};

interface CardCarouselProps {
  cards: CardProps[];
  loading?: boolean;
  error?: string;
}

const Card: React.FC<CardProps> = ({ title, description, imageUrl, onClick }) => (
  <div
    className="rounded-2xl sm:rounded-3xl overflow-hidden transition-all duration-300 transform hover:scale-105 hover:shadow-xl relative cursor-pointer h-[200px] sm:h-[240px] md:h-[283px] w-full max-w-[240px] sm:max-w-[280px] mx-auto"
    onClick={onClick}
    role="button"
    tabIndex={0}
    onKeyDown={(e) => {
      if (e.key === "Enter" || e.key === " ") {
        onClick?.();
      }
    }}
  >
    <img 
      src={imageUrl} 
      alt={title} 
      className="w-full h-full object-cover transition-transform duration-300 hover:scale-110" 
      onError={(e: any) => {
        e.target.onerror = null;
        e.target.src = "/assets/dummy_image_new.jpg";
      }}
    />
    <div className="absolute bottom-0 w-full bg-gradient-to-t from-black via-black/70 to-transparent p-4 sm:p-6 md:p-8 text-center">
      <h3 className="text-white text-sm sm:text-base md:text-lg font-semibold mb-1 sm:mb-2 drop-shadow-lg">
        {title}
      </h3>
      <p className="text-white text-xs sm:text-sm opacity-90 line-clamp-2 drop-shadow-md">
        {description}
      </p>
    </div>
  </div>
);

const CardCarousel: React.FC<CardCarouselProps> = ({ cards, loading, error }) => {
  const sliderRef = useRef<Slider | null>(null);
  const router = useRouter();
  const setQuery = useQueryStore((state) => state.setQuery);

  const handleDestinationClick = (destination: string) => {
    const encodedDestination = encodeURIComponent(destination);
    setQuery(encodedDestination);
    router.push(`/vacation/tours?query=${encodedDestination}`);
  };

  const settings = {
    infinite: true,
    speed: 500,
    slidesToShow: 5,
    slidesToScroll: 1,
    arrows: false,
    centerMode: true,
    centerPadding: "60px",
    focusOnSelect: true,
    autoplay: true,
    autoplaySpeed: 3000,
    pauseOnHover: true,
    pauseOnFocus: true,
    responsive: [
      {
        breakpoint: 1400,
        settings: {
          slidesToShow: 5,
          centerPadding: "40px",
          autoplay: true,
          autoplaySpeed: 3000,
        },
      },
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 4,
          centerPadding: "20px",
          autoplay: true,
          autoplaySpeed: 3000,
        },
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 4,
          centerPadding: "40px",
          autoplay: true,
          autoplaySpeed: 3000,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 3,
          centerPadding: "20px",
          autoplay: true,
          autoplaySpeed: 3000,
        },
      },
      {
        breakpoint: 640,
        settings: {
          slidesToShow: 2,
          centerPadding: "80px",
          autoplay: true,
          autoplaySpeed: 3000,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          centerPadding: "60px",
          autoplay: true,
          autoplaySpeed: 3000,
        },
      },
      {
        breakpoint: 360,
        settings: {
          slidesToShow: 1,
          centerPadding: "40px",
          autoplay: true,
          autoplaySpeed: 3000,
        },
      },
    ],
  };

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!sliderRef.current) return;
      if (event.key === "ArrowRight" || event.key === "d") {
        sliderRef.current.slickNext();
      } else if (event.key === "ArrowLeft" || event.key === "a") {
        sliderRef.current.slickPrev();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  if (loading) {
    return (
      <section className="relative py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-start pb-8 sm:pb-16">
            <div className="flex flex-col gap-2 sm:gap-[14px]">
              <p className="text-gray-500 text-sm sm:text-lg">TOP DESTINATION</p>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800">
                Explore top destinations
              </h2>
            </div>
          </div>
          <div className="flex justify-center items-center h-72">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="relative py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-start pb-8 sm:pb-16">
            <div>
              <p className="text-[#175CD3] text-sm sm:text-[18px] font-semibold mb-2">TOP DESTINATION</p>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800">
                Explore top destination
              </h2>
            </div>
          </div>
          <div className="flex justify-center items-center h-72">
            <p className="text-red-600 text-center px-4">{error}</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <div className="flex flex-col w-full overflow-hidden">
      <section className="w-full px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="container mx-auto  lg:px-8">
          {/* Header Section */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0">
            <div>
              <p className="text-[#175CD3] text-sm sm:text-lg font-semibold mb-2 sm:mb-4">
                TOP DESTINATION
              </p>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800">
                Explore top destination
              </h2>
            </div>
            <div className="flex space-x-3 sm:space-x-4 mt-4 sm:mt-0">
              <button
                onClick={() => sliderRef.current?.slickPrev()}
                className="border border-[#175CD3] p-2 sm:p-3 rounded-full hover:bg-[#175CD3] hover:text-white transition-colors"
              >
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  className="w-4 h-4 sm:w-5 sm:h-5 text-[#175CD3] hover:text-white" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  strokeWidth={2.5} 
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                </svg>
              </button>
              <button
                onClick={() => sliderRef.current?.slickNext()}
                className="border border-[#175CD3] p-2 sm:p-3 rounded-full hover:bg-[#175CD3] hover:text-white transition-colors"
              >
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  className="w-4 h-4 sm:w-5 sm:h-5 text-[#175CD3] hover:text-white" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  strokeWidth={2.5} 
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </section>
      
      <section className="relative py-4 w-full">
        <div className="w-full">
          <Slider ref={sliderRef} {...settings}>
            {cards.map((card, index) => (
              <div key={index} className="px-2 sm:px-3 focus:outline-none">
                <Card {...card} onClick={() => handleDestinationClick(card.title)} />
              </div>
            ))}
          </Slider>
        </div>

        <div className="flex justify-center mt-6 sm:mt-8 mb-6 px-4">
          <button
            onClick={() => router.push("/vacation")}
            className="bg-[#EFF8FF] text-[#175CD3] px-6 py-3  font-semibold rounded-full shadow-sm flex items-center hover:bg-blue-200 transition text-base font-medium"
          >
            <span className="mr-2 sm:mr-3">See All</span>
            <svg 
              width="20" 
              height="20" 
              className="sm:w-6 sm:h-6" 
              viewBox="0 0 25 24" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                d="M5.5 12H19.5M19.5 12L12.5 5M19.5 12L12.5 19" 
                stroke="#175CD3" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      </section>
    </div>
  );
};

export default CardCarousel;