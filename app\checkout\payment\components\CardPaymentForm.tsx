import { useState, ChangeEvent } from 'react';
import { CardDetails } from '../types/payment.types';

interface CardPaymentFormProps {
  onSubmit: (cardDetails: CardDetails) => void;
  isSubmitting: boolean;
}

export const CardPaymentForm = ({ onSubmit, isSubmitting }: CardPaymentFormProps) => {
  const [cardDetails, setCardDetails] = useState<CardDetails>({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardName: ''
  });
  const [errors, setErrors] = useState<Partial<CardDetails>>({});

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    let formattedValue = value;

    if (name === 'cardNumber') {
      // Remove any non-digit characters
      const digitsOnly = value.replace(/\D/g, '');
      // Add hyphens after every 4 digits
      formattedValue = digitsOnly.replace(/(\d{4})(?=\d)/g, '$1-');
      // Limit to 19 characters (16 digits + 3 hyphens)
      formattedValue = formattedValue.slice(0, 19);
    } else if (name === 'expiryDate') {
      // Remove any non-digit characters
      const digitsOnly = value.replace(/\D/g, '');
      // Add forward slash after 2 digits
      if (digitsOnly.length > 2) {
        formattedValue = `${digitsOnly.slice(0, 2)}/${digitsOnly.slice(2, 4)}`;
      } else {
        formattedValue = digitsOnly;
      }
      // Limit to 5 characters (MM/YY)
      formattedValue = formattedValue.slice(0, 5);
    } else if (name === 'cvv') {
      // Limit to 4 digits
      formattedValue = value.replace(/\D/g, '').slice(0, 4);
    }

    setCardDetails(prev => ({
      ...prev,
      [name]: formattedValue
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<CardDetails> = {};
    
    if (!cardDetails.cardNumber.trim()) {
      newErrors.cardNumber = 'Card number is required';
    } else if (cardDetails.cardNumber.replace(/\D/g, '').length !== 16) {
      newErrors.cardNumber = 'Enter a valid 16-digit card number';
    }
    
    if (!cardDetails.expiryDate.trim()) {
      newErrors.expiryDate = 'Expiry date is required';
    } else if (!/^(0[1-9]|1[0-2])\/(\d{2}|\d{4})$/.test(cardDetails.expiryDate)) {
      newErrors.expiryDate = 'Enter a valid expiry date (MM/YY or MM/YYYY)';
    }
    
    if (!cardDetails.cvv.trim()) {
      newErrors.cvv = 'CVV is required';
    } else if (cardDetails.cvv.length < 3 || cardDetails.cvv.length > 4) {
      newErrors.cvv = 'Enter a valid CVV (3-4 digits)';
    }
    
    if (!cardDetails.cardName.trim()) {
      newErrors.cardName = 'Cardholder name is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(cardDetails);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Card Number
        </label>
        <input
          type="text"
          name="cardNumber"
          value={cardDetails.cardNumber}
          onChange={handleInputChange}
          placeholder="1234 5678 9012 3456"
          className="w-full p-3 border rounded-lg text-sm"
          maxLength={19}
        />
        {errors.cardNumber && (
          <p className="mt-1 text-sm text-red-600">{errors.cardNumber}</p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Expiry Date
          </label>
          <input
            type="text"
            name="expiryDate"
            value={cardDetails.expiryDate}
            onChange={handleInputChange}
            placeholder="MM/YY"
            className="w-full p-3 border rounded-lg text-sm"
            maxLength={5}
          />
          {errors.expiryDate && (
            <p className="mt-1 text-sm text-red-600">{errors.expiryDate}</p>
          )}
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            CVV
          </label>
          <input
            type="password"
            name="cvv"
            value={cardDetails.cvv}
            onChange={handleInputChange}
            placeholder="123"
            className="w-full p-3 border rounded-lg text-sm"
            maxLength={4}
          />
          {errors.cvv && (
            <p className="mt-1 text-sm text-red-600">{errors.cvv}</p>
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Cardholder Name
        </label>
        <input
          type="text"
          name="cardName"
          value={cardDetails.cardName}
          onChange={handleInputChange}
          placeholder="John Doe"
          className="w-full p-3 border rounded-lg text-sm"
        />
        {errors.cardName && (
          <p className="mt-1 text-sm text-red-600">{errors.cardName}</p>
        )}
      </div>

      <button
        type="submit"
        disabled={isSubmitting}
        className={`w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium ${
          isSubmitting ? 'opacity-70 cursor-not-allowed' : 'hover:bg-blue-700'
        }`}
      >
        {isSubmitting ? 'Processing...' : 'Pay Now'}
      </button>
    </form>
  );
};
