import { useCurrencyStore } from '@/app/store/useCurrencyStore';
import Link from 'next/link';
import React, { useEffect, useRef, useState } from 'react';

const PopularTouristCard = ({ tour, handleBookNow }: any) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const pillsRef = useRef<HTMLDivElement[]>([]);
  const [visibleCount, setVisibleCount] = useState(tour.country.split(',').length);
  const convertFromINR = useCurrencyStore(state => state.convertFromINR);
  const symbols = useCurrencyStore(state => state.symbols);
  const selectedCurrency = useCurrencyStore(state => state.selectedCurrency);
  const countries = tour.country.split(',').map((c: string) => c.trim());

  useEffect(() => {
    const updateVisibleCount = () => {
      const containerWidth = containerRef.current?.offsetWidth || 0;
      const pillWidths = pillsRef.current.map(pill => pill?.offsetWidth || 0);
      
      let total = 0;
      let countThatFit = 0;
      const morePillWidth = 60; // Reserved space for "+X more" pill

      for (let i = 0; i < pillWidths.length; i++) {
        total += pillWidths[i] + 8; // 8px gap
        if (total + (i < pillWidths.length - 1 ? morePillWidth : 0) <= containerWidth) {
          countThatFit = i + 1;
        } else {
          break;
        }
      }

      setVisibleCount(countThatFit);
    };

    // Initial calculation
    updateVisibleCount();
    
    // Recalculate on window resize
    window.addEventListener('resize', updateVisibleCount);
    return () => window.removeEventListener('resize', updateVisibleCount);
  }, [tour.country]);

  // Calculate pricing
  const max_discount_rule = tour?.applicableDiscountRules 
    ? [...tour?.applicableDiscountRules || []].sort((a: any, b: any) => b?.discountPercentage - a?.discountPercentage)[0] 
    : null;
  
  let price_to_show = max_discount_rule 
    ? tour.netSellingPrice - (tour.netSellingPrice * (max_discount_rule?.discountPercentage / 100))
    : tour.netSellingPrice;
  
  price_to_show = convertFromINR(price_to_show);
  const originalPrice = convertFromINR(tour?.grossSellingPrice);
  const hasDiscount = originalPrice > price_to_show;
  const hiddenCount = countries.length - visibleCount;

  return (
    <div className="bg-white  overflow-hidden  transition-shadow duration-300 group w-full flex flex-col">
      {/* Image Container with Proper Aspect Ratio */}
      <div className="relative w-full aspect-[2/1] overflow-hidden">
        {tour.packageMainImage ? (
          <img
            src={tour.packageMainImage}
            alt={tour.packageTitle}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300 rounded-xl"
            style={{ objectFit: 'cover' }}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = '/placeholder-tour-image.jpg';
            }}
          />
        ) : (
          <div className="w-full h-full bg-gray-200 flex items-center justify-center rounded-t-xl">
            <span className="text-gray-500 text-sm">No image available</span>
          </div>
        )}
      </div>

      {/* Content Section with Flexible Height */}
      <div className="p-1 flex flex-col flex-1">
        {/* Tour Title */}
        <div className="mb-3">
          <h3 className="text-lg font-bold leading-tight text-gray-900 line-clamp-2 min-h-[2.5rem]">
            {tour.packageTitle}
          </h3>
        </div>

        {/* Starting from text */}
        <div className="mb-2">
          <p className="text-sm font-medium text-gray-500">Starting from</p>
        </div>

        {/* Price Section */}
        <div className="mb-4">
          {/* Original Price (if discounted) */}
          {hasDiscount && (
            <p className="text-sm text-gray-400 line-through mb-1 text-sm">
              {symbols[selectedCurrency]} {Math.round(originalPrice).toLocaleString()}
            </p>
          )}
          
          {/* Current Price and Duration */}
          <div className="flex justify-between items-end">
            <div className="flex items-baseline gap-2">
              <p className="text-sm font-bold text-gray-900">
                {symbols[selectedCurrency]} {Math.round(price_to_show || tour.priceSummary || 10000).toLocaleString()}
              </p>
              <p className="text-sm text-gray-600">per person</p>
            </div>
            
            {/* Duration */}
            <div className="text-right">
              <p className="text-sm text-gray-700">
                <span className="font-semibold">{tour.noOfDays}</span> Days{' '}
                <span className="font-semibold">{tour.noOfNights}</span> Nights
              </p>
            </div>
          </div>
        </div>

        {/* Countries Pills Container */}
        <div className="mb-6 flex-1">
          <div
            className="flex flex-wrap gap-2"
            ref={containerRef}
          >
            {countries.slice(0, visibleCount).map((country: any, index: any) => (
              <span
                key={index}
                className="inline-flex items-center bg-gray-100 text-gray-700 px-3 py-1.5 rounded-full text-sm font-medium"
                ref={(el: any) => (pillsRef.current[index] = el!)}
              >
                <img
                  src={`/assets/flags/${country.split(' ').join('-').toLowerCase()}.svg`}
                  className="w-4 h-3 mr-2"
                  alt={country}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
                {country}
              </span>
            ))}

            {hiddenCount > 0 && (
              <span className="inline-flex items-center bg-gray-100 text-gray-700 px-3 py-1.5 rounded-full text-sm font-medium">
                +{hiddenCount} more
              </span>
            )}
          </div>
        </div>

        {/* Book Now Button - Always at Bottom */}
        <div className="mt-auto">
          <Link
            href={`/tours/package-details?query=${tour.packageCode}`}
            className="block w-full"
          >
            <button className="bg-blue-50 text-blue-600 w-full font-semibold text-sm py-3 px-4 rounded-full hover:bg-blue-100 transition-colors duration-200 border border-blue-100">
              Book Now
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PopularTouristCard;