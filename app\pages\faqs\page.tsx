"use client";

import React, { useState } from 'react';
import NavBar from '@/app/components/NavBar';
import Footer from '@/app/components/Footer';
import Breadcrumb from '@/app/components/BreadCrumbs';

const FAQItem = ({ question, answer }: { question: string; answer: string }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-gray-200">
      <button
        className="w-full py-4 px-6 text-left focus:outline-none"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">{question}</h3>
          <svg
            className={`w-5 h-5 text-gray-500 transform transition-transform duration-200 ${
              isOpen ? 'rotate-180' : ''
            }`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </div>
      </button>
      {isOpen && (
        <div className="px-6 pb-4">
          <p className="text-gray-600">{answer}</p>
        </div>
      )}
    </div>
  );
};

const FAQsPage = () => {
  const faqs = [
    {
      question: "How do I book a tour?",
      answer: "You can book a tour by selecting your desired destination, choosing a package, and following the booking process. You'll need to provide traveler details and make the payment to confirm your booking."
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept various payment methods including credit cards, debit cards, net banking, and UPI. All payments are processed securely through our payment gateway."
    },
    {
      question: "Can I modify my booking after confirmation?",
      answer: "Yes, you can modify your booking depending on the tour operator's policies. Please contact our customer support team for assistance with modifications."
    },
    {
      question: "What is your cancellation policy?",
      answer: "Our cancellation policy varies depending on the tour package and timing of cancellation. Generally, cancellations made 30 days before departure receive a 90% refund, while cancellations made less than 7 days before departure may not be eligible for a refund."
    },
    {
      question: "Do I need travel insurance?",
      answer: "While travel insurance is not mandatory, we strongly recommend it for all travelers. It provides coverage for medical emergencies, trip cancellations, lost baggage, and other unforeseen circumstances."
    },
    {
      question: "What documents do I need to travel?",
      answer: "Required documents vary by destination but typically include a valid passport, visa (if required), travel insurance, and any necessary health certificates. Please check the specific requirements for your destination."
    },
    {
      question: "How can I contact customer support?",
      answer: "You can reach our customer support team by phone at +91 **********, <NAME_EMAIL>, or through our contact form on the website. We're available 24/7 to assist you."
    },
    {
      question: "Do you offer group discounts?",
      answer: "Yes, we offer special discounts for group bookings. The discount percentage varies based on the group size and tour package. Please contact our sales team for group booking inquiries."
    }
  ];

  return (
    <main className="min-h-screen">
      <NavBar />
      <Breadcrumb />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">Frequently Asked Questions</h1>
        
        <div className="bg-white rounded-lg shadow">
          {faqs.map((faq, index) => (
            <FAQItem key={index} question={faq.question} answer={faq.answer} />
          ))}
        </div>

        {/* Additional Help Section */}
        <div className="mt-12 bg-blue-50 p-6 rounded-lg">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">Still have questions?</h2>
          <p className="text-gray-600 mb-4">
            If you can not find the answer you are looking for, our customer support team is here to help.
          </p>
          <button className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition">
            Contact Support
          </button>
        </div>
      </div>

      <Footer />
    </main>
  );
};

export default FAQsPage; 