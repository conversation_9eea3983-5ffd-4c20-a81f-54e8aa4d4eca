'use client';

import React, { useState, useEffect } from 'react';
import axios from 'axios';
import type { PackageDetails } from '@/app/types/PackageDetails';

interface PaymentPolicySectionProps {
  packageData: PackageDetails;
  expandedSections: string[];
  toggleSection: (section: string) => void;
  from_download_pdf?: boolean;
  setExpandedSections?: any;
}

interface PolicyData {
  imageText: string;
  content: string;
}

const PaymentPolicySection: React.FC<PaymentPolicySectionProps> = ({
  packageData,
  expandedSections,
  toggleSection,
  from_download_pdf = false,
  setExpandedSections
}) => {
  const [policies, setPolicies] = useState({
    paymentPolicy: '',
    refundPolicy: '',
    termsAndConditions: ''
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [faqOpen, setFaqOpen] = useState({
    payment: false,
    refund: false,
    terms: false
  });

  const toggleFaq = (key: keyof typeof faqOpen) => {
    setFaqOpen(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  useEffect(() => {
    const fetchPolicies = async () => {
      try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/web-images`);
        if (response.data) {
          const policyData = response.data;

          const paymentPolicy = policyData.find((policy: PolicyData) =>
            policy.imageText === 'Payment Policy'
          )?.content || 'Payment policy information not available.';

          const refundPolicy = policyData.find((policy: PolicyData) =>
            policy.imageText === 'Cancellation & Rescheduling Terms'
          )?.content || 'Refund policy information not available.';

          const termsAndConditions = policyData.find((policy: PolicyData) =>
            policy.imageText === 'Terms and Conditions'
          )?.content || 'Terms and conditions not available.';

          setPolicies({ paymentPolicy, refundPolicy, termsAndConditions });
        }
      } catch (err) {
        console.error('Error fetching policies:', err);
        setError('Failed to load policies. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchPolicies();

    if (from_download_pdf) {
      setExpandedSections?.((prev: any) => [...prev, 'payment-policy']);
    }
  }, []);

  const renderToggleIcon = (expanded: boolean) => (
    expanded ? (
      <div className='w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 border-blue-700 flex items-center justify-center bg-white ml-2'>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-3.5 h-3.5 text-blue-700">
        <path strokeLinecap="round" strokeLinejoin="round" d="M18 12H6" />
      </svg>
      </div>
    ) : (
      <div className='w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 border-blue-700 flex items-center justify-center bg-white ml-2'>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-3.5 h-3.5 text-blue-700">
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m6-6H6" />
      </svg>
      </div>
    )
  );

  return (
    <section id="payment-policy" className="mt-6 sm:mt-8 border-t pt-4">
      <button onClick={() => toggleSection('payment-policy')} className="w-full flex items-center justify-between">
        <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-5">Payment and Cancellation Policy</h2>
        <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full border-2 border-blue-700 flex items-center justify-center bg-white">
          {expandedSections.includes('payment-policy') ? (
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 text-blue-700">
              <path strokeLinecap="round" strokeLinejoin="round" d="M18 12H6" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 text-blue-700">
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m6-6H6" />
            </svg>
          )}
        </div>
      </button>

      <div className={`mt-2 transition-all duration-300 ${expandedSections.includes('payment-policy') ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
        {loading ? (
          <div className="flex items-center justify-center py-4">
            <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-2 text-gray-600">Loading policy information...</span>
          </div>
        ) : error ? (
          <div className="text-red-500 p-4 bg-red-50 rounded-md">{error}</div>
        ) : (
          <div className="space-y-6">
            {/* FAQ Item 1 */}
            <div className="border-b pb-3">
              <button onClick={() => toggleFaq('payment')} className="w-full flex justify-between items-center mb-2">
                <h3 className="text-md font-semibold text-gray-800">Payment Policy</h3>
                {renderToggleIcon(faqOpen.payment)}
              </button>
              {faqOpen.payment && (
                <div className="text-sm sm:text-base text-gray-600 space-y-2">
                  <div className="itinerary-content" dangerouslySetInnerHTML={{ __html: policies.paymentPolicy }} />
                </div>
              )}
            </div>

            {/* FAQ Item 2 */}
            <div className="border-b pb-3">
              <button onClick={() => toggleFaq('refund')} className="w-full flex justify-between items-center mb-2">
                <h3 className="text-md font-semibold text-gray-800">Refund & Cancellation Policy</h3>
                {renderToggleIcon(faqOpen.refund)}
              </button>
              {faqOpen.refund && (
                <div className="text-sm sm:text-base text-gray-600 space-y-2">
                  <div className="itinerary-content" dangerouslySetInnerHTML={{ __html: policies.refundPolicy }} />
                </div>
              )}
            </div>

            {/* FAQ Item 3 */}
            <div>
              <button onClick={() => toggleFaq('terms')} className="w-full flex justify-between items-center mb-2">
                <h3 className="text-md font-semibold text-gray-800">Other Important Information</h3>
                {renderToggleIcon(faqOpen.terms)}
              </button>
              {faqOpen.terms && (
                <div className="text-sm sm:text-base text-gray-600 space-y-2">
                  <div className="itinerary-content" dangerouslySetInnerHTML={{ __html: policies.termsAndConditions }} />
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default PaymentPolicySection;
