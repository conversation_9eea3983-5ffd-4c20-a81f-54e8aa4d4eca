import { useRef, useState } from "react";
import DatePicker from "react-datepicker";
import "./datepicker.css";
import "react-datepicker/dist/react-datepicker.css";
export default function CustomDateInput({className="",onChange,value,height=null,width=null,error_message,from_discount=false,...rest}:any) {
  const inputRef = useRef<HTMLInputElement>(null);

  return (
     <div className={`relative  w-[${width ? width : "243px"}] h-[${height?height:"44px"}]`}>
      <div className="absolute left-3 sm:left-3.5 top-1/2 -translate-y-1/2 pointer-events-none z-10">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3.5 sm:w-4 h-3.5 sm:h-4 text-gray-400">
          <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
        </svg>
      </div>
      {from_discount?
      <DatePicker
        selected={value}
        ref={inputRef}
        onChange={onChange}
        placeholderText="dd/MM/yyyy"
        {...rest}
        dateFormat="dd/MM/yyyy"
        showMonthDropdown
        showYearDropdown
        dropdownMode="select"
        popperPlacement="bottom-start"
        showPopperArrow={false}
        popperClassName="react-datepicker-popper z-[9999]"
        popperModifiers={[
          {
            name: 'preventOverflow',
            options: {
              boundary: 'viewport',
              padding: 8,
            },
          },
        ]}
        className={
          className !== ""
            ? className
            : "w-full h-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm shadow-sm"
        }
      /> :
      <DatePicker
        // value={value}
        selected={value}
        ref={inputRef}
        onChange={onChange}
        placeholderText="dd/MM/yyyy"
        {...rest}
        // minDate={new Date()}
        dateFormat="dd/MM/yyyy"
        popperClassName="react-datepicker-popper"
        popperPlacement="bottom-start"
        showPopperArrow={false}
        showMonthDropdown
        showYearDropdown
        dropdownMode="select"
         className={className!=="" ? className :"w-full h-full pl-10 pr-4 py-2 border border-[#B3A4F4] rounded-lg text-[#101828] bg-white focus:outline-none font-normal text-base"}
      />}
    </div>
    // <div className="flex flex-col gap-1">
    //   <div className={`relative w-[${width ? width : "243px"}] h-[${height?height:"44px"}]`}>
    //     <div
    //       className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 cursor-pointer"
    //       // onClick={() => inputRef.current?.showPicker()}
    //     >
    //       <svg
    //         xmlns="http://www.w3.org/2000/svg"
    //         className="h-5 w-5"
    //         fill="none"
    //         viewBox="0 0 24 24"
    //         stroke="currentColor"
    //       >
    //         <path
    //           strokeLinecap="round"
    //           strokeLinejoin="round"
    //           strokeWidth={2}
    //           d="M8 7V3M16 7V3M3 11H21M5 21H19C20.1046 21 21 20.1046 21 19V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V19C3 20.1046 3.89543 21 5 21Z"
    //         />
    //       </svg>
    //     </div>
    //     {/* <input
    //       ref={inputRef}
    //       type="date"
    //       value={value}
    //       onChange={onChange}
    //       {...rest}
          // className={className!=="" ? className :"w-full h-full pl-10 pr-4 py-2 border border-[#B3A4F4] rounded-lg text-[#101828] bg-white focus:outline-none font-normal text-base"}
    //     /> */}
    //     <div className="relative">
    //     <div className="absolute left-3 sm:left-3.5 top-1/2 -translate-y-1/2 pointer-events-none z-10">
    //       <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3.5 sm:w-4 h-3.5 sm:h-4 text-gray-400">
    //         <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
    //       </svg>
    //     </div>
    //     <DatePicker
    //       // ref={inputRef}
    //       // selected={value}
          // value={value}
          // onChange={onChange}
          // {...rest}
    //       placeholderText="When are you travelling?"
    //       dateFormat="dd/MM/yyyy"
    //       popperClassName="react-datepicker-popper"
    //       popperPlacement="bottom-start"
    //       showPopperArrow={false}
    //       className={className!=="" ? className :"w-full h-full pl-10 pr-4 py-2 border border-[#B3A4F4] rounded-lg text-[#101828] bg-white focus:outline-none font-normal text-base"}
    //     />
    //   </div>
    //   </div>
    //  {error_message && error_message!=="" && <span className="text-red-600 text-sm">{error_message}</span>}

    //   {/* Inline CSS to hide native calendar icon */}
    //   <style>
    //       {`
    //         input::-webkit-calendar-picker-indicator {
    //           display: none;
    //           -webkit-appearance: none;
    //         }
    //       `}
    //     </style>
    //   {/* <style>
    //     {`
    //       input[type="date"]::-webkit-calendar-picker-indicator {
    //         display: none;
    //         -webkit-appearance: none;
    //       }
    //     `}
    //   </style> */}
    // </div>
  );
}
