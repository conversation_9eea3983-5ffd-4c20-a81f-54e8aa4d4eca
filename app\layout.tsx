import './globals.css'
// import 'leaflet/dist/leaflet.css';
// import 'leaflet-defaulticon-compatibility';
// import 'leaflet-defaulticon-compatibility/dist/leaflet-defaulticon-compatibility.css';

import Head from 'next/head';
import type { Metadata } from 'next'
import { Inter, Manrope } from 'next/font/google'
import '@fortawesome/fontawesome-free/css/all.min.css'
import ContextProvider from './ContextProvider/ContextProvider'
import { Toaster } from 'react-hot-toast'
import TidioChat from './components/TidioChat.tsx/TidioChat'
import FloatingIcon from './utility/HideFloatingLabel'

const inter = Inter({ subsets: ['latin'] })
const manrope = Manrope({
  subsets: ['latin'],
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'WiseYatra - Making friends one traveler at a time',
  description: 'Your perfect travel companion',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={manrope.className}>
      <body>
        <ContextProvider>
          {children}
        </ContextProvider>
        <FloatingIcon/>

        {/* <div className="fixed bottom-20 left-4 flex flex-col gap-4 z-[9999]">
          <a
            href="https://wa.me/+919717559499"
            target="_blank"
            rel="noopener noreferrer"
            className="w-12 h-12 rounded-full bg-green-500 text-white flex items-center justify-center shadow-lg hover:scale-110 transition-transform"
            aria-label="Chat on WhatsApp"
          >
            <i className="fab fa-whatsapp text-xl"></i>
          </a>
          <a
            href="tel:+919717559499"
            className="w-12 h-12 rounded-full bg-blue-500 text-white flex items-center justify-center shadow-lg hover:scale-110 transition-transform"
            aria-label="Call Us"
          >
            <i className="fas fa-phone-alt text-lg"></i>
          </a>
        </div> */}
         <TidioChat/>
        <Toaster position="top-right" />
      </body>
    </html>
  )
}