@tailwind base;
@tailwind components;
@tailwind utilities;


@layer base {
  html {
    @apply text-gray-900;
    font-family: var(--font-manrope), system-ui, sans-serif;
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
  }
  
  body {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
  }
  
  input, select, textarea {
    @apply text-gray-900;
  }
  
  input::placeholder, select::placeholder, textarea::placeholder {
    @apply text-gray-500;
  }
}

:root {
  --foreground-rgb: 0, 0, 0;
}

body {
  color: rgb(var(--foreground-rgb));
  background-color: white;
}

.itinerary-content ul {
  list-style-type: disc;
  padding-left: 1.25rem; /* same as pl-5 */
  margin-bottom: 0.5rem;
}
.itinerary-content ol {
  list-style-type: decimal;
  padding-left: 1.25rem;
  margin-bottom: 0.5rem;
}
.itinerary-content li {
  margin-bottom: 0.25rem;
}