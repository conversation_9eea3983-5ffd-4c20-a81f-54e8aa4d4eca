import Image from 'next/image'
import Link from 'next/link'
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { GetServerSideProps } from 'next';
import NavigationBar from '.././components/NavBar';
import Breadcrumb from '../components/BreadCrumbs';
import SearchSection from '../components/SearchVacation';
import TourCollectionSection from '../components/CuratedTourDetails';
import BlogSection from '../components/BlogSection';
import TestimonialSection from '../components/Testimonials';
import CallToAction from '../components/ContactSection';
import Footer from '.././components/Footer';



const Home = async () => {

  return (
    <main>
      <NavigationBar></NavigationBar>
      <Breadcrumb></Breadcrumb>
      <SearchSection></SearchSection>
      <TourCollectionSection></TourCollectionSection>
      <BlogSection></BlogSection>
      <TestimonialSection></TestimonialSection>
      <CallToAction></CallToAction>
      <Footer></Footer>
    </main>
  );
};


export default Home;