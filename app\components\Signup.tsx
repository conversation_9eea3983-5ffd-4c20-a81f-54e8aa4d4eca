"use client";

import React, { useEffect, useRef, useState } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import axios from "axios";
import { phone_code } from "../utility/country_code";

// Type for carousel images
interface CarouselImage {
  image: string;
  text: string;
}

// Slick settings for carousel
const sliderSettings = {
  dots: false,
  infinite: true,
  auto: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
  arrows: true,
};

const carouselImages: CarouselImage[] = [
  {
    image: "/assets/tours/imageone.png",
    text: "Welcome to Your Next Adventure",
  },
  {
    image: "/assets/tours/imageone.png",
    text: "Join Us and Start Your Journey Today",
  },
  {
    image: "/assets/tours/imageone.png",
    text: "We Make Travel Planning Easy",
  },
];

const SignUp: React.FC = () => {
  const NEXT_PUBLIC_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
  const [selectedCountryCode, setSelectedCountryCode] = useState<any>('91'); // Default to India
  const [selectedCountryName, setSelectedCountryName] = useState<string>('India');
  const [passwordVisible, setPasswordVisible] = useState<boolean>(false);
  const [email, setEmail] = useState<string>("");
  const [name, setName] = useState<string>("");
  const [mobileNumber, setMobileNumber] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [verificationCode, setVerificationCode] = useState<string>("");
  const [showVerificationModal, setShowVerificationModal] = useState<boolean>(false);
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [error, setError] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [verificationLoading, setVerificationLoading] = useState<boolean>(false);

  const togglePasswordVisibility = () => setPasswordVisible(!passwordVisible);

  const handleInputChange = (setter: React.Dispatch<React.SetStateAction<string>>) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => setter(event.target.value);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setError("");

    if (password.length < 8) {
      setError("Password must be at least 8 characters long.");
      return;
    }

    setLoading(true);
    try {
      const response = await axios.post(`${NEXT_PUBLIC_API_BASE_URL}/api/auth/signup`, { 
        name, 
        email, 
        phone: `${selectedCountryCode}${mobileNumber}`,
        // selectedCountryCode 
        password 
      });
    
      setSuccessMessage(response.data || "Signup successful! Please check your email for verification code.");
      setShowVerificationModal(true);
    } catch (err: any) {
      console.log(err.response?.data);
      setError(err.response?.data || "Something went wrong. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleVerificationSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setVerificationLoading(true);
    setError("");
    try {
      const response = await axios.get(`${NEXT_PUBLIC_API_BASE_URL}/api/auth/verify-email?code=${verificationCode}`);
      console.log(response);
      if (response.data) {
        setSuccessMessage(response?.data || "Email verified successfully! Redirecting to login page in 3 seconds");
        // Show success message for 3 seconds before redirecting
        setTimeout(() => {
          window.location.href = "/login";
        }, 3000);
      } else {
        setError(response.data.message || "Verification failed. Please try again.");
      }
    } catch (err: any) {
      setError(err.response?.data?.message || "Invalid verification code. Please try again.");
    } finally {
      setVerificationLoading(false);
    }
  };

 const [showDropdown, setShowDropdown] = useState(false);
  // const [selectedCountryCode, setSelectedCountryCode] = useState('91');
  // const [selectedCountryName, setSelectedCountryName] = useState('India');
  // const [mobileNumber, setMobileNumber] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  const filteredCountries = phone_code.sort((a,b)=>a?.country_en.localeCompare(b?.country_en)).filter((country:any) =>{
    return(
    country?.country_en?.toLowerCase()?.startsWith(searchTerm.toLowerCase()) ||
  country?.phone_code?.toString()?.startsWith(searchTerm)
    )
  }
  );

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(e.target as Node)) {
        setShowDropdown(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <div className="grid grid-cols-1 lg:grid-cols-2 lg:gap-16 px-6 py-16 max-w-7xl mx-auto">
        {/* Left Column - Carousel */}
        <div className="relative h-full overflow-hidden rounded-lg">
          <Slider {...sliderSettings} className="h-full">
            {carouselImages.map((slide, index) => (
              <div key={index} className="relative h-full">
                <img
                  src={slide.image}
                  alt={`Slide ${index + 1}`}
                  className="w-full h-[500px] object-cover rounded-2xl"
                />
                <div className="absolute bottom-20 sm:bottom-12 left-4 text-white text-xl font-bold z-10">
                  {slide.text}
                </div>
              </div>
            ))}
          </Slider>
        </div>

        {/* Right Column - Sign-Up Form */}
        <div className="flex items-center justify-center">
          <div className="max-w-md w-full">
            <h3 className="text-2xl font-semibold text-gray-800 mb-2">Create Your Account</h3>
            <p className="text-sm text-gray-500 mb-6">Signing up for WiseYatra is fast and 100% free</p>

            <form onSubmit={handleSubmit}>
              {error && <p className="text-red-500 text-sm mb-2">{error}</p>}

              <div className="mb-4">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Name*</label>
                <input 
                  id="name" 
                  type="text" 
                  value={name} 
                  onChange={handleInputChange(setName)} 
                  className="w-full px-4 py-2.5 border border-gray-300 rounded-md bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                  required 
                />
              </div>
              
              {/* <div className="mb-4" ref={dropdownRef}>
                <label htmlFor="mobile" className="block text-sm font-medium text-gray-700 mb-1">
                  Mobile Number*
                </label>

                <div className="flex gap-2">
                  <div className="relative">
                    <button
                      type="button"
                      onClick={() => setShowDropdown(!showDropdown)}
                      className="flex items-center gap-2 border border-gray-300 rounded-md px-2 py-1 bg-white text-gray-700 select-none h-full"
                    >
                      <img
                        src={`/assets/flags/${selectedCountryName.toLowerCase().replace(/ /g, '-')}.svg`}
                        className="w-6 h-4 object-cover"
                        alt="flag"
                      />
                      +{selectedCountryCode} ▾
                    </button>
                    {showDropdown && (
                      <div className="absolute z-10 mt-1 max-h-60 overflow-y-auto border bg-white shadow rounded-md">
                        {phone_code.map((country) => (
                          <div
                            key={country.phone_code}
                            onClick={() => {
                              setSelectedCountryCode(country.phone_code);
                              setSelectedCountryName(country.country_en);
                              setShowDropdown(false);
                            }}
                            className="flex items-center gap-2 px-3 py-2 hover:bg-gray-100 cursor-pointer"
                          >
                            <img
                              src={`/assets/flags/${country.country_en.toLowerCase().replace(/ /g, '-')}.svg`}
                              className="w-5 h-3 object-cover"
                              alt="flag"
                            />
                            <span className="flex-1 text-sm">{country.country_en}</span>
                            <span className="text-sm text-gray-500">+{country.phone_code}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  <input
                    id="mobile"
                    type="tel"
                    value={mobileNumber}
                    onChange={(e) => {
                      const numericValue = e.target.value.replace(/[^0-9]/g, '');
                      setMobileNumber(numericValue);
                    }}
                    className="flex-1 px-4 py-2.5 border border-gray-300 rounded-md bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                    placeholder="Enter mobile number"
                  />
                </div>
              </div> */}
              <div className="mb-4" ref={dropdownRef}>
                  <label htmlFor="mobile" className="block text-sm font-medium text-gray-700 mb-1">
                    Mobile Number*
                  </label>

                  <div className="flex gap-2">
                    <div className="relative">
                      <button
                        type="button"
                        onClick={() => setShowDropdown(!showDropdown)}
                        className="flex items-center gap-2 border border-gray-300 rounded-md px-2 py-1 bg-white text-gray-700 select-none h-full"
                      >
                        <img
                          src={`/assets/flags/${selectedCountryName.toLowerCase().replace(/ /g, '-')}.svg`}
                          className="w-6 h-4 object-cover"
                          alt="flag"
                        />
                        +{selectedCountryCode} ▾
                      </button>

                      {showDropdown && (
                        <div className="absolute z-10 mt-1 w-72 max-h-64 overflow-y-auto border bg-white shadow-lg rounded-md">
                          {/* Search box */}
                          <div className="sticky top-0 bg-white z-10 p-2 border-b">
                            <input
                              type="text"
                              placeholder="Search country/Phone Code..."
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="w-full px-3 py-1.5 border rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                            />
                          </div>

                          {/* Country list */}
                          {filteredCountries.length > 0 ? (
                            filteredCountries.map((country) =>{
                              console.log("filteredCountries",filteredCountries);
                              return(
                                
                                <div 
                                key={country.country_code}
                                // key={country.phone_code}
                                onClick={() => {
                                  setSelectedCountryCode(country.phone_code);
                                  setSelectedCountryName(country.country_en);
                                  setShowDropdown(false);
                                  setSearchTerm('');
                                }}
                                className="flex items-center gap-2 px-3 py-2 hover:bg-gray-100 cursor-pointer"
                                >
                                <img
                                  src={`/assets/flags/${country.country_en.toLowerCase().replace(/ /g, '-')}.svg`}
                                  className="w-5 h-3 object-cover"
                                  alt={country.country_en}
                                />
                                <span className="flex-1 text-sm">{country.country_en}</span>
                                <span className="text-sm text-gray-500">+{country.phone_code}</span>

                                </div>
                                // <div
                                // key={country.phone_code}
                                // onClick={() => {
                                //   setSelectedCountryCode(country.phone_code);
                                //   setSelectedCountryName(country.country_en);
                                //   setShowDropdown(false);
                                //   setSearchTerm('');
                                // }}
                                // className="flex items-center gap-2 px-3 py-2 hover:bg-gray-100 cursor-pointer"
                              // >
                                // <img
                                //   src={`/assets/flags/${country.country_en.toLowerCase().replace(/ /g, '-')}.svg`}
                                //   className="w-5 h-3 object-cover"
                                //   alt={country.country_en}
                                // />
                                // <span className="flex-1 text-sm">{country.country_en}</span>
                                // <span className="text-sm text-gray-500">+{country.phone_code}</span>
                              // </div>
                              )
                            })
                          ) : (
                            <div className="p-3 text-sm text-gray-500 text-center">No countries found.</div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Mobile input */}
                    <input
                      id="mobile"
                      type="tel"
                      value={mobileNumber}
                      onChange={(e) => {
                        const numericValue = e.target.value.replace(/[^0-9]/g, '');
                        setMobileNumber(numericValue);
                      }}
                      className="flex-1 px-4 py-2.5 border border-gray-300 rounded-md bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                      placeholder="Enter mobile number"
                    />
                  </div>
                </div>

              <div className="mb-4">
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Email*</label>
                <input 
                  id="email" 
                  type="email" 
                  value={email} 
                  onChange={handleInputChange(setEmail)} 
                  className="w-full px-4 py-2.5 border border-gray-300 rounded-md bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                  required 
                />
              </div>

              <div className="mb-4">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">Password*</label>
                <div className="relative">
                  <input 
                    id="password" 
                    type={passwordVisible ? "text" : "password"} 
                    value={password} 
                    onChange={handleInputChange(setPassword)} 
                    className="w-full px-4 py-2.5 border border-gray-300 rounded-md bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                    required 
                  />
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  >
                    {passwordVisible ? "Hide" : "Show"}
                  </button>
                </div>
                <p className="text-sm text-gray-500 mt-1">Must be at least 8 characters</p>
              </div>

              <button 
                type="submit" 
                className="w-full py-3 text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors" 
                disabled={loading}
              >
                {loading ? "Creating Account..." : "Create Account"}
              </button>
            </form>

            {/* Terms and Conditions Text */}
            <div className="mt-4 text-center">
              <p className="text-sm text-gray-600">
                By signing up, you agree to our{" "}
                <span className="font-bold">Terms & Conditions</span> &amp;{" "}
                <span className="font-bold">Privacy Policy</span>.
              </p>
            </div>

            {/* Login Link */}
            <div className="mt-4 text-center">
              <p className="text-sm text-gray-600">
                Already have an account?{" "}
                <a href="#" className="text-blue-600 font-bold hover:underline">
                  Login to your account
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Verification Modal */}
      {showVerificationModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">Email Verification</h3>
            <p className="text-gray-600 mb-4">{successMessage}</p>
            {!successMessage.includes("successfully") && (
              <form onSubmit={handleVerificationSubmit}>
                <div className="mb-4">
                  <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700 mb-1">
                    Verification Code
                  </label>
                  <input
                    id="verificationCode"
                    type="text"
                    value={verificationCode}
                    onChange={handleInputChange(setVerificationCode)}
                    className="w-full px-4 py-2.5 border border-gray-300 rounded-md bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
                {error && <p className="text-red-500 text-sm mb-2">{error}</p>}
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowVerificationModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
                    disabled={verificationLoading}
                  >
                    {verificationLoading ? "Verifying..." : "Verify"}
                  </button>
                </div>
              </form>
            )}
            {successMessage.includes("successfully") && (
              <div className="flex justify-end">
                <button
                  onClick={() => window.location.href = "/login"}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Okay
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SignUp;
