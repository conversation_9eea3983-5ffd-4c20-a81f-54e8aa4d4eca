"use client";

import NavigationBar from '@/app/components/NavBar';
import Breadcrumb from '@/app/components/BreadCrumbs';
import PackageDetails from '@/app/components/PackageDetails';
import Footer from '@/app/components/Footer';
import ParentComponent from '@/app/components/PopularToursParents';
import { LoadScript } from '@react-google-maps/api';
import { Suspense, useRef } from 'react';

const PageContent = () => {
		const componentRef = useRef<any>(null); 
	const breadCrumb = [
		{
			label:"Tours",
			link:"/vacation/tours"
		},
		{
			label:"Package-Details",
			link:"/last"
		},
	];
	return (
	// <LoadScript googleMapsApiKey="AIzaSyDVYB1GofLKV56yqxseuasNRX0nNLKWOQg">
		<main className="min-h-screen" ref={componentRef}>
			<NavigationBar />
			<Breadcrumb breadCrumb={breadCrumb}/>
			<PackageDetails componentRefData={componentRef} from_download_pdf={true} />
			{/* <ParentComponent /> */}
			<Footer />
		</main>
	// {/* </LoadScript> */}
	);
}; 	

const Page = () => {
  return (
    <Suspense fallback={<div></div>}>
      <PageContent />
    </Suspense>
  );
};

export default Page;
