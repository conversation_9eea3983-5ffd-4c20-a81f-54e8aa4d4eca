"use client";

import { useState, useEffect, useRef, useContext } from 'react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import Image from 'next/image';
import axios from 'axios';
import { useRouter } from 'next/navigation';

import type { PackageDetails as PackageDetailsType } from '@/app/types/PackageDetails';
import BookingCard from './package-details/BookingCard';
import PaymentModal from './package-details/PaymentModal';
import NavigationSidebar from './package-details/NavigationSidebar';
import ImageCarousel from './package-details/ImageCarousel';
import TripHighlights from './package-details/TripHighlights';
import DestinationMap from './package-details/DestinationMap';
import InclusionsExclusions from './package-details/InclusionsExclusions';
import ItinerarySection from './package-details/ItinerarySection';
import VoucherSection from './package-details/VoucherSection';
import PaymentPolicySection from './package-details/PaymentPolicySection';
import PackageInfo from './package-details/PackageInfo';
import './package-details/sidebar.css';

import crypto from 'crypto';
import { AppContext } from '../context/useAppContext';
import DiscountModal from './DiscountModal/DiscountModal';
import MapPage from './MapComponent/MapPage';
import { useCuponStore } from '../store/bookingCuponStore';
import { useSearchParams } from 'next/navigation';

const NEXT_PUBLIC_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

const PackageDetails = ({from_download_pdf=false,componentRefData=null,setLoadingForDownload=null}:any) => {
  const searchParams = useSearchParams();
  const query = searchParams.get('query');
  const custom = searchParams.get('custom');
  const {setCuponApplied,setCuponData,cuponApplied,cuponData} = useCuponStore();
  const [applicableDiscountRules,setApplicableDiscountRules] = useState([]);
  const [applicableDiscountRulesVisible,setApplicableDiscountRulesVisible] = useState(false);
  const router = useRouter();
  const { setBookingData } = useContext(AppContext);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [expandedDays, setExpandedDays] = useState<number[]>([]);
  const [activeTab, setActiveTab] = useState('summary');
  const [expandedSections, setExpandedSections] = useState<string[]>([]);
  const [packageData, setPackageData] = useState<PackageDetailsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentError, setPaymentError] = useState('');
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [paymentProcessing, setPaymentProcessing] = useState(false);
  const [sightseeingImages, setSightseeingImages] = useState<Array<{ filePath: string; DisplayIn?: string[] }>>([]);
  const [currentVisibleSection, setCurrentVisibleSection] = useState<string>('trip-highlights');
  const [data_new,setDataNew] = useState<any>([]);
  // Refs for DOM elements
  const mainContentRef = useRef<HTMLDivElement>(null);
  const recommendationRef = useRef<HTMLDivElement>(null);

  // No PayU Configuration needed here as it's moved to PaymentModal component

  // Function to collect all sightseeing images
  const collectSightseeingImages = (data: PackageDetailsType) => {
    const images: Array<{ filePath: string; DisplayIn?: string[] }> = [];

    data.days.forEach((day: any) => {
      day.cities.forEach((city: any) => {
        city.itineraries.forEach((itinerary: any) => {
          if (itinerary.excursionType === 'sightseeing' &&
            itinerary.selected_excursion.images &&
            itinerary.selected_excursion.images.length > 0) {
            images.push(...itinerary.selected_excursion.images);
          }
        });
      });
    });

    return images;
  };

  useEffect(() => {
     
    const fetchPackageData = async () => {
      try {
        // Get the query parameter from the URL
        const searchParams = new URLSearchParams(window.location.search);
        const packageCode = searchParams.get('query');
        const custom = searchParams.get('custom');

        if (!packageCode) {
          throw new Error('Package code not found in URL');
        }
        const API_END_POINT = custom=="true" ? `${NEXT_PUBLIC_API_BASE_URL}/api/packages/${packageCode}` :`${NEXT_PUBLIC_API_BASE_URL}/api/packages/website/${packageCode}` 
        const { data } = await axios.get(API_END_POINT);
        console.log('Package data received:', data);
        let day_sorted_by_day_number = data?.days;
        day_sorted_by_day_number.sort((a:any,b:any)=>{
            return a.day_number - b.day_number
        })
        data.days = day_sorted_by_day_number;
        setPackageData(data);
        if(data?.applicableDiscountRules){
          setApplicableDiscountRules(data?.applicableDiscountRules);
        }
        setSightseeingImages(collectSightseeingImages(data));
      } catch (error) {
        console.error('Error fetching package data:', error);
      } finally {
        setLoading(false);
      }
    };
    const fetchAllCityLatLong = async () => {
      if (!packageData) return;

      const days = packageData.days || [];

      const getLatLngFromAPI = async (cityName: string): Promise<{ lat: number; lng: number }> => {
        try {
          const res = await fetch(
            `https://api.opencagedata.com/geocode/v1/json?q=${cityName}&key=********************************`
            // opencagedata.com/
          );
          const data = await res.json();
          if (data?.results.length > 0) {
            return {
              lat: parseFloat(data.results[0].geometry.lat),
              lng: parseFloat(data.results[0].geometry.lng),
            };
          }
        } catch (err) {
          console.error(`Error fetching lat/lng for ${cityName}`, err);
        }
        return { lat: 0, lng: 0 }; // fallback
      };

      const latLngCache = new Map<string, { lat: number; lng: number }>();
      const cityMap = new Map<string, any>();
      const appearanceOrder: string[] = [];

      for (let dayIndex = 0; dayIndex < days.length; dayIndex++) {
        const day = days[dayIndex];

        for (let cityIndex = 0; cityIndex < day.cities.length; cityIndex++) {
          const city = day.cities[cityIndex];
          const excursions = city.itineraries || [];
          const cityName = excursions[0]?.selected_excursion?.cityName || "Unknown City";
          if (!cityName) continue;

          // Fetch lat/lng from cache or API
          let lat = 0,
            lng = 0;
          if (latLngCache.has(cityName)) {
            ({ lat, lng } = latLngCache.get(cityName)!);
          } else {
            const coords = await getLatLngFromAPI(cityName);
            lat = coords.lat;
            lng = coords.lng;
            latLngCache.set(cityName, coords);
          }

          // Gather activity names
          const activities = excursions.map((ex: any) => {
            const name =
              ex.subCodeDescriptor ||
              ex?.selected_excursion?.hotelName ||
              ex?.selected_excursion?.sightseeingName ||
              "Activity";
            return {
              type: ex.excursionType,
              name,
            };
          });

          // Detect transport
          let transport;
          for (const ex of excursions) {
            const mode = ex?.selected_excursion?.transportDetails?.[0]?.transferMode;
            if (ex.excursionType === "intercity" && mode) {
              transport = mode.toLowerCase();
              break;
            }
          }

          // Determine if it's the first city
          const isFirst = dayIndex === 0 && cityIndex === 0;
          const cityKey = cityName;

          if (!cityMap.has(cityKey)) {
            cityMap.set(cityKey, {
              name: cityName,
              city_name: cityName,
              lat,
              lng,
              type: isFirst ? "start" : "visited",
              days: [dayIndex + 1],
              transport: transport ? new Set([transport]) : new Set(),
              activities: [...activities],
            });
            appearanceOrder.push(cityKey);
          } else {
            const existing = cityMap.get(cityKey);
            if (!existing.days.includes(dayIndex + 1)) {
              existing.days.push(dayIndex + 1);
            }
            activities.forEach((act) => existing.activities.push(act));
            if (transport) existing.transport.add(transport);

            // Do not downgrade 'start' to 'visited'
            if (existing.type !== "start") {
              existing.type = "visited";
            }
          }
        }
      }

      // Mark the last city as 'end' (if it's not already 'start')
      const lastCity = appearanceOrder[appearanceOrder.length - 1];
      if (cityMap.has(lastCity)) {
        const entry = cityMap.get(lastCity);
        if (entry.type !== "start") {
          entry.type = "end";
        }
      }

      // Convert Map to Array and cleanup transport set
      const finalResult = Array.from(cityMap.values()).map((entry) => ({
        ...entry,
        transport: entry.transport.size > 0 ? Array.from(entry.transport).join(", ") : undefined,
      }));

      setDataNew(finalResult);
      console.log("Final city data with day info:", finalResult);
    };

    // fetchAllCityLatLong();
    fetchPackageData();
  }, [query ]);
  
  // useEffect(() => {
     
  //   const fetchPackageData = async () => {
  //     try {
  //       // Get the query parameter from the URL
  //       const searchParams = new URLSearchParams(window.location.search);
  //       const packageCode = searchParams.get('query');
  //       const custom = searchParams.get('custom');

  //       if (!packageCode) {
  //         throw new Error('Package code not found in URL');
  //       }
  //       const API_END_POINT = custom=="true" ? `${NEXT_PUBLIC_API_BASE_URL}/api/packages/${packageCode}` :`${NEXT_PUBLIC_API_BASE_URL}/api/packages/website/${packageCode}` 
  //       const { data } = await axios.get(API_END_POINT);
  //       console.log('Package data received:', data);
  //       let day_sorted_by_day_number = data?.days;
  //       day_sorted_by_day_number.sort((a:any,b:any)=>{
  //           return a.day_number - b.day_number
  //       })
  //       data.days = day_sorted_by_day_number;
  //       setPackageData(data);
  //       if(data?.applicableDiscountRules){
  //         setApplicableDiscountRules(data?.applicableDiscountRules);
  //       }
  //       setSightseeingImages(collectSightseeingImages(data));
  //     } catch (error) {
  //       console.error('Error fetching package data:', error);
  //     } finally {
  //       setLoading(false);
  //     }
  //   };
  //   const fetchAllCityLatLong = async () => {
  //     if (!packageData) return;

  //     const days = packageData.days || [];

  //     const getLatLngFromAPI = async (cityName: string): Promise<{ lat: number; lng: number }> => {
  //       try {
  //         const res = await fetch(
  //           `https://api.opencagedata.com/geocode/v1/json?q=${cityName}&key=********************************`
  //           // opencagedata.com/
  //         );
  //         const data = await res.json();
  //         if (data?.results.length > 0) {
  //           return {
  //             lat: parseFloat(data.results[0].geometry.lat),
  //             lng: parseFloat(data.results[0].geometry.lng),
  //           };
  //         }
  //       } catch (err) {
  //         console.error(`Error fetching lat/lng for ${cityName}`, err);
  //       }
  //       return { lat: 0, lng: 0 }; // fallback
  //     };

  //     const latLngCache = new Map<string, { lat: number; lng: number }>();
  //     const cityMap = new Map<string, any>();
  //     const appearanceOrder: string[] = [];

  //     for (let dayIndex = 0; dayIndex < days.length; dayIndex++) {
  //       const day = days[dayIndex];

  //       for (let cityIndex = 0; cityIndex < day.cities.length; cityIndex++) {
  //         const city = day.cities[cityIndex];
  //         const excursions = city.itineraries || [];
  //         const cityName = excursions[0]?.selected_excursion?.cityName || "Unknown City";
  //         if (!cityName) continue;

  //         // Fetch lat/lng from cache or API
  //         let lat = 0,
  //           lng = 0;
  //         if (latLngCache.has(cityName)) {
  //           ({ lat, lng } = latLngCache.get(cityName)!);
  //         } else {
  //           const coords = await getLatLngFromAPI(cityName);
  //           lat = coords.lat;
  //           lng = coords.lng;
  //           latLngCache.set(cityName, coords);
  //         }

  //         // Gather activity names
  //         const activities = excursions.map((ex: any) => {
  //           const name =
  //             ex.subCodeDescriptor ||
  //             ex?.selected_excursion?.hotelName ||
  //             ex?.selected_excursion?.sightseeingName ||
  //             "Activity";
  //           return {
  //             type: ex.excursionType,
  //             name,
  //           };
  //         });

  //         // Detect transport
  //         let transport;
  //         for (const ex of excursions) {
  //           const mode = ex?.selected_excursion?.transportDetails?.[0]?.transferMode;
  //           if (ex.excursionType === "intercity" && mode) {
  //             transport = mode.toLowerCase();
  //             break;
  //           }
  //         }

  //         // Determine if it's the first city
  //         const isFirst = dayIndex === 0 && cityIndex === 0;
  //         const cityKey = cityName;

  //         if (!cityMap.has(cityKey)) {
  //           cityMap.set(cityKey, {
  //             name: cityName,
  //             city_name: cityName,
  //             lat,
  //             lng,
  //             type: isFirst ? "start" : "visited",
  //             days: [dayIndex + 1],
  //             transport: transport ? new Set([transport]) : new Set(),
  //             activities: [...activities],
  //           });
  //           appearanceOrder.push(cityKey);
  //         } else {
  //           const existing = cityMap.get(cityKey);
  //           if (!existing.days.includes(dayIndex + 1)) {
  //             existing.days.push(dayIndex + 1);
  //           }
  //           activities.forEach((act) => existing.activities.push(act));
  //           if (transport) existing.transport.add(transport);

  //           // Do not downgrade 'start' to 'visited'
  //           if (existing.type !== "start") {
  //             existing.type = "visited";
  //           }
  //         }
  //       }
  //     }

  //     // Mark the last city as 'end' (if it's not already 'start')
  //     const lastCity = appearanceOrder[appearanceOrder.length - 1];
  //     if (cityMap.has(lastCity)) {
  //       const entry = cityMap.get(lastCity);
  //       if (entry.type !== "start") {
  //         entry.type = "end";
  //       }
  //     }

  //     // Convert Map to Array and cleanup transport set
  //     const finalResult = Array.from(cityMap.values()).map((entry) => ({
  //       ...entry,
  //       transport: entry.transport.size > 0 ? Array.from(entry.transport).join(", ") : undefined,
  //     }));

  //     setDataNew(finalResult);
  //     console.log("Final city data with day info:", finalResult);
  //   };

  //   // fetchAllCityLatLong();
  //   fetchPackageData();
  // }, []);

  // Set up the fixed booking card position
  useEffect(() => {
    const updateBookingCardPosition = () => {
      const bookingCardPlaceholder = document.getElementById('booking-card-placeholder');
      const fixedBookingCard = document.getElementById('fixed-booking-card');
      const imageCarousel = document.querySelector('.col-span-12.lg\\:col-span-7 > div:first-child');

      if (bookingCardPlaceholder && fixedBookingCard && imageCarousel) {
        // Get the position of the image carousel
        const carouselRect = imageCarousel.getBoundingClientRect();

        // Set the initial top position to match the image carousel, but ensure it's not too high
        if (!fixedBookingCard.style.top || fixedBookingCard.style.top === '120px') {
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          const topPosition = carouselRect.top + scrollTop;

          // Ensure the booking card is not positioned above the minimum safe top position (120px)
          const minSafeTop = 120;
          const finalTopPosition = Math.max(topPosition, minSafeTop);

          fixedBookingCard.style.top = `${finalTopPosition}px`;
        }
      }
    };

    // Initial positioning
    setTimeout(updateBookingCardPosition, 100); // Small delay to ensure elements are rendered

    // Update on resize
    window.addEventListener('resize', updateBookingCardPosition);

    return () => {
      window.removeEventListener('resize', updateBookingCardPosition);
    };
  }, []);

  // Booking card behavior is now handled in the combined scroll handler below

  // Sidebar navigation with menu highlighting
  useEffect(() => {
    // Function to highlight the active section in the sidebar
    const highlightActiveSectionInSidebar = () => {
      // Remove active class from all nav items
      document.querySelectorAll('.navigation-sidebar a').forEach(item => {
        item.classList.remove('active');
      });

      // Find the sections on the page
      const sections = document.querySelectorAll('[id^="trip-"], [id^="destinations"], [id="itinerary"], [id="inclusion"], [id="exclusion"], [id="vouchers"], [id="payment-policy"]');

      // Find the section closest to the reference position
      let closestSection: HTMLElement | null = null;
      let closestDistance = Infinity;

      // We'll use a position 150px from the top of the viewport as our reference point
      const referencePosition = 150;

      sections.forEach((section: Element) => {
        const rect = section.getBoundingClientRect();

        // Calculate the distance from the reference position to the top of the section
        const distance = Math.abs(rect.top - referencePosition);

        // If this section is closer to our reference position than the current closest, update
        if (distance < closestDistance) {
          closestDistance = distance;
          closestSection = section as HTMLElement;
        }
      });

      // Highlight only the closest section's nav item and update current visible section
      if (closestSection) {
        // Use optional chaining and type assertion to access the id property safely
        const id = (closestSection as any).id || '';

        // Only update if we have a valid ID
        if (id) {
          // Update the current visible section
          setCurrentVisibleSection(id);
          
          // Find and highlight only the corresponding nav item
          const activeNavItem = document.querySelector(`.navigation-sidebar a[href="#${id}"]`);
          if (activeNavItem && activeNavItem instanceof HTMLElement) {
            activeNavItem.classList.add('active');
          }
        }
      }
    };

    // Update on scroll
    window.addEventListener('scroll', highlightActiveSectionInSidebar);

    // Initial call
    highlightActiveSectionInSidebar();

    return () => {
      window.removeEventListener('scroll', highlightActiveSectionInSidebar);
    };
  }, []);

  // Handle booking card and sidebar in separate effects for independent behavior

  // Create a ref outside the useEffect to track the booking card state
  const hasFixedBookingCardRef = useRef(false);

  // Handle booking card with a completely different approach to fix flickering
  useEffect(() => {
    // Use requestAnimationFrame for smoother animations
    let ticking = false;

    const handleBookingCardScroll = () => {
      const fixedBookingCard = document.getElementById('fixed-booking-card');
      const recommendationSection = recommendationRef.current;

      if (!fixedBookingCard || !recommendationSection) return;

      // Get the position of the recommendation section
      const recommendationRect = recommendationSection.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      // Get the height of the booking card
      const bookingCardHeight = fixedBookingCard.clientHeight;

      // Calculate the distance from the top of the recommendation section to the bottom of the viewport
      const distanceToRecommendation = recommendationRect.top - window.innerHeight;

      // Add a buffer to prevent flickering at the transition point
      const buffer = 50;

      // Check if we've reached the recommendation section with a buffer zone
      const reachedRecommendation = distanceToRecommendation < -buffer;

      // Only update if the state has changed and we're outside the buffer zone
      if (reachedRecommendation !== hasFixedBookingCardRef.current &&
        Math.abs(distanceToRecommendation) > buffer) {

        if (reachedRecommendation) {
          // We just reached the recommendation section
          hasFixedBookingCardRef.current = true;

          // Calculate the position where the booking card should stop
          // Use a fixed offset from the recommendation section
          const recommendationTop = recommendationRect.top + scrollTop - 100; // 100px buffer for more space

          // Fix the booking card at this position
          fixedBookingCard.style.position = 'absolute';
          fixedBookingCard.style.top = `${recommendationTop - bookingCardHeight}px`;
          fixedBookingCard.classList.add('stop-floating');
        } else {
          // We're scrolling back up and leaving the recommendation section
          hasFixedBookingCardRef.current = false;

          // Restore the booking card
          fixedBookingCard.style.position = 'fixed';
          fixedBookingCard.style.top = '120px';
          fixedBookingCard.classList.remove('stop-floating');
        }
      }

      ticking = false;
    };

    // Use requestAnimationFrame for smoother animations
    const requestTick = () => {
      if (!ticking) {
        requestAnimationFrame(handleBookingCardScroll);
        ticking = true;
      }
    };

    // Initial call with a delay to ensure elements are rendered
    setTimeout(handleBookingCardScroll, 100);
    setTimeout(handleBookingCardScroll, 500);

    // Update on scroll and resize with requestAnimationFrame
    window.addEventListener('scroll', requestTick);
    window.addEventListener('resize', requestTick);
    window.addEventListener('load', handleBookingCardScroll);

    return () => {
      window.removeEventListener('scroll', requestTick);
      window.removeEventListener('resize', requestTick);
      window.removeEventListener('load', handleBookingCardScroll);
    };
  }, [recommendationRef]);

  // Handle sidebar separately
  useEffect(() => {
    let hasFixedSidebar = false;

    // Use requestAnimationFrame for smoother animations
    let ticking = false;

    const handleSidebarScroll = () => {
      const fixedSidebar = document.getElementById('fixed-sidebar');
      const recommendationSection = recommendationRef.current;
      const tripHighlightsSection = document.getElementById('trip-highlights');

      if (!fixedSidebar || !recommendationSection || !tripHighlightsSection) {
        ticking = false;
        return;
      }

      // Get the position of the recommendation section and trip highlights
      const recommendationRect = recommendationSection.getBoundingClientRect();
      const tripHighlightsRect = tripHighlightsSection.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      // Calculate the trip highlights position
      const tripHighlightsTop = tripHighlightsRect.top + scrollTop;

      // Check if we've scrolled past the trip highlights section
      const pastTripHighlights = window.scrollY > tripHighlightsTop - 120; // 120px is the default top position

      // Check if we've reached the recommendation section
      // Use a more conservative threshold for the sidebar
      const reachedRecommendation = recommendationRect.top < window.innerHeight * 0.7;

      // Add a buffer to prevent flickering at transition points
      const buffer = 50;

      // Handle sidebar independently - only update when necessary
      if (reachedRecommendation && !hasFixedSidebar) {
        // We just reached the recommendation section
        hasFixedSidebar = true;

        // Calculate the position where the sidebar should stop
        const recommendationTop = recommendationRect.top + scrollTop - 200; // 200px buffer for more space

        // Fix the sidebar at this position
        const currentLeft = fixedSidebar.style.left;
        fixedSidebar.style.position = 'absolute';
        fixedSidebar.style.top = `${recommendationTop - fixedSidebar.clientHeight}px`;
        // Ensure the left position is maintained
        fixedSidebar.style.left = currentLeft;
        fixedSidebar.classList.add('stop-floating');
      }
      else if (!reachedRecommendation && hasFixedSidebar &&
        Math.abs(recommendationRect.top - window.innerHeight * 0.7) > buffer) {
        // We're scrolling back up and leaving the recommendation section
        // Only change if we're clearly outside the buffer zone
        hasFixedSidebar = false;

        // Restore the sidebar
        fixedSidebar.style.position = 'fixed';

        // Set the top position based on whether we're past the trip highlights section
        if (pastTripHighlights) {
          fixedSidebar.style.top = '120px'; // Default position when scrolling
        } else {
          // Position at trip highlights when above it
          fixedSidebar.style.top = `${tripHighlightsTop}px`;
        }

        // Get the position of the first column to align the sidebar with it
        const firstColumn = document.querySelector('.grid-cols-12 > div:first-child');
        if (firstColumn) {
          const columnRect = firstColumn.getBoundingClientRect();
          const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
          fixedSidebar.style.left = `${columnRect.left + scrollLeft + 5}px`; // 5px offset to prevent overlap
        }

        fixedSidebar.classList.remove('stop-floating');
      }
      // Handle the case when we're above trip highlights but not at recommendation section
      else if (!reachedRecommendation && !hasFixedSidebar && !pastTripHighlights &&
        Math.abs(window.scrollY - (tripHighlightsTop - 120)) > buffer) {
        // Position at trip highlights - only if we're clearly above it
        fixedSidebar.style.position = 'absolute';
        fixedSidebar.style.top = `${tripHighlightsTop}px`;
      }
      // Handle the case when we're between trip highlights and recommendation section
      else if (!reachedRecommendation && !hasFixedSidebar && pastTripHighlights &&
        Math.abs(window.scrollY - (tripHighlightsTop - 120)) > buffer) {
        // Use fixed position with default top - only if we're clearly past trip highlights
        fixedSidebar.style.position = 'fixed';
        fixedSidebar.style.top = '120px';
      }

      ticking = false;
    };

    // Use requestAnimationFrame for smoother animations
    const requestTick = () => {
      if (!ticking) {
        requestAnimationFrame(handleSidebarScroll);
        ticking = true;
      }
    };

    // Initial setup and positioning
    const initSidebar = () => {
      const fixedSidebar = document.getElementById('fixed-sidebar');
      const firstColumn = document.querySelector('.grid-cols-12 > div:first-child');
      const tripHighlightsSection = document.getElementById('trip-highlights');

      if (fixedSidebar && firstColumn) {
        // Get the position of the first column to align the sidebar with it
        const columnRect = firstColumn.getBoundingClientRect();
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

        // Set the sidebar position to match the first column with a small offset
        fixedSidebar.style.left = `${columnRect.left + scrollLeft + 5}px`; // 5px offset to prevent overlap

        // Position the sidebar at the trip highlights section
        if (tripHighlightsSection) {
          const tripHighlightsRect = tripHighlightsSection.getBoundingClientRect();
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          const topPosition = tripHighlightsRect.top + scrollTop;

          // Only update if we're not already at the trip highlights position
          if (parseInt(fixedSidebar.style.top) !== topPosition) {
            fixedSidebar.style.top = `${topPosition}px`;
          }
        }

        // Then run the scroll handler
        handleSidebarScroll();
      }
    };

    // Call with multiple delays to ensure elements are rendered
    setTimeout(initSidebar, 100);
    setTimeout(initSidebar, 500);
    setTimeout(initSidebar, 1000);

    // Handle window resize specifically to reposition the sidebar
    const handleResize = () => {
      const fixedSidebar = document.getElementById('fixed-sidebar');
      const firstColumn = document.querySelector('.grid-cols-12 > div:first-child');

      if (fixedSidebar && firstColumn) {
        // Get the position of the first column to align the sidebar with it
        const columnRect = firstColumn.getBoundingClientRect();
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

        // Set the sidebar position to match the first column with a small offset
        fixedSidebar.style.left = `${columnRect.left + scrollLeft + 5}px`; // 5px offset to prevent overlap
      }

      // Then run the scroll handler using requestAnimationFrame
      requestTick();
    };

    // Update on scroll, resize and load with requestAnimationFrame
    window.addEventListener('scroll', requestTick);
    window.addEventListener('resize', handleResize);
    window.addEventListener('load', initSidebar);

    return () => {
      window.removeEventListener('scroll', requestTick);
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('load', initSidebar);
    };
  }, [recommendationRef]);

  // Set up the intersection observer for the recommendation section
  useEffect(() => {
    if (!recommendationRef.current) return;

    // Create an observer for the recommendation section
    const observer = new IntersectionObserver(
      ([entry]) => {
        // Add a visual indicator that we've reached the recommendations
        if (entry.isIntersecting) {
          const recommendationSection = entry.target as HTMLElement;
          recommendationSection.classList.add('recommendation-visible');

          // Scroll into view with a small offset if the user is very close to it
          if (entry.intersectionRatio > 0.5) {
            const headerOffset = 20; // Small offset for smooth transition
            const currentScrollPos = window.scrollY;
            const targetScrollPos = recommendationSection.getBoundingClientRect().top + currentScrollPos - headerOffset;

            // Only do this if we're very close to avoid disrupting normal scrolling
            const scrollDifference = Math.abs(currentScrollPos - targetScrollPos);
            if (scrollDifference < 100) {
              window.scrollTo({
                top: targetScrollPos,
                behavior: 'smooth'
              });
            }
          }
        } else {
          if (recommendationRef.current) {
            recommendationRef.current.classList.remove('recommendation-visible');
          }
        }
      },
      {
        rootMargin: '-20px 0px 0px 0px', // Adjusted for better transition with new layout
        threshold: [0, 0.25, 0.5, 0.75, 1], // Multiple thresholds for smoother transition
      }
    );

    observer.observe(recommendationRef.current);

    // Create a throttle function to limit how often the sidebar highlighting runs
    const throttle = (func: Function, limit: number) => {
      let inThrottle: boolean = false;
      return function (this: any, ...args: any[]) {
        if (!inThrottle) {
          func.apply(this, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    };

    // Create a completely different approach for sidebar highlighting
    // Instead of using IntersectionObserver, we'll use scroll events
    const handleSidebarHighlighting = () => {
      // Get all sections that have IDs
      const sections = document.querySelectorAll('section[id], div[id="trip-highlights"], div[id="destinations"], div[id="itinerary"], div[id="inclusion"], div[id="exclusion"], div[id="payment-policy"]');
      const navItems = document.querySelectorAll('.navigation-sidebar a');

      // First, remove active class from all nav items
      navItems.forEach(item => {
        item.classList.remove('active');
      });

      // Find the section that is currently in view
      let currentSection = '';

      sections.forEach((section: Element) => {
        const sectionRect = section.getBoundingClientRect();
        // Consider a section in view if it's in the top half of the viewport
        if (sectionRect.top < window.innerHeight / 2 && sectionRect.bottom > 0) {
          currentSection = section.getAttribute('id') || '';
        }
      });

      // Highlight the corresponding nav item
      if (currentSection) {
        navItems.forEach((item: Element) => {
          const href = item.getAttribute('href');
          if (href === `#${currentSection}`) {
            (item as HTMLElement).classList.add('active');
          } else {
            (item as HTMLElement).classList.remove('active');
          }
        });
      }
    }

    // Throttle the function to run at most once every 100ms
    const throttledHighlighting = throttle(handleSidebarHighlighting, 100);

    // Call the throttled function on scroll
    window.addEventListener('scroll', throttledHighlighting);

    // Initial call
    setTimeout(handleSidebarHighlighting, 100);

    // Clean up
    return () => {
      window.removeEventListener('scroll', throttledHighlighting);

      if (recommendationRef.current) {
        observer.unobserve(recommendationRef.current);
      }
    };
  }, []);

  const toggleSection = (section: string) => {
    setExpandedSections(prev =>
      prev.includes(section)
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };



  const tripHighlights = [
    {
      title: "Venice - 2 nights",
      description: "Experience the romantic canals, historic architecture, and unique culture of Venice with a 2-night stay in this enchanting city.",
      duration: "2 nights",
      image: "/assets/tours/venice-highlight.jpg"
    },
    {
      title: "Rome - 2 nights",
      description: "Explore the eternal city of Rome, visiting ancient ruins, Vatican City, and experiencing the vibrant Italian lifestyle.",
      duration: "2 nights",
      image: "/assets/tours/rome-highlight.jpg"
    },
    {
      title: "5 Days & 4 Nights",
      description: "A perfectly balanced 5-day Italian adventure covering the best of Venice and Rome, with comfortable accommodations and guided tours.",
      duration: "Total duration",
      image: "/assets/tours/italy-duration.jpg"
    },
    {
      title: "Guided Tours & Activities",
      description: "Expert-led tours of major attractions, skip-the-line access, and authentic local experiences including cooking classes and gondola rides.",
      duration: "Throughout the trip",
      image: "/assets/tours/guided-tours.jpg"
    }
  ];

  // Payment functions moved to PaymentModal component

  const handleBookNow = (date?:any,rule?:any) => {
    if(applicableDiscountRulesVisible == false && applicableDiscountRules.length >0){
      setApplicableDiscountRulesVisible(true);
      return;
    }
    setCuponData({});
    setCuponApplied(false);
    setBookingData((prev: any) => ({
      ...prev,
      travellers:[],
      // packageData:null,
      date:date instanceof Date && !isNaN(date.getTime()) ? date:"",
      rule:rule?rule:null,
      packageCode: packageData?.packageCode || ''
    }));
    // Redirect to traveller page
    const searchParams = new URLSearchParams(window.location.search);
    const custom = searchParams.get('custom');
    let url_redirect =  custom=="true"? "package-details/travellers?custom=true" : 'package-details/travellers';
    router.push(url_redirect);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <div className="w-[90vw] h-[90vh]">
          <Skeleton height="100%" width="100%" />
        </div>
      </div>
    )
  }

  if (!packageData) {
    return <div className="flex justify-center items-center min-h-screen">Error loading package data</div>;
  }
  if(packageData && packageData?.status?.toLowerCase()!=="active"){
    return <div className="flex justify-center items-center min-h-screen">Package is not active..</div>;

  }

  return (
    <div className="flex-1 bg-white" ref={mainContentRef}>
      {/* Fixed Sidebar - Positioned outside main container */}
      <div id="fixed-sidebar" className="fixed-sidebar hidden lg:block">
        <NavigationSidebar className="navigation-sidebar" />
      </div>

      <div className="container mx-auto max-w-7xl py-2 sm:py-2 lg:py-2 px-4">
        {/* Top Section - Three Column Layout (Image Carousel + Package Info, and Booking Card) */}
        <div className="grid grid-cols-12 mb-6">
          {/* Main column - Image Carousel and Package Info - aligned with breadcrumbs */}
          <div className="col-span-12 lg:col-span-8 lg:px-0">
            {/* Image Carousel */}
            <div className="mb-6">
              <ImageCarousel
                images={sightseeingImages}
                fallbackImage={packageData?.packageMainImage || '/assets/tours/default-image.jpg'}
                altText={packageData?.packageTitle || 'Tour package'}
              />
            </div>

            {/* Package Info - Directly below carousel */}
            <PackageInfo packageData={packageData}  />
          </div>

          {/* Booking Card - Desktop - Fixed Position - adjusted column span */}
          <div className="hidden lg:block lg:col-span-4">{/* Adjusted to fill remaining space */}
            <div id="booking-card-placeholder" style={{ height: '1px' }}></div>
          </div>

          {/* Fixed Booking Card - Positioned outside the grid */}
          <div id="fixed-booking-card" className="fixed-booking-card hidden lg:block">
            <BookingCard setLoadingForDownload={setLoadingForDownload}  componentRefData={componentRefData}  packageData={packageData} handleBookNow={handleBookNow} />
          </div>
        </div>

        {/* Mobile Booking Card */}
        <div className="lg:hidden mb-6">
          <BookingCard setLoadingForDownload={setLoadingForDownload} componentRefData={componentRefData} packageData={packageData} handleBookNow={handleBookNow} />
        </div>

        {/* Mobile Navigation - Horizontal - always visible on mobile */}
        <div className="lg:hidden mb-6 overflow-x-auto">
          <NavigationSidebar className="mobile-nav" />
        </div>

        <div className="grid grid-cols-12">
          {/* Main Content Area - adjusted with margin for always-visible sidebar */}
          <div className="col-span-12 lg:col-span-8 lg:px-0 lg:ml-[150px]">
            <div className="relative" id="main-content">
              {/* Top Highlights */}
              <TripHighlights packageData={packageData} />

              {/* Destination Map */}
              <DestinationMap
                mapImage="/assets/tours/map.png"
                altText={`${packageData.packageTitle} route map`}
              />
              {/* <MapPage packageData={packageData} data_to_plot={data_new}/> */}

              {/* Itinerary Section */}
              <ItinerarySection packageData={packageData} from_download_pdf={from_download_pdf}/>

              {/* Inclusions & Exclusions */}
              <InclusionsExclusions packageData={packageData} from_download_pdf={from_download_pdf}/>

              {/* Vouchers Section */}
            {/* {packageData?.itineraryType?.toLowerCase() =="final" &&
              <VoucherSection
                packageData={packageData}
                expandedSections={expandedSections}
                toggleSection={toggleSection}
              />} */}

              {/* Payment Policy Section */}
              <PaymentPolicySection
                packageData={packageData}
                expandedSections={expandedSections}
                setExpandedSections={setExpandedSections}
                toggleSection={toggleSection}
                from_download_pdf={from_download_pdf}
              />
            </div>
          </div>

          {/* Right Column (350px) - Empty for desktop since booking card is at the top */}
          <div className="hidden lg:block lg:col-span-4 lg:pl-6">{/* Adjusted to fill remaining space */}
            {/* This column is intentionally left empty as the booking card is already at the top */}
          </div>
        </div>

        {/* Recommendation Section - This is where the sidebar stops being sticky */}
        <section
          ref={recommendationRef}
          className="relative z-20"
          id="recommendations"
        >
          {/* Smaller spacer to reduce excessive space */}
          <div className="h-10 w-full"></div>


        </section>
      </div>
      <DiscountModal
      applicableDiscountRulesVisible={applicableDiscountRulesVisible}
      discountRules={applicableDiscountRules}
      packageData={packageData}
      onClose={() => setApplicableDiscountRulesVisible(false)}
      onSelectDate={(date:any, rule:any) => {
      let date_new = date;
      let rule_new = rule;
      handleBookNow(date_new,rule_new);
      setApplicableDiscountRulesVisible(false);
      }}
      />       
      <PaymentModal
        showPaymentModal={showPaymentModal}
        setShowPaymentModal={setShowPaymentModal}
        packageData={packageData}
        paymentError={paymentError}
        setPaymentError={setPaymentError}
        paymentProcessing={paymentProcessing}
        setPaymentProcessing={setPaymentProcessing}
        paymentSuccess={paymentSuccess}
        setPaymentSuccess={setPaymentSuccess}
      />
    </div>
  );

};


export default PackageDetails;






// test = JSON.parse('`[{"name":"Zurich","city_name":"Zurich","lat":47.3744489,"lng":8.5410422,"type":"start","days":[1,2],"transport":"train (with swiss pass)","activities":[{"type":"airport","name":"ZRH-Airport to Hotel-Shuttle service"},{"type":"hotel","name":"Hotel Check-in (3*)-Standard Room"},{"type":"sightseeing","name":"Zurich Walking Tour"},{"type":"sightseeing","name":"Rhine Falls"},{"type":"sightseeing","name":"Swiss Pass"},{"type":"intercity","name":"Zurich to Interlaken-Train (with Swiss Pass) 2h"}]},{"name":"Interlaken","city_name":"Interlaken","lat":46.6855231,"lng":7.8585139,"type":"visited","days":[2,3],"transport":"train (with swiss pass)","activities":[{"type":"hotel","name":"Hotel Check-in (3*)-Standard Room"},{"type":"sightseeing","name":"Interlaken Region Full Day"},{"type":"intercity","name":"Interlaken to Lucerne-Train (With Swiss Pass) 2h"}]},{"name":"Lucerne","city_name":"Lucerne","lat":47.0505452,"lng":8.3054682,"type":"visited","days":[3,4],"transport":"train","activities":[{"type":"hotel","name":"Hotel Check-in (3*)-Standard Room"},{"type":"sightseeing","name":"Mount Titlis by Cable Car"},{"type":"intercity","name":"Lucerne to Venice-Train 8h"}]},{"name":"Venice","city_name":"Venice","lat":45.4371908,"lng":12.3345898,"type":"visited","days":[4,5],"transport":"train evening between 3-6 pm)","activities":[{"type":"hotel","name":"Hotel Check-in (3*)-Standard Room"},{"type":"sightseeing","name":"Key Attractions of Venice"},{"type":"sightseeing","name":"Murano, Burano and Torcello Islands"},{"type":"intercity","name":"Venice to Florence-Train Evening between 3-6 PM) 2h"}]},{"name":"Florence","city_name":"Florence","lat":43.7697955,"lng":11.2556404,"type":"visited","days":[5,6],"transport":"train (evening between 3-6 pm)","activities":[{"type":"hotel","name":"Hotel Check-in (3*)-Standard Room"},{"type":"sightseeing","name":"Key Attractions Of Florence-Key Attractions Of Florence"},{"type":"sightseeing","name":"Leaning Tower of Pisa"},{"type":"intercity","name":"Florence to Rome-Train (Evening between 3-6 PM) 2h"}]},{"name":"Rome","city_name":"Rome","lat":41.8933203,"lng":12.4829321,"type":"end","days":[6,7,8],"activities":[{"type":"hotel","name":"Hotel Check-in (3*)-Standard Room"},{"type":"sightseeing","name":"Key Attractions Of Rome"},{"type":"sightseeing","name":"Colosseum"},{"type":"sightseeing","name":"Vatican City and St Peter's Basilica"},{"type":"airport","name":"FCO-Hotel to Airport-Cab"}]}]`)