import React from 'react';

interface PDFDownloadButtonProps {
  query: string;
  isCustom?:boolean,
  fileName?: string;
  buttonText?: string;
  className?: string;
  setLoadingForDownload?:any;
}

const PDFDownloadButton: React.FC<PDFDownloadButtonProps> = ({
  query,
  isCustom=false,
  fileName = `PackageDetail_${query}.pdf`,
  buttonText = 'Download PDF',
  className = '',
  setLoadingForDownload=null
}) => {
  const handleDownload = async () => {
    setLoadingForDownload(true);
    try {
      const response = await fetch(`/api/generate-pdf?query=${query}&isCustom=${isCustom}`);

      if (!response.ok) {
        throw new Error('Failed to generate PDF');
      }

      const blob = await response.blob();

      // Trigger download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
      setLoadingForDownload(false);
    } catch (error) {
      console.error('PDF download failed:', error);
      alert('Failed to download PDF. Please check the console.');
      setLoadingForDownload(false);

    }
  };

  return (
    // <button onClick={handleDownload} className={className}>
    <>
      <button onClick={handleDownload}  className={`px-4 py-2 mx-auto block bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-md transition duration-300 ease-in-out font-semibold ${className} mb-3`}>
        {buttonText}
      </button>
    </>
  );
};

export default PDFDownloadButton;




// import { RefObject } from 'react';
// import domtoimage from 'dom-to-image';
// import jsPDF from 'jspdf';

// interface PDFDownloadButtonProps {
//   targetRef: RefObject<HTMLElement>;
//   fileName?: string;
//   buttonText?: string;
//   className?: string;
// }

// const PDFDownloadButton: React.FC<PDFDownloadButtonProps> = ({
//   targetRef,
//   fileName = 'download.pdf',
//   buttonText = 'Download PDF',
//   className = '',
// }) => {
//   const handleDownload = async () => {
//     if (!targetRef.current) return;

//     try {
//       const blob = await domtoimage.toPng(targetRef.current, {
//         cacheBust: true,
//         quality: 1,
//         bgcolor: '#ffffff',
//       });

//       const img = new Image();
//       img.src = blob;

//       img.onload = () => {
//         const pdf = new jsPDF({
//           orientation: 'portrait',
//           unit: 'px',
//           format: [img.width, img.height],
//         });

//         pdf.addImage(img, 'PNG', 0, 0, img.width, img.height);
//         pdf.save(fileName);
//       };
//     } catch (error) {
//       console.error('PDF generation failed:', error);
//       alert('PDF generation failed! Check console for details.');
//     }
//   };

//   return (
//     <button onClick={handleDownload} className={className}>
//       {buttonText}
//     </button>
//   );
// };

// export default PDFDownloadButton;




// import { useRef } from 'react';
// import html2canvas from 'html2canvas';
// import jsPDF from 'jspdf';

// interface PDFDownloadButtonProps {
//   targetRef: React.RefObject<HTMLElement>;
//   fileName?: string;
//   buttonText?: string;
//   className?: string;
// }

// const PDFDownloadButton: React.FC<PDFDownloadButtonProps> = ({
//   targetRef,
//   fileName = 'download.pdf',
//   buttonText = 'Download PDF',
//   className = '',
// }) => {
//   const handleDownload = async () => {
//     if (!targetRef.current) return;

//     // Clone the node deeply
//     const clonedNode = targetRef.current.cloneNode(true) as HTMLElement;

//     // Replace unsupported CSS colors
//     const applySafeColors = (element: HTMLElement) => {
//       const computedStyle = window.getComputedStyle(element);

//       // If color uses oklch or any unsupported format, override
//       if (computedStyle.color.includes('oklch')) {
//         element.style.color = '#000000';
//       }
//       if (computedStyle.backgroundColor.includes('oklch')) {
//         element.style.backgroundColor = '#ffffff';
//       }

//       // Recursively check children
//       Array.from(element.children).forEach((child) => {
//         applySafeColors(child as HTMLElement);
//       });
//     };

//     applySafeColors(clonedNode);

//     // Render the node in a hidden container
//     const container = document.createElement('div');
//     container.style.position = 'fixed';
//     container.style.top = '-10000px';
//     container.style.left = '-10000px';
//     container.style.zIndex = '-1';
//     container.appendChild(clonedNode);
//     document.body.appendChild(container);

//     try {
//       const canvas = await html2canvas(clonedNode, {
//         backgroundColor: '#ffffff',
//         useCORS: true,
//         allowTaint: true,
//         logging: false,
//       });

//       const imgData = canvas.toDataURL('image/png');
//       const pdf = new jsPDF({
//         orientation: 'portrait',
//         unit: 'px',
//         format: [canvas.width, canvas.height],
//       });

//       pdf.addImage(imgData, 'PNG', 0, 0, canvas.width, canvas.height);
//       pdf.save(fileName);
//     } catch (error) {
//       console.error('PDF generation failed:', error);
//       alert('PDF generation failed. Check console for details.');
//     } finally {
//       document.body.removeChild(container);
//     }
//   };

//   return (
//     <button onClick={handleDownload} className={className}>
//       {buttonText}
//     </button>
//   );
// };

// export default PDFDownloadButton;
