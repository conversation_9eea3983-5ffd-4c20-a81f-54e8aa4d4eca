"use client";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

const ShimmerCard = () => {
  return (
    <div className="flex gap-4 py-6 border-b">
      {/* Image Skeleton */}
      <div className="w-[300px] h-[180px] rounded-xl overflow-hidden">
        <Skeleton height="100%" width="100%" />
      </div>

      {/* Right Side */}
      <div className="flex flex-col justify-between flex-grow">
        {/* Top */}
        <div className="space-y-2">
          <Skeleton width={150} height={20} />
          <Skeleton width={250} height={25} />
          <Skeleton width={100} height={30} borderRadius={10} />
        </div>

        {/* Inclusions */}
        <div className="flex gap-3 mt-4">
          <Skeleton width={40} height={40} circle />
          <Skeleton width={40} height={40} circle />
          <Skeleton width={40} height={40} circle />
        </div>

        {/* Bottom Row */}
        <div className="flex justify-between items-center mt-4">
          <div>
            <Skeleton width={120} height={20} />
            <Skeleton width={100} height={15} />
          </div>
          <Skeleton width={120} height={45} borderRadius={9999} />
        </div>
      </div>
    </div>
  );
};

const FullPageShimmer = () => {
  return (
    <div className="bg-white rounded-2xl overflow-hidden cursor-pointer animate-pulse px-10 mb-10">
				<div className="flex items-center flex-col gap-10 md:gap-0 md:flex-row">
					{/* Left side - Image */}
					<div className="w-full md:w-[355px] h-[200px] bg-gray-200 rounded-3xl"></div>

					{/* Right side - Content */}
					<div className="w-full md:w-2/3 p-6 pt-1 pl-[30px]" style={{ height: '197px', paddingBottom: '1px', paddingLeft: '20px' }}>
						{/* Top Section */}
						<div className="flex justify-between items-start">
							{/* Left content */}
							<div className="flex-1">
								{/* Attributes */}
								<div className="flex items-center space-x-2 mb-2">
									<div className="h-4 w-16 bg-gray-200 rounded"></div>
									<div className="h-4 w-8 bg-gray-200 rounded"></div>
									<div className="h-4 w-12 bg-gray-200 rounded"></div>
								</div>

								{/* Tags */}
								<div className="flex flex-wrap gap-2 mb-2">
									{[...Array(3)].map((_, i) => (
										<div key={i} className="h-6 w-20 bg-gray-200 rounded-full"></div>
									))}
								</div>

								{/* Title */}
								<div className="h-6 w-3/4 bg-gray-300 rounded mb-4"></div>

								{/* Country Flags */}
								<div className="flex flex-wrap gap-2 mb-2">
									{[...Array(2)].map((_, i) => (
										<div key={i} className="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full">
											<div className="w-3 h-2.5 bg-gray-300 rounded"></div>
											<div className="h-4 w-16 bg-gray-200 rounded"></div>
										</div>
									))}
								</div>
							</div>

							{/* Price and Duration */}
							<div className="text-right mt-[30px] space-y-2">
								<div className="h-3 w-20 bg-gray-200 rounded"></div>
								<div className="flex items-baseline gap-1">
									<div className="h-7 w-24 bg-gray-300 rounded"></div>
									<div className="h-4 w-16 bg-gray-200 rounded"></div>
								</div>
								<div className="h-4 w-32 bg-gray-200 rounded"></div>
							</div>
						</div>

						{/* Bottom Section */}
						<div className="flex justify-between items-end mt-6">
							{/* What's included */}
							<div>
								<div className="h-4 w-24 bg-gray-300 rounded mb-2"></div>
								<div className="flex gap-6">
									{[...Array(3)].map((_, i) => (
										<div key={i} className="flex items-center gap-2">
											<div className="w-5 h-5 bg-gray-300 rounded-full"></div>
											<div className="h-4 w-20 bg-gray-200 rounded"></div>
										</div>
									))}
								</div>
							</div>

							{/* Explore Now Button Skeleton */}
							<div className="h-11 w-40 bg-gray-300 rounded-full"></div>
						</div>
					</div>
				</div>
			</div>

  );
};

export default FullPageShimmer;

