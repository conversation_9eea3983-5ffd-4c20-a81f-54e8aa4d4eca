"use client";

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

// Define Types for Destination Data
interface Destination {
  id: number;
  image: string;
  title: string;
  tags: string[];
  country: string[];
  price: string;
  nights: string;
  hotels: number;
  transfers: number;
  activities: number;
}

interface CountryFlags {
  [key: string]: string;
}

// Sample data in a JSON object
const destinationsData: Destination[] = [
  {
    id: 1,
    image: '/assets/tours/imageone.png', // Use your actual image path
    title: 'Paris & Berlin in 6 days with included Disneyland visit',
    tags: ['Adventure', 'Culture', 'Honeymoon', 'Western Europe'],
    country: ['France', 'Germany'],
    price: '1,05,000',
    nights: '6 Days 5 Nights',
    hotels: 2,
    transfers: 3,
    activities: 3
  },
  {
    id: 2,
    image: '/assets/tours/imagetwo.png', // Use your actual image path
    title: 'Russian Highlights: A journey through Moscow and Russian history',
    tags: ['Adventure', 'Culture', 'Honeymoon', 'Western Europe'],
    country: ['Russia'],
    price: '86,000',
    nights: '6 Days 5 Nights',
    hotels: 2,
    transfers: 3,
    activities: 3
  },
  // Add more sample data here...
];

// Country to Flag Emoji mapping
const countryFlags: CountryFlags = {
  'France': '🇫🇷',
  'Germany': '🇩🇪',
  'Japan': '🇯🇵',
  // Add other countries here...
};

// Add new filter state interfaces
interface FilterState {
  tourType: string[];
  theme: string[];
  priceRange: {
    min: number;
    max: number;
  };
  duration: string[];
}

interface SearchResult {
  id: number;
  packageName: string;
  packageTitle?: string;
  packageMainImage?: string;
  destination: string;
  duration: string;
  priceSummary: number | null;
  imageLink: string;
  tags?: string[];
  country?: string | string[];
  hotels?: number;
  transfers?: number;
  activities?: number;
  noOfDays?: number;
  noOfNights?: number;
  packageType?: string;
  packageTheme?: string;
  region?: string;
}

interface DestinationTableProps {
  searchResults: SearchResult[];
  loading: boolean;
  error: string | null;
  searchQuery: string | null;
}

const DestinationTable: React.FC<DestinationTableProps> = ({
  searchResults,
  loading,
  error,
  searchQuery
}) => {

  const [selectedFilters, setSelectedFilters] = useState<FilterState>({
    tourType: [],
    theme: [],
    priceRange: {
      min: 65000,
      max: 400000,
    },
    duration: []
  });
  const [activeTab, setActiveTab] = useState<'price-duration' | 'tour-theme'>('price-duration');
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [localSearchQuery, setLocalSearchQuery] = useState<string>('');
  const [sortOption, setSortOption] = useState<string>('');
  const [activeCategory, setActiveCategory] = useState<'price' | 'duration' | 'tourType' | 'theme'>('price');

  // Update filter state to use categories
  const [filterState, setFilterState] = useState<FilterState>({
    tourType: [],
    theme: [],
    priceRange: {
      min: 65000,
      max: 400000
    },
    duration: []
  });

  // Add price range state
  const [priceRange, setPriceRange] = useState({
    min: 65000,
    max: 400000
  });

  // Filter results based on local search
  // const filteredResults = searchResults.filter(result => {
  //   const searchTerm = localSearchQuery.toLowerCase();
  //   return (
  //     result.packageName?.toLowerCase().includes(searchTerm) ||
  //     result.packageTitle?.toLowerCase().includes(searchTerm) ||
  //     result.destination?.toLowerCase().includes(searchTerm) ||
  //     result.country?.toString().toLowerCase().includes(searchTerm) ||
  //     result.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
  //   );
  // });

  const filteredResults = searchResults
  .filter(result => {
    const searchTerm = localSearchQuery.toLowerCase();

    // 1. Text-based search
    const matchesSearch =
      (result.packageName?.toLowerCase().includes(searchTerm) ||
      result.packageTitle?.toLowerCase().includes(searchTerm) ||
      result.destination?.toLowerCase().includes(searchTerm) ||
      result.country?.toString().toLowerCase().includes(searchTerm) ||
      result.tags?.some(tag => tag.toLowerCase().includes(searchTerm))) ?? false;

    if (!matchesSearch) return false;

    // Fallback defaults to avoid NaN or null
    const price = result.priceSummary ?? 0;
    const days = result.noOfDays ?? 0;
    const type = result.packageType ?? '';
    const theme = result.packageTheme ?? '';

    // 2. Price filter
    if (price < filterState.priceRange.min || price > filterState.priceRange.max) {
      return false;
    }

    // 3. Duration filter
    if (
      filterState.duration.length > 0 &&
      !filterState.duration.some(dur => {
        if (dur === '1-3-days') return days >= 1 && days <= 3;
        if (dur === '4-6-days') return days >= 4 && days <= 6;
        if (dur === '7-plus-days') return days >= 7;
        return false;
      })
    ) {
      return false;
    }

    // 4. Type filter
    if (filterState.tourType.length > 0 && !filterState.tourType.includes(type)) {
      return false;
    }

    // 5. Theme filter
    if (filterState.theme.length > 0 && !filterState.theme.includes(theme)) {
      return false;
    }

    return true;
  })
  .sort((a, b) => {
    if (sortOption === 'price') {
      return (a.priceSummary ?? 0) - (b.priceSummary ?? 0);
    } else if (sortOption === 'duration') {
      return (a.noOfDays ?? 0) - (b.noOfDays ?? 0);
    }
    return 0;
  });

  // Handle price range change
  const handlePriceChange = (type: 'min' | 'max', value: number) => {
    setPriceRange(prev => ({
      ...prev,
      [type]: value
    }));
  };

  // Calculate total active filters
  const totalActiveFilters = Object.entries(filterState).reduce((total, [key, value]) => {
    if (key === 'priceRange') {
      return total + (value.min !== 65000 || value.max !== 400000 ? 1 : 0);
    }
    return total + (Array.isArray(value) ? value.length : 0);
  }, 0);

  const handleFilterSelect = (category: keyof FilterState, value: string) => {
    if (category === 'priceRange') return;
  
    setSelectedFilters(prev => {
      const current = prev[category] as string[];
      const newFilters = current.includes(value)
        ? current.filter(f => f !== value)
        : [...current, value];
  
      return {
        ...prev,
        [category]: newFilters
      };
    });
  };
  
  // Reset filter modal selections as well
  const resetFilters = () => {
    setPriceRange({ min: 65000, max: 400000 });
    setSelectedFilters({
      tourType: [],
      theme: [],
      priceRange: { min: 65000, max: 400000 },
      duration: []
    });
  };
  

  // Apply filters and close modal
  const applyFilters = () => {
    setFilterState({
      tourType: selectedFilters.tourType,
      theme: selectedFilters.theme,
      duration: selectedFilters.duration,
      priceRange: priceRange
    });
    setModalOpen(false);
  };
  // Handle search input change
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearchQuery(e.target.value);
  };

  // Handle sort option change
  const handleSort = (e: React.ChangeEvent<HTMLSelectElement>) => setSortOption(e.target.value);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center text-red-600">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (!searchQuery && searchResults.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center text-gray-600">
          <p>Please enter a search query to find destinations</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 container mx-auto max-w-7xl">
      {/* Keep the title constant */}
      

      {/* Filter and Search Bar */}
      <div className="flex justify-between mb-4 items-center">
        {totalActiveFilters === 0 ? (
          <div className="flex items-center">
            <div className="relative">
              <button
                onClick={() => setModalOpen(true)}
                className="px-4 py-2 border rounded-full border-gray-400 text-black flex items-center"
              >
                <i className="fas fa-filter mr-2 text-black"></i>
                Filter
              </button>
            </div>
          </div>
        ) : (
        <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-2">
              {Object.entries(filterState).map(([category, filters]) =>
                filters.map((filter: string, index: number) => (
                <div
                    key={`${category}-${index}`}
                    className="flex items-center space-x-1 border border-gray-400 px-3 py-1 rounded-full"
                >
                  <span>{filter}</span>
                  <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleFilterSelect(category as keyof FilterState, filter);
                      }}
                      className="text-black"
                  >
                    <i className="fas fa-times"></i>
                  </button>
                </div>
                ))
              )}
            </div>
            </div>
          )}

        <div className="flex items-center space-x-4">
          {totalActiveFilters > 0 && (
            <div className="relative">
          <button
                onClick={() => setModalOpen(true)}
                className="px-4 py-2 border rounded-full border-blue-600 text-blue-600 flex items-center"
          >
                <i className="fas fa-filter mr-2 text-blue-600"></i>
            Filter
                <div className="absolute -top-1 -right-1 bg-blue-100 border border-blue-600 text-blue-600 text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {totalActiveFilters}
                </div>
          </button>
            </div>
          )}

          {/* Search Bar */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search Destinations"
              value={localSearchQuery}
              onChange={handleSearch}
              className="px-4 py-2 pl-10 border rounded-full w-64 bg-white focus:outline-none text-black placeholder:text-gray-400"
            />
            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-black"></i>
          </div>

          {/* Sort Dropdown */}
          <div className="relative">
            <select
              value={sortOption}
              onChange={handleSort}
              className="px-4 py-2 pr-8 border rounded-full w-48 bg-white focus:outline-none appearance-none text-black"
            >
              <option value="">Sort By</option>
              <option value="price">Price</option>
              <option value="duration">Duration</option>
            </select>
            <i className="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-black"></i>
          </div>
        </div>
      </div>

      {/* Table of Destinations */}
      <div className="border-t pt-4">
        <div className="space-y-6">
          {filteredResults.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-16 h-16 text-gray-300 mb-4">
                <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
              </svg>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">No results found</h3>
              <p className="text-gray-500 text-center max-w-md">
                We could not find any destinations matching `{localSearchQuery}`. Try adjusting your search terms or filters.
              </p>
            </div>
          ) : (
            filteredResults.map((result) => (
              <div key={result.id} className="bg-white rounded-2xl overflow-hidden">
                <div className="flex">
                  {/* Left side - Image */}
                  <div className="w-1/3">
                    {result.packageMainImage ? (
                      <img
                        src={result.packageMainImage}
                        alt={result.packageName || 'Tour package image'}
                        className="w-full h-64 object-cover rounded-2xl"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-100 flex items-center justify-center rounded-2xl">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-12 h-12 text-gray-400">
                          <path strokeLinecap="round" strokeLinejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
                        </svg>
                      </div>
                    )}
                  </div>

                  {/* Right side - Content */}
                  <div className="w-2/3 p-6 pt-1">
                    {/* Top Section */}
                    <div className="flex justify-between items-start">
                      {/* Left content */}
                      <div className="flex-1">
                        {/* Package Attributes */}
                        <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
                          {[result.packageType, result.packageTheme, result.region]
                            .filter(Boolean)
                            .map((attribute, index, array) => (
                              <span key={index} className="flex items-center">
                                {attribute}
                                {index < array.length - 1 && <span className="mx-2">•</span>}
                        </span>
                      ))}
                    </div>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-2 mb-2">
                          {result.tags?.flatMap(tag => 
                            tag.split(',').map((subTag, subIndex) => (
                              <span 
                                key={`${tag}-${subIndex}`} 
                                className="text-sm text-gray-500 bg-gray-50 px-3 py-1 rounded-full"
                              >
                                {subTag.trim()}
                              </span>
                            ))
                          )}
                        </div>

                        {/* Title */}
                        <h2 className="text-2xl font-semibold text-blue-600 mb-4">
                          {result.packageTitle || 'Unnamed Package'}
                        </h2>

                        {/* Country Flags */}
                        <div className="flex flex-wrap gap-2 mb-4">
                          {Array.isArray(result.country) ? (
                            result.country.flatMap((country: string) => 
                              country.split(',').map((singleCountry: string, index: number) => (
                                <div key={`${country}-${index}`} className="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full">
                                  <span className="text-sm">{countryFlags[singleCountry.trim()] || ''}</span>
                                  <span className="text-sm text-black">{singleCountry.trim()}</span>
                                </div>
                              ))
                            )
                          ) : result.country ? (
                            result.country.split(',').map((singleCountry: string, index: number) => (
                              <div key={index} className="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full">
                                <span className="text-sm">{countryFlags[singleCountry.trim()] || ''}</span>
                                <span className="text-sm text-black">{singleCountry.trim()}</span>
                    </div>
                            ))
                          ) : null}
                        </div>
                      </div>

                      {/* Right content - Price and Duration */}
                      <div className="text-right mt-20">
                        <div className="flex items-baseline gap-1">
                          <span className="text-2xl text-black font-bold">₹ {Math.round(result.priceSummary as any)?.toLocaleString() || 'Price not available'}</span>
                          <span className="text-sm text-black">per person</span>
                        </div>
                        <p className="text-sm text-black pt-2">
                          {result.noOfDays && result.noOfNights ? (
                            <>
                              <span className="font-bold">{result.noOfDays}</span> Days{' '}
                              <span className="font-bold">{result.noOfNights}</span> Nights
                            </>
                          ) : 'Duration not specified'}
                        </p>
                      </div>
                      </div>

                    {/* Bottom Section */}
                    <div className="flex justify-between items-end mt-6">
                      {/* What's included */}
                      <div>
                        <p className="text-sm text-black mb-2">What is included</p>
                        <div className="flex gap-6">
                          <div className="flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-gray-500">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z" />
                            </svg>
                            {/* <span className="text-sm text-black">Hotels <span className="font-bold">{result.hotels || 0}</span></span> */}
                            <span className="text-sm text-black">Hotels <span className="font-bold"></span></span>
                          </div>
                          <div className="flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-gray-500">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12" />
                            </svg>
                            {/* <span className="text-sm text-black">Transfers <span className="font-bold">{result.transfers || 0}</span></span> */}
                            <span className="text-sm text-black">Transfers <span className="font-bold"></span></span>
                          </div>
                          <div className="flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-gray-500">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                            </svg>
                            {/* <span className="text-sm text-black">Activities <span className="font-bold">{result.activities || 0}</span></span> */}
                            <span className="text-sm text-black">Activities <span className="font-bold"></span></span>
                          </div>
                        </div>
                      </div>

                      {/* Explore Now Button */}
                      <Link 
                        href={`/destination/package/${result.id}`}
                        className="bg-blue-600 text-white px-8 py-3 rounded-full hover:bg-blue-700 transition"
                      >
                        Explore Now
                      </Link>
                    </div>
                  </div>
                    </div>
                    </div>
              ))
          )}
                  </div>
      </div>

      {/* Filter Modal */}
      {modalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white rounded-[20px] shadow-lg w-[600px] relative overflow-hidden">
            {/* Close button */}
            <button 
              onClick={() => setModalOpen(false)}
              className="absolute right-4 top-4 text-gray-400 hover:text-gray-600"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>

            {/* Header */}
            <div className="bg-blue-50 p-6">
              <h3 className="text-xl font-semibold text-black">Filters</h3>
              <p className="text-sm text-gray-500 mt-1">Compare tours based on different filters.</p>
            </div>

            <div className="flex">
              {/* Left Side - Categories */}
              <div className="w-48 border-r border-gray-200">
                <button
                  onClick={() => setActiveCategory('price')}
                  className={`w-full text-left px-6 py-4 text-sm font-medium cursor-pointer ${
                    activeCategory === 'price' ? 'text-blue-600' : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Price per person {filterState.priceRange.min !== 65000 || filterState.priceRange.max !== 400000 && <span className="text-blue-600">({totalActiveFilters - 1})</span>}
                </button>
                <button
                  onClick={() => setActiveCategory('duration')}
                  className={`w-full text-left px-6 py-4 text-sm font-medium ${
                    activeCategory === 'duration' ? 'text-blue-600' : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Tour Duration {filterState.duration.length > 0 && <span className="text-blue-600">({filterState.duration.length})</span>}
                </button>
                <button
                  onClick={() => setActiveCategory('tourType')}
                  className={`w-full text-left px-6 py-4 text-sm font-medium ${
                    activeCategory === 'tourType' ? 'text-blue-600' : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Tour Type {filterState.tourType.length > 0 && <span className="text-blue-600">({filterState.tourType.length})</span>}
                </button>
                <button
                  onClick={() => setActiveCategory('theme')}
                  className={`w-full text-left px-6 py-4 text-sm font-medium ${
                    activeCategory === 'theme' ? 'text-blue-600' : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Theme {filterState.theme.length > 0 && <span className="text-blue-600">({filterState.theme.length})</span>}
                </button>
              </div>

              {/* Right Side - Content */}
              <div className="flex-1 p-6 max-h-[60vh] overflow-y-auto">
                {activeCategory === 'price' && (
                  <div>
                    <div className="mb-6">
                      <p className="text-sm text-gray-600 mb-2">Selected range</p>
                      <p className="text-black text-lg">
                        ₹ {priceRange.min.toLocaleString('en-IN')} - ₹ {priceRange.max.toLocaleString('en-IN')}
                      </p>
                    </div>

                    {/* Price Range Slider */}
                    <div className="relative pt-6">
                      {/* Bar Chart Background */}
                      <div className="h-24 flex items-end space-x-1 mb-2">
                        {Array.from({ length: 20 }).map((_, index) => (
                          <div
                            key={index}
                            className="flex-1 bg-gray-100 rounded-t"
                            style={{ height: `${Math.random() * 100}%` }}
                          ></div>
                        ))}
                      </div>

                      {/* Range Slider */}
                      <div className="relative h-2">
                        {/* Track Background */}
                        <div className="absolute w-full h-full bg-gray-200 rounded-full"></div>
                        
                        {/* Active Track */}
                        <div
                          className="absolute h-full bg-blue-600 rounded-full"
                          style={{
                            left: `${((priceRange.min - 65000) / (400000 - 65000)) * 100}%`,
                            width: `${((priceRange.max - priceRange.min) / (400000 - 65000)) * 100}%`
                          }}
                        ></div>

                        {/* Min Range Input */}
                        <div className="absolute inset-0">
                          <input
                            type="range"
                            min="65000"
                            max="400000"
                            step="1000"
                            value={priceRange.min}
                            onChange={(e) => {
                              const value = Number(e.target.value);
                              if (value < priceRange.max - 1000) {
                                handlePriceChange('min', value);
                              }
                            }}
                            className="absolute w-full h-8 -top-3 appearance-none cursor-pointer"
                            style={{
                              zIndex: 30
                            }}
                          />
                        </div>

                        {/* Max Range Input */}
                        <div className="absolute inset-0">
                          <input
                            type="range"
                            min="65000"
                            max="400000"
                            step="1000"
                            value={priceRange.max}
                            onChange={(e) => {
                              const value = Number(e.target.value);
                              if (value > priceRange.min + 1000) {
                                handlePriceChange('max', value);
                              }
                            }}
                            className="absolute w-full h-8 -top-3 appearance-none cursor-pointer"
                            style={{
                              zIndex: 40
                            }}
                          />
                        </div>

                        {/* Slider Thumbs */}
                        <div
                          className="absolute w-4 h-4 bg-white border-2 border-blue-600 rounded-full top-1/2 -mt-2 pointer-events-none"
                          style={{
                            left: `${((priceRange.min - 65000) / (400000 - 65000)) * 100}%`,
                            zIndex: 35
                          }}
                        ></div>
                        <div
                          className="absolute w-4 h-4 bg-white border-2 border-blue-600 rounded-full top-1/2 -mt-2 pointer-events-none"
                          style={{
                            left: `${((priceRange.max - 65000) / (400000 - 65000)) * 100}%`,
                            zIndex: 45
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                )}

                {activeCategory === 'duration' && (
                  <div>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={selectedFilters.duration.includes('1-3-days')}
                          onChange={() => handleFilterSelect('duration', '1-3-days')}
                          className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                        />
                        <span className="text-black">1-3 Days</span>
                      </label>
                      <label className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={selectedFilters.duration.includes('4-6-days')}
                          onChange={() => handleFilterSelect('duration', '4-6-days')}
                          className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                        />
                        <span className="text-black">4-6 Days</span>
                      </label>
                      <label className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={selectedFilters.duration.includes('7-plus-days')}
                          onChange={() => handleFilterSelect('duration', '7-plus-days')}
                          className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                        />
                        <span className="text-black">7+ Days</span>
                      </label>
                    </div>
                  </div>
                )}

                {activeCategory === 'tourType' && (
                  <div>
                    <div className="space-y-2">
                      {['Adventure', 'Culture', 'History',"Religious"].map((type) => (
                        <label key={type} className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={selectedFilters.tourType.includes(type)}
                            onChange={() => handleFilterSelect('tourType', type)}
                            className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                          />
                          <span className="text-black">{type}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}

                {activeCategory === 'theme' && (
                  <div>
                    <div className="space-y-2">
                      {['Budget', 'City Breaks', 'Hill Retreat', 'Honeymoon', 'Exclusive Switzerland',"Family"].map((theme) => (
                        <label key={theme} className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={selectedFilters.theme.includes(theme)}
                            onChange={() => handleFilterSelect('theme', theme)}
                            className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                          />
                          <span className="text-black">{theme}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Footer Actions */}
            <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={resetFilters}
                className="px-6 py-2 bg-transparent border border-gray-300 rounded-full text-gray-600 hover:bg-gray-50 font-medium"
              >
                Reset Filters
            </button>
              <button
                onClick={applyFilters}
                className="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 font-medium"
              >
                Apply Filter
            </button>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        input[type='range'] {
          -webkit-appearance: none;
          background: transparent;
          pointer-events: auto;
        }
        input[type='range']::-webkit-slider-thumb {
          -webkit-appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: transparent;
          cursor: pointer;
          pointer-events: auto;
        }
        input[type='range']::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: transparent;
          cursor: pointer;
          border: none;
          pointer-events: auto;
        }
      `}</style>
    </div>
  );
};

export default DestinationTable;
