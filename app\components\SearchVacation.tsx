"use client";

import React, { useState } from "react";
import { useRouter } from 'next/navigation';
import { useQueryStore } from "../store/queryStore";

const SearchSection = () => {
  const setQuery = useQueryStore((state) => state.setQuery);
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();

  const handleSearch = () => {
    if (searchQuery.trim()) {
      setQuery(encodeURIComponent(searchQuery.trim()));
      router.push(`/vacation/tours?query=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="flex flex-col justify-start items-center px-4 sm:px-6 md:px-8 lg:px-12">
      {/* Text Section */}
      <div className="text-center mb-8 mt-4">
        <h1 className="text-black font-medium text-[40px]">
          Let us take you to
        </h1>
        <h2 className="sm:text-4xl md:text-5xl lg:text-6xl text-gray-800 font-extrabold text-6xl">
          your dream vacation
        </h2>
      </div>

      {/* Input Field and Search Now Button in Same Box */}
      <div className="p-2 border border-gray-300 bg-white rounded-full flex items-center w-[526px] mt-[20px]">
        {/* Input Field */}
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyPress={handleKeyPress}
          className="w-full px-4 py-2 rounded-l-full focus:outline-none bg-white placeholder:text-base placeholder:font-normal placeholder:text-[#667085]"
          placeholder="Where are you travelling next?"
        />
        {/* Search Now Button */}
        <button
          onClick={handleSearch}
          className="px-6 py-2 text-white bg-[rgba(21,112,239,1)] rounded-full hover:bg-blue-700 whitespace-nowrap text-sm font-semibold h-[44px] w-[120px]"
        >
          Search Now
        </button>
      </div>
    </div>
  );
};

export default SearchSection;
