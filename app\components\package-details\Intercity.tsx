// https://stage-api.wiseyatra.com/api/packages/WYT00541
import { findDisplayInFromObject, getValueFromPath } from "@/app/utility/display_in_tracker";
import React from "react";

interface TransportDetails {
  transferMode: string;
  groupSize: string;
  finalPrice: string;
  description: string;
  departureTime: string;
  arrivalTime: string;
  originatingPoint: string;
  destinationPoint: string;
}

interface SelectedExcursion {
  titleDescriptor: string;
  transportDetails: TransportDetails[];
  cityName: string;
  destinationCityName: string;
}

interface IntercityExcursionData {
  sortNo: number;
  subCode: string;
  subCodeDescriptor: string;
  price: string;
  noOfNightsBooked: number;
  desc: string;
  selected_excursion: SelectedExcursion;
}

interface Props {
  data: IntercityExcursionData;
}

const IntercityExcursionCard: React.FC<any> = ({ data,itinerary,itineraryType="website" }) => {
  const {
    sortNo,
    subCode,
    subCodeDescriptor,
    price,
    noOfNightsBooked,
    desc,
    selected_excursion,
  } = data;

  const transport = selected_excursion.transportDetails[0];
  debugger;
const display_in_tracker  = findDisplayInFromObject(data);
  return (
    
		<div className="space-y-4">
      <div className="text-xs font-normal text-black space-y-2">
        <p><strong>From:</strong> {selected_excursion.cityName}</p>
        <p><strong>To:</strong> {selected_excursion.destinationCityName}</p>
 			 <p><strong>Mode:</strong> {transport.transferMode}</p>
 			 <p><strong>Duration:</strong> {transport.groupSize}</p>
 			 {/* <p><strong>Price:</strong> {price}</p>
 			 <p><strong>Nights Booked:</strong> {noOfNightsBooked}</p> */}

      </div>
			<div className=" text-sm text-[#344054] font-medium" style={{ marginTop: 12 }}>
        <strong>Instructions:</strong>
        <div className="itinerary-content"
          dangerouslySetInnerHTML={{
            __html: transport.description.replace(/\\r\\n/g, "").replace(/\\n/g, ""),
          }}
        />
      </div>
     {
        display_in_tracker.length > 0 &&
        display_in_tracker.map((element, index) => {
          const key = element.path.split(".").at(-1).replace(/displayin$/i, "");
          const value = getValueFromPath(itinerary, element.path);

          if (!value) return null; // 🟡 skip if value is empty

          return (
            (element.key.toLowerCase() !== "displayin" &&
              element.value
                .map((itr: any) => itr.toLowerCase().split(" ")[0])
                .includes(itineraryType.toLowerCase())) && (
              <div
                key={index}
                className="bg-white border border-gray-200 rounded-lg shadow-md p-4 mb-4 mt-6"
              >
                <p className="text-sm font-semibold text-gray-800 mb-2">
                  {key
                    .replace(/([A-Z])/g, " $1")
                    .replace(/^./, (str: any) => str.toUpperCase())}
                </p>

                {key.toLowerCase().includes("link") ? (
                  key.toLowerCase().includes("youtube") ? (
                    <div className="w-full aspect-video">
                      <iframe
                        className="w-full h-full rounded-md"
                        src={`https://www.youtube.com/embed/${value.split("v=")[1]}`}
                        frameBorder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                      />
                    </div>
                  ) : (
                    <a
                      href={value}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 underline break-words"
                    >
                      {value}
                    </a>
                  )
                ) : (
                  <div
                    className="itinerary-content text-sm text-gray-700 leading-relaxed"
                    dangerouslySetInnerHTML={{ __html: value }}
                  />
                )}
              </div>
            )
          );
        })
      }
          {typeof window !== 'undefined' && localStorage.getItem('wy_user_data') && itineraryType!=='website' &&
            (() => {
              try {
                const role = JSON.parse(localStorage.getItem('wy_user_data') || '{}')?.role?.toLowerCase();

                // Normalize the role to category
                const isAdmin = role === 'admin' || role === 'subadmin';
                const isCustomer = role === 'customer';
                const isAgent = !isCustomer && !isAdmin; // All others treated as agent

                // Collect keys to show
                const noteKeys: (keyof typeof itinerary)[] = [];
                if (isCustomer) {
                  noteKeys.push('customerNote');
                }
                if (isAgent) {
                  noteKeys.push('customerNote', 'agentNote');
                }
                if (isAdmin) {
                  noteKeys.push('customerNote', 'agentNote', 'adminNote', 'subAdminNote');
                }

                // Render notes
                return (
                  <>
                    {noteKeys.map((key:any) =>
                      itinerary?.[key] ? (
                        <div key={key} className="bg-white border border-gray-200 rounded-lg shadow-md p-4 mb-4 mt-6">
                          <p className="text-sm font-semibold text-gray-800 mb-2">Note ({key})</p>
                          <div
                            className="itinerary-content text-sm text-gray-700 leading-relaxed"
                            dangerouslySetInnerHTML={{ __html: itinerary[key] as string }}
                          />
                        </div>
                      ) : null
                    )}
                  </>
                );
              } catch {
                return null;
              }
            })()
          }
    </div>
  );
};

export default IntercityExcursionCard;
