"use client";

import { findDisplayInFromObject, getValueFromPath } from "@/app/utility/display_in_tracker";

interface TransferDetailsProps {
  itinerary: any; // Using any for simplicity, but should be properly typed
  itineraryType?:string;
}

const TransferDetails: React.FC<TransferDetailsProps> = ({ itinerary ,itineraryType="website"}) => {
  debugger;
    const display_in_tracker  = findDisplayInFromObject(itinerary);
  return (
    <div className="space-y-4">
      <div dangerouslySetInnerHTML={{ __html: itinerary?.desc }} className='itinerary-content text-xs font-normal text-black space-y-2'>
      </div>
      <div className="flex items-center gap-6 text-sm text-[#344054] font-medium">
        <div className="flex items-center gap-2">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 20.25H22.5M2.25 9.72458V4.79053C2.25 4.67169 2.27824 4.55455 2.33239 4.44877C2.38655 4.34298 2.46506 4.25158 2.56147 4.1821C2.65787 4.11261 2.76941 4.06703 2.88689 4.04911C3.00437 4.03118 3.12443 4.04143 3.23717 4.07902L4.5 4.49996L6 7.31246L9 8.24996V4.79053C9 4.67169 9.02824 4.55455 9.08239 4.44877C9.13655 4.34298 9.21506 4.25158 9.31147 4.1821C9.40787 4.11261 9.51941 4.06703 9.63689 4.04911C9.75437 4.03118 9.87443 4.04143 9.98717 4.07902L11.25 4.49996L14.25 9.74996L18.8029 11.0147C19.4347 11.1902 19.9916 11.5678 20.3884 12.0898C20.7852 12.6119 21 13.2495 21 13.9052V17.25L4.44111 12.6135C3.81071 12.437 3.25535 12.0591 2.85974 11.5375C2.46414 11.0159 2.25 10.3792 2.25 9.72458Z" stroke="#344054" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <span className='text-[#344054] text-sm font-medium'>{itinerary.selected_excursion.airportName} Airport</span>
        </div>
        <div className="flex items-center gap-2 font-medium">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 17H21C21.2652 17 21.5196 16.8946 21.7071 16.7071C21.8946 16.5196 22 16.2652 22 16V12.631C22 12.4296 21.9393 12.2328 21.8256 12.0665C21.7119 11.9002 21.5507 11.7721 21.363 11.699L17 10L14 5H6L4 10H3C2.73478 10 2.48043 10.1054 2.29289 10.2929C2.10536 10.4804 2 10.7348 2 11V16C2 16.2652 2.10536 16.5196 2.29289 16.7071C2.48043 16.8946 2.73478 17 3 17H6" stroke="#344054" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M16 19C16.5304 19 17.0391 18.7893 17.4142 18.4142C17.7893 18.0391 18 17.5304 18 17C18 16.4696 17.7893 15.9609 17.4142 15.5858C17.0391 15.2107 16.5304 15 16 15C15.4696 15 14.9609 15.2107 14.5858 15.5858C14.2107 15.9609 14 16.4696 14 17C14 17.5304 14.2107 18.0391 14.5858 18.4142C14.9609 18.7893 15.4696 19 16 19Z" stroke="#344054" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8 19C7.46957 19 6.96086 18.7893 6.58579 18.4142C6.21071 18.0391 6 17.5304 6 17C6 16.4696 6.21071 15.9609 6.58579 15.5858C6.96086 15.2107 7.46957 15 8 15C8.53043 15 9.03914 15.2107 9.41421 15.5858C9.78929 15.9609 10 16.4696 10 17C10 17.5304 9.78929 18.0391 9.41421 18.4142C9.03914 18.7893 8.53043 19 8 19Z" stroke="#344054" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M14 17H10" stroke="#344054" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <span className='text-[#344054] text-sm font-medium'>{itinerary.selected_excursion.transportDetails?.[0]?.carType || 'Private Cab'}</span>
        </div>
      </div>
     {display_in_tracker.length > 0 &&
        display_in_tracker.map((element, index) => {
          const key = element.path.split(".").at(-1).replace(/displayin$/i, "");
          const value = getValueFromPath(itinerary, element.path);

          // Special condition for meetingPoint
          if (key.toLowerCase() !== "meetingpoint" && !value) return null;

          if (key.toLowerCase() === "meetingpoint") {
            const basePath = element.path.split(".").slice(0, -1).join(".");
            const lat = getValueFromPath(itinerary, `${basePath}.meetingPointLocationLatitude`);
            const lng = getValueFromPath(itinerary, `${basePath}.meetingPointLocationLongitude`);
            if (!lat || !lng) return null;
          }

          const shouldRender =
            element.key.toLowerCase() !== "displayin" &&
            element.value
              .map((itr: any) => itr.toLowerCase().split(" ")[0])
              .includes(itineraryType.toLowerCase());

          if (!shouldRender) return null;

          const formattedLabel = key
            .replace(/([A-Z])/g, " $1")
            .replace(/^./, (str: any) => str.toUpperCase());

          const showMapLink =
            key.toLowerCase() === "meetingpoint" &&
            itineraryType.toLowerCase() === "final";

          const basePath = element.path.split(".").slice(0, -1).join(".");
          const lat = getValueFromPath(itinerary, `${basePath}.meetingPointLocationLatitude`);
          const lng = getValueFromPath(itinerary, `${basePath}.meetingPointLocationLongitude`);

          return (
            <div
              key={index}
              className="bg-white border border-gray-200 rounded-lg shadow-md p-4 mb-4"
            >
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-2">
                <h3 className="text-sm font-semibold text-gray-800">
                  {formattedLabel}
                </h3>

                {showMapLink && lat && lng && (
                  <a
                    href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(`${lat},${lng}`)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-blue-600 hover:text-blue-800 underline font-medium mt-2 sm:mt-0"
                  >
                    Get Directions
                  </a>
                )}
              </div>

              {key.toLowerCase().includes("link") ? (
                key.toLowerCase().includes("youtube") ? (
                  <div className="w-full aspect-video">
                    <iframe
                      className="w-full h-full rounded-md"
                      src={`https://www.youtube.com/embed/${value.split("v=")[1]}`}
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    />
                  </div>
                ) : (
                  <a
                    href={value}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 underline break-words"
                  >
                    {value}
                  </a>
                )
              ) : (
                <p
                  className="text-sm text-gray-700 leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: value }}
                />
              )}
            </div>
          );
        })}


           {typeof window !== 'undefined' && localStorage.getItem('wy_user_data') && itineraryType!=='website' &&
            (() => {
              try {
                const role = JSON.parse(localStorage.getItem('wy_user_data') || '{}')?.role?.toLowerCase();

                // Normalize the role to category
                const isAdmin = role === 'admin' || role === 'subadmin';
                const isCustomer = role === 'customer';
                const isAgent = !isCustomer && !isAdmin; // All others treated as agent

                // Collect keys to show
                const noteKeys: (keyof typeof itinerary)[] = [];
                if (isCustomer) {
                  noteKeys.push('customerNote');
                }
                if (isAgent) {
                  noteKeys.push('customerNote', 'agentNote');
                }
                if (isAdmin) {
                  noteKeys.push('customerNote', 'agentNote', 'adminNote', 'subAdminNote');
                }

                // Render notes
                return (
                  <>
                    {noteKeys.map((key:any) =>
                      itinerary?.[key] ? (
                        <div key={key} className="bg-white border border-gray-200 rounded-lg shadow-md p-4 mb-4 mt-6">
                          <p className="text-sm font-semibold text-gray-800 mb-2">Note ({key})</p>
                          <div
                            className="itinerary-content text-sm text-gray-700 leading-relaxed"
                            dangerouslySetInnerHTML={{ __html: itinerary[key] as string }}
                          />
                        </div>
                      ) : null
                    )}
                  </>
                );
              } catch {
                return null;
              }
            })()
          }

    </div>
  );
};

export default TransferDetails;
