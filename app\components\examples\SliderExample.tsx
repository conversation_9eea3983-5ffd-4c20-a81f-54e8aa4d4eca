'use client';

import React, { useState } from 'react';
import DualRangeSlider from '../ui/DualRangeSlider';

const SliderExample: React.FC = () => {
  const [priceRange, setPriceRange] = useState({ min: 50000, max: 300000 });
  const [durationRange, setDurationRange] = useState({ min: 3, max: 15 });

  const formatPrice = (value: number) => `₹${value.toLocaleString('en-IN')}`;
  const formatDuration = (value: number) => `${value} ${value === 1 ? 'Day' : 'Days'}`;

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-8">
      <h1 className="text-2xl font-bold text-gray-800 mb-8">Improved Dual Range Slider Examples</h1>
      
      {/* Price Range Slider */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold text-gray-700 mb-4">Price Range Filter</h2>
        <DualRangeSlider
          min={0}
          max={500000}
          step={5000}
          value={priceRange}
          onChange={setPriceRange}
          formatLabel={formatPrice}
          className="w-full"
        />
        <div className="mt-4 p-3 bg-gray-50 rounded">
          <p className="text-sm text-gray-600">
            Selected: {formatPrice(priceRange.min)} - {formatPrice(priceRange.max)}
          </p>
        </div>
      </div>

      {/* Duration Range Slider */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold text-gray-700 mb-4">Duration Range Filter</h2>
        <DualRangeSlider
          min={1}
          max={30}
          step={1}
          value={durationRange}
          onChange={setDurationRange}
          formatLabel={formatDuration}
          className="w-full"
        />
        <div className="mt-4 p-3 bg-gray-50 rounded">
          <p className="text-sm text-gray-600">
            Selected: {formatDuration(durationRange.min)} - {formatDuration(durationRange.max)}
          </p>
        </div>
      </div>

      {/* Custom Styled Slider */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold text-gray-700 mb-4">Custom Styled Slider</h2>
        <DualRangeSlider
          min={0}
          max={100}
          step={5}
          value={{ min: 20, max: 80 }}
          onChange={(value) => console.log('Custom slider value:', value)}
          formatLabel={(value) => `${value}%`}
          className="w-full"
          trackClassName="bg-gradient-to-r from-green-200 to-green-300"
        />
      </div>

      {/* Features List */}
      <div className="bg-blue-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-800 mb-3">✨ Improvements Made</h3>
        <ul className="space-y-2 text-sm text-blue-700">
          <li>• <strong>Fixed overlapping inputs:</strong> No more interference between min/max sliders</li>
          <li>• <strong>Better touch support:</strong> Improved mobile and tablet interaction</li>
          <li>• <strong>Proper thumb styling:</strong> Consistent visual thumbs across browsers</li>
          <li>• <strong>Smooth animations:</strong> Added hover effects and transitions</li>
          <li>• <strong>Accessibility:</strong> Better keyboard navigation and screen reader support</li>
          <li>• <strong>Reusable component:</strong> Easy to use anywhere in your app</li>
          <li>• <strong>Customizable:</strong> Support for custom formatting, styling, and step values</li>
        </ul>
      </div>
    </div>
  );
};

export default SliderExample;
