"use client";

import { useState } from 'react';
import Image from 'next/image';

interface ImageCarouselProps {
  images: Array<{ filePath: string; DisplayIn?: string[] }>;
  fallbackImage: string;
  altText: string;
  youtubelink:any;
}

const ImageCarousel: React.FC<ImageCarouselProps> = ({ 
  images, 
  fallbackImage,
  altText,
  youtubelink=[]
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  
  const displayImages = (images.length > 0 || youtubelink.length>0) 
    // ? [...youtubelink,...images] 
    ? [...images,...youtubelink] 
    : [{ filePath: fallbackImage }];

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % displayImages.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev === 0 ? displayImages.length - 1 : prev - 1));
  };

  const goToImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  return (
    <div className="relative">
      <div className="relative aspect-[634/357] w-full overflow-hidden rounded-2xl mt-5">
        {displayImages[currentImageIndex]?.youtube_link_embedded?
        <iframe
        className='w-full h-full'
        src={`https://www.youtube.com/embed/${displayImages[currentImageIndex]?.link_id}`}
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen></iframe>
        :
        <Image
          src={displayImages[currentImageIndex].filePath}
          alt={`${altText} image ${currentImageIndex + 1}`}
          fill
          className="object-cover"
          priority
          onError={(e: any) => {
            const target = e.target as HTMLImageElement;
            target.src = fallbackImage;
          }}
        />}
        <button
            onClick={prevImage}
            className="absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full w-6 h-6 flex items-center justify-center transition-colors"
            aria-label="Previous image"
            >
            <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={2.5}
                stroke="currentColor"
                className="w-4 h-4 text-gray-900"
            >
                <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
            </svg>
            </button>

            <button
            onClick={nextImage}
            className="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full w-6 h-6 flex items-center justify-center transition-colors"
            aria-label="Next image"
            >
            <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={2.5}
                stroke="currentColor"
                className="w-4 h-4 text-gray-900"
            >
                <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
            </svg>
            </button>

        <div className="absolute bottom-4 left-0 right-0">
          <div className="flex justify-center space-x-1.5 sm:space-x-2">
            {displayImages.map((_, index) => (
              <button
                key={index}
                onClick={() => goToImage(index)}
                className={`w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full transition-colors ${
                  currentImageIndex === index ? 'bg-white' : 'bg-white/50 hover:bg-white/80'
                }`}
                aria-label={`Go to image ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageCarousel;
