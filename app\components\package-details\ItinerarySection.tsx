"use client";

import { useEffect, useState } from 'react';
import { PackageDetails } from '@/app/types/PackageDetails';
import HotelDetails from './HotelDetails';
import ActivityDetails from './ActivityDetails';
import TransferDetails from './TransferDetails';
import Intercity from './Intercity';
import { addDays, format, parseISO } from 'date-fns';
import InformationDetails from './InformationDetails';

interface ItinerarySectionProps {
  packageData: PackageDetails;
  from_download_pdf?: boolean;
}

const ItinerarySection: React.FC<ItinerarySectionProps> = ({ packageData,from_download_pdf=false }) => {
  const [expandedDays, setExpandedDays] = useState<number[]>([]);
  const [activeTab, setActiveTab] = useState('summary');
  const [expandedDetails, setExpandedDetails] = useState<string[]>([]);

  const handleItinaryAllAccordianView = ()=>{
    if(expandedDays.length == packageData.days.length){
      setExpandedDays([]);
    } else{
      const days_number = packageData.days.map((day)=>{
        return day.day_number;
      })
      setExpandedDays(days_number);
    }
  }
  
  const toggleDay = (dayNumber: number) => {
    setExpandedDays(prev =>
      prev.includes(dayNumber)
        ? prev.filter(d => d !== dayNumber)
        : [...prev, dayNumber]
    );
  };

  const tabs = [
    { id: 'summary', label: 'Summary' },
    { id: 'flights', label: 'Flights' },
    { id: 'hotels', label: 'Hotels' },
    { id: 'transfers', label: 'Transfers' },
    { id: 'activities', label: 'Activities' },
    { id: 'information', label: 'Information' }
  ];
  let current_city = null;
  useEffect(()=>{
    if(from_download_pdf){
      const days_number = packageData.days.map((day)=>{
        return day.day_number;
      })
      setExpandedDays(days_number);
      let state_data:any =[];
      [...packageData.days].forEach((day)=>{
          day?.cities?.forEach((city,cityIndex)=>{
              let array_detailKey = city.itineraries.map((itinerary:any, itineraryIndex:any) => {
              const detailKey = `${day.dayId}-${cityIndex}-${itineraryIndex}`;
              return detailKey;
              })
              state_data.push(...array_detailKey);
          })
      })
      setExpandedDetails(prev =>state_data);
    }
  },[])
  return (
    <section id="itinerary" className="space-y-4 mb-8">
      <div className='flex flex-col md:flex-row justify-between items-center'>
        <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Itinerary</h2>
        <button type="button" className="text-sm sm:text-sm font-semibold text-blue-600 cursor-pointer"
        onClick={handleItinaryAllAccordianView}
        >
          {expandedDays.length ==packageData.days.length? "Hide All":"Expand All"}
        </button>
      </div>

      {/* Tabs Section */}
      <div className="overflow-x-auto -mx-4 sm:mx-0 px-4 sm:px-0">
        <div className="flex flex-nowrap gap-2 min-w-max sm:min-w-0 sm:flex-wrap">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => {
                setActiveTab(tab.id);
                setExpandedDays([]);
                setExpandedDetails([]);
              }}
              className={`py-1.5 px-4 sm:px-6 text-sm font-medium rounded-full whitespace-nowrap transition-colors ${activeTab === tab.id
                ? 'bg-blue-50 text-blue-700'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>
      <div className="space-y-4">
        {packageData.days
          // Filter out days that have no content (empty cities or no itineraries)
          .filter(day => {
            // If we're on the summary tab, just check if there are any itineraries
            if (activeTab === 'summary') {
              return day.cities && day.cities.length > 0 && 
                // day.cities.some(city => city.itineraries && city.itineraries.length > 0);
                day.cities.some(city => city.itineraries);
            }
            
            // For other tabs, check if there are itineraries matching the selected tab type
            return day.cities && day.cities.length > 0 && 
              day.cities.some(city => 
                city.itineraries &&
                city.itineraries.some(itinerary => {
                  // if (activeTab === 'flights') return itinerary.excursionType === 'airport';
                  if (activeTab === 'flights') return false;
                  if (activeTab === 'hotels') return itinerary.excursionType.includes('hotel');
                  // if (activeTab === 'transfers') return itinerary.excursionType === 'transfer';
                  if (activeTab === 'transfers') return itinerary.excursionType === 'intercity' || itinerary.excursionType === 'airport';
                  if (activeTab === 'activities') return itinerary.excursionType === 'sightseeing';
                  if (activeTab === 'information') return itinerary.excursionType === 'information';
                  return false;
                })
              );
          })
          .map((day:any,index) => {
             const baseDate = (packageData?.itineraryType?.toLowerCase() == "final" || packageData?.itineraryType?.toLowerCase() == "quotation") ? parseISO(packageData?.packageStartDate as string) :""; // first date
            const adjustedDate =(packageData?.itineraryType?.toLowerCase() == "final" || packageData?.itineraryType?.toLowerCase() == "quotation") ? addDays(baseDate, index) :""; // consecutive days
            debugger;
            let cities =(index > 0 && packageData?.days[index - 1]?.cities) || [];
            let lastCity = cities.length > 0 ? cities[cities.length - 1] : null;
            let itinery = lastCity?.itineraries || [];
            let lastItinerary = itinery.length > 0 ? itinery[itinery.length - 1] : null;
            current_city = lastItinerary?.selected_excursion?.cityName || '';
            
            let current_city_new = day?.cities[0]?.itineraries?.length>0 ? (()=>{
              current_city = day?.cities[0]?.itineraries[0]?.selected_excursion?.cityName;
              return day?.cities[0]?.itineraries[0]?.selected_excursion?.cityName;
            })() :current_city;
            let data_to_show:any = [];
            day?.cities?.forEach((city:any)=>{
              return city.itineraries.map((itinery:any)=>{
                if(!data_to_show.includes(itinery?.selected_excursion?.cityName)){
                  data_to_show.push(itinery?.selected_excursion?.cityName);
                }
                if(itinery?.selected_excursion?.destinationCityName){
                  if(!data_to_show.includes(itinery?.selected_excursion?.destinationCityName)){
                  data_to_show.push(itinery?.selected_excursion?.destinationCityName);
                }
                }
              })
            })
            if(data_to_show?.length ==0){
              data_to_show.push(current_city_new);
            }
          return (
            <>
              {
                data_to_show.length>0 &&
                <div className="flex flex-wrap items-center gap-2 text-gray-700 text-sm font-medium">
                  {
                    data_to_show?.map((ele:any,index_new:any)=>{
                      return(
                        <span key={`${ele}_${index}_${index_new}`}>{index_new>0 && <>&gt;</>}{ele}</span>
                      )
                    })
                  }
                </div>
              }
              <div key={day.dayId} className="bg-blue-50 rounded-xl sm:rounded-2xl sm:hover:rounded-2xl">
                {!expandedDays.includes(day.day_number) && <button
                    onClick={() =>{
                      if(day.cities?.some((ele:any)=>ele.itineraries?.length!==0)){
                        toggleDay(day.day_number);
                      }
                    }}
                    // className={`w-full px-4 sm:px-6 py-3 sm:py-4 flex items-center justify-between text-left hover:bg-blue-50 transition-all duration-300 ${
                    className={`w-full px-4 sm:px-6 py-3 sm:py-4 flex items-center justify-between text-left hover:bg-blue-50 sm:hover:rounded-2xl  ${
                      expandedDays.includes(day.day_number) ? 'bg-white' : ''
                  }`}
                >
                  <div className="flex items-center gap-4">
                      <span className="bg-blue-200 text-black font-bold text-sm px-3 py-1 rounded-xl">
                        Day {day.day_number + 1}{(packageData?.itineraryType?.toLowerCase() == "final" || packageData?.itineraryType?.toLowerCase() == "quotation") && `- ${format(adjustedDate, 'EEEE, MMM dd')}`}: {day.cities.map((x:any)=>{
                          if(x.itineraries.length ==0){
                            return x?.title;
                          }
                          return(
                        x.itineraries.map((y:any)=>{
                          if(
                          (activeTab === "flights" && y.excursionType == "flights") ||
                          (activeTab === "hotels" && y.excursionType.includes('hotel')) ||
                          (activeTab === "activities" && y.excursionType == 'sightseeing') ||
                          (activeTab === "information" && y.excursionType == 'information') ||
                          (activeTab === "transfers" && ( y.excursionType == 'intercity' || y.excursionType == 'airport') )  || 
                          activeTab == "summary"
                        ) 
                          {
                            if(y.excursionType.includes('hotel')){
                              return y.selected_excursion.hotelName;
                            }
                            if(y.excursionType == 'sightseeing'){
                              return y.selected_excursion.sightseeingName;
                            }
                            return y.subCodeDescriptor
                          }
                        })
                          )
                        }
                        ).flat(Infinity).filter((x:any)=>x).join(";")}
                      </span>
                  </div>
                    {day.cities?.some((ele:any)=>ele.itineraries?.length!==0) &&<div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full border-2 border-blue-700 flex items-center justify-center bg-white">
                      {expandedDays.includes(day.day_number) ? (
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 text-blue-700">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M18 12H6" />
                        </svg>
                      ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 text-blue-700">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m6-6H6" />
                      </svg>
                      )}
                    </div>}
                </button>}
                {expandedDays.includes(day.day_number) && (
                  <div className={`transition-all duration-300 ease-in-out ${expandedDays.includes(day.day_number) ? ' opacity-100' : 'max-h-0 opacity-0'
                    }`}>
                    <div className="bg-blue-50 relative rounded-xl outline-none p-[10px] mt-[30px]">
                      {day.cities.map((city:any, cityIndex:any) => {
                        let null_count = 0;
                        return (
                          <div key={cityIndex} className="">
                            <button onClick={() => toggleDay(day.day_number)} className='absolute flex justify-between w-full px-5 top-[-10px]'>
                              <div className="flex items-center gap-4">
                                <span className="bg-blue-200 text-black font-bold text-sm px-3 py-1 rounded-xl">
                                  Day {day.day_number + 1} {(packageData?.itineraryType?.toLowerCase() == "final" || packageData?.itineraryType?.toLowerCase() == "quotation") && `- ${format(parseISO(packageData?.packageStartDate as string), 'EEEE, MMM dd')}`}: {
                                day.cities.map((x:any)=>x.itineraries.map((y:any)=>{
                                if(
                                (activeTab === "flights" && y.excursionType == "flights") ||
                                (activeTab === "hotels" && y.excursionType.includes('hotel')) ||
                                (activeTab === "activities" && y.excursionType == 'sightseeing') ||
                                (activeTab === "information" && y.excursionType == 'information') ||
                                (activeTab === "transfers" && (y.excursionType == 'intercity' || y.excursionType == 'airport') )  || 
                                activeTab == "summary"
                              ) 
                                {
                                  if(y.excursionType.includes('hotel')){
                                    return y.selected_excursion.hotelName
                                  }
                                  if(y.excursionType == 'sightseeing'){
                                    return y.selected_excursion.sightseeingName;
                                  }
                                  return y.subCodeDescriptor
                                }
                              })).flat(Infinity).filter((x:any)=>x).join(";")
                                }
                                </span>
                            </div>
                              <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full border-2 border-blue-700 flex items-center justify-center bg-white">
                                {expandedDays.includes(day.day_number) ? (
                                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 text-blue-700">
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M18 12H6" />
                                  </svg>
                                ) : (
                                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 text-blue-700">
                                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m6-6H6" />
                                </svg>
                                )}
                              </div>
                            </button>
                            {city.itineraries.map((itinerary:any, itineraryIndex:any) => {
                              const detailKey = `${day.dayId}-${cityIndex}-${itineraryIndex}`;
                              const isExpanded = expandedDetails.includes(detailKey);

                              const toggleDetails = () => {
                                setExpandedDetails(prev =>
                                  prev.includes(detailKey)
                                    ? prev.filter(key => key !== detailKey)
                                    : [...prev, detailKey]
                                );
                              };

                              // Remove the 'no result found' message completely - just return null
                              if (
                                (null_count == city.itineraries.length - 1 && itineraryIndex == city.itineraries.length - 1) &&
                                (
                                (activeTab === "flights" && itinerary.excursionType != "flights") ||
                                (activeTab === "hotels" && !itinerary.excursionType.includes('hotel')) ||
                                (activeTab === "activities" && itinerary.excursionType !== 'sightseeing') ||
                                (activeTab === "information" && itinerary.excursionType !== 'information') ||
                                (activeTab === "transfers" && itinerary.excursionType !== 'intercity' && itinerary.excursionType !== 'airport')
                                ) 
                              ) {
                                return null;
                              }

                              if (
                                (activeTab === "flights" && itinerary.excursionType != "flights") ||
                                (activeTab === "hotels" && !itinerary.excursionType.includes('hotel')) ||
                                (activeTab === "activities" && itinerary.excursionType !== 'sightseeing') ||
                                (activeTab === "information" && itinerary.excursionType !== 'information') ||
                                (activeTab === "transfers" && itinerary.excursionType !== 'intercity' && itinerary.excursionType !== 'airport')
                              ) {
                                null_count += 1;
                                return null;
                              }

                              return (
                                <div key={itineraryIndex} className="bg-white m-4 sm:m-6 rounded-xl">
                                  <div className="p-4 sm:p-6">
                                    {!isExpanded ? (
                                      <div className="flex justify-between items-start mb-4">
                                        <h3
                                          className={`text-bases font-bold ${
                                            (itinerary.excursionType === 'sightseeing' || itinerary.excursionType === 'information') ? 'text-[#175CD3] text-[16px] font-bold' : 'text-black'
                                          }`}
                                        >
                                          {itinerary.excursionType === 'airport'
                                            ? itinerary.selected_excursion.titleDescriptor
                                            : itinerary.excursionType.includes('hotel')
                                            ? 'Check-in to hotel'
                                            : itinerary.excursionType === 'sightseeing'
                                            ? 'Activity'
                                            : itinerary.excursionType === 'information'
                                            ? 'Information'
                                            : itinerary.excursionType === 'intercity'
                                            ? itinerary.selected_excursion.titleDescriptor
                                            : ''}
                                        </h3>
                                        <button
                                          onClick={toggleDetails}
                                          className="text-blue-600 hover:text-blue-700 text-xs font-medium whitespace-nowrap ml-4"
                                        >
                                          View Details
                                        </button>
                                      </div>
                                    ) : (
                                      <div className="grid grid-cols-2 gap-4 mb-4">
                                        <h3
                                          className={`text-bases font-bold ${
                                            (itinerary.excursionType === 'sightseeing' || itinerary.excursionType === 'information') ? 'text-[#175CD3] text-[16px] font-bold' : 'text-black'
                                          }`}
                                        >
                                          {itinerary.excursionType === 'airport'
                                            ? itinerary.selected_excursion.titleDescriptor
                                            : itinerary.excursionType.includes('hotel')
                                            ? 'Check-in to hotel'
                                            : itinerary.excursionType === 'sightseeing'
                                            ? 'Activity'
                                            : itinerary.excursionType === 'information'
                                            ? 'Information'
                                            : itinerary.excursionType === 'intercity'
                                            ? itinerary.selected_excursion.titleDescriptor
                                            : ''}
                                        </h3>
                                        <div className="text-right">
                                          <button
                                            onClick={toggleDetails}
                                            className="text-blue-600 hover:text-blue-700 text-xs font-medium whitespace-nowrap"
                                          >
                                            Hide Details
                                          </button>
                                        </div>
                                      </div>
                                    )}
                                    {/* As when view detail is noyt expanded then too we can see some detils view */}
                                    {!isExpanded && 
                                      <div className={``}>
                                      <div className={`transition-all duration-300`}>
                                        {/* {itinerary.excursionType === 'airport' && (
                                          <TransferDetails itinerary={itinerary} />
                                        )} */}
                                        {itinerary.excursionType.includes("hotel") && (
                                          <HotelDetails
                                            itinerary={itinerary}
                                            dayId={day.dayId}
                                            cityIndex={cityIndex}
                                            itineraryIndex={itineraryIndex}
                                            isExpanded={false}
                                            itineraryType= {packageData?.itineraryType ? packageData?.itineraryType : "website" }
                                          />
                                        )}
                                        {itinerary.excursionType === 'sightseeing' && (
                                          <ActivityDetails from_download_pdf={from_download_pdf} itinerary={itinerary} isExpanded={false} itineraryType= {packageData?.itineraryType ? packageData?.itineraryType : "website" } />
                                        )}
                                        {itinerary.excursionType === 'information' && (
                                          <InformationDetails from_download_pdf={from_download_pdf} itinerary={itinerary} isExpanded={false} itineraryType= {packageData?.itineraryType ? packageData?.itineraryType : "website" } />
                                        )}
                                      </div>
  {/* 
                                      {!isExpanded && (
                                        <div className="absolute bottom-0 left-0 right-0 h-12 bg-gradient-to-t from-white to-transparent" />
                                      )} */}
                                    </div>
                                    }
                                    {/*======================================================================*/}

                                    <div className={`relative ${!isExpanded ? 'max-h-24 overflow-hidden' : ''}`}>
                                      <div className={`transition-all duration-300 ${isExpanded ? 'opacity-100' : 'opacity-0 h-0'}`}>
                                        {/* Transfer details */}
                                        {(itinerary.excursionType === 'airport') && (
                                          <TransferDetails itinerary={itinerary}itineraryType= {packageData?.itineraryType ? packageData?.itineraryType : "website" } />
                                        )}

                                        {/* Hotel details */}
                                        {itinerary.excursionType.includes("hotel") && (
                                          <HotelDetails
                                            itinerary={itinerary}
                                            dayId={day.dayId}
                                            cityIndex={cityIndex}
                                            itineraryIndex={itineraryIndex}
                                            itineraryType= {packageData?.itineraryType ? packageData?.itineraryType : "website" }
                                          />
                                        )}

                                        {/* Activity details */}
                                        {itinerary.excursionType === 'sightseeing' && (
                                          <ActivityDetails from_download_pdf={from_download_pdf} itinerary={itinerary} itineraryType= {packageData?.itineraryType ? packageData?.itineraryType : "website" }/>
                                        )}
                                        {itinerary.excursionType === 'information' && (
                                          <InformationDetails from_download_pdf={from_download_pdf} itinerary={itinerary} itineraryType= {packageData?.itineraryType ? packageData?.itineraryType : "website" }/>
                                        )}
                                        {itinerary.excursionType === 'intercity' && (
                                          <Intercity data={itinerary} itinerary={itinerary} itineraryType= {packageData?.itineraryType ? packageData?.itineraryType : "website" }/>
                                        )}
                                      </div>

                                      {!isExpanded && (
                                        <div className="absolute bottom-0 left-0 right-0 h-12 bg-gradient-to-t from-white to-transparent" />
                                      )}
                                    </div>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            </>
          );
        }
        )}
        {
          packageData.days.map((day)=>{
            if (activeTab === 'summary') {
              return day.cities && day.cities.length > 0 && 
                day.cities.some(city => city.itineraries && city.itineraries.length > 0);
            }
             return day.cities && day.cities.length > 0 && 
              day.cities.some(city => 
                city.itineraries && city.itineraries.length > 0 &&
                city.itineraries.some(itinerary => {
                  // if (activeTab === 'flights') return itinerary.excursionType === 'airport';
                  if (activeTab === 'flights') return false;
                  if (activeTab === 'hotels') return itinerary.excursionType.includes('hotel');
                  // if (activeTab === 'transfers') return itinerary.excursionType === 'transfer';
                  if (activeTab === 'transfers') return itinerary.excursionType === 'intercity' || itinerary.excursionType === 'airport';
                  if (activeTab === 'activities') return itinerary.excursionType === 'sightseeing';
                  if (activeTab === 'information') return itinerary.excursionType === 'information';
                  return false;
                })
              );
            
          }).filter((x)=>x).length ==0
          && <p className='text-sm text-center'>No Results found</p>
        }
      </div>
    </section>
  );
};

export default ItinerarySection;
