"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useQueryStore } from "../store/queryStore";

interface TourImage {
  id: number;
  destination: string;
  imageLink: string;
  tourCount: number;
  startingPrice: number;
}

const TourCollectionSection = () => {
  const router = useRouter();
  const [tourImages, setTourImages] = useState<TourImage[]>([]);
  const [toursCount, setToursCount] = useState<any>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const setQuery = useQueryStore((state) => state.setQuery);
  useEffect(() => {
  const API_BASE_URL_DEV = process.env.NEXT_PUBLIC_API_BASE_URL;

  const fetchAllData = async () => {
    try {
      setIsLoading(true);

      const [toursRes, statsRes] = await Promise.all([
        fetch(`${API_BASE_URL_DEV}/api/cities/popular-destinations`),
        fetch(`${API_BASE_URL_DEV}/api/packages/website/stats`),
      ]);

      if (!toursRes.ok || !statsRes.ok) {
        throw new Error('Failed to fetch tour data');
      }

      const [toursData, statsData] = await Promise.all([
        toursRes.json(),
        statsRes.json(),
      ]);

      const filtered_data = toursData.filter((item: any) => item.status === 'ACTIVE');
      setTourImages(filtered_data);
      setToursCount(statsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching data');
    } finally {
      setIsLoading(false);
    }
  };

  fetchAllData();
}, []);


  const handleTourClick = (destination: string) => {
    const encodedDestination = encodeURIComponent(destination);
    setQuery(encodedDestination);
    router.push(`/vacation/tours?query=${encodedDestination}`);
  };
  useEffect(()=>{
    if(tourImages && tourImages.length>0){
       
      console.log("tourImages",tourImages);
    }
  },[tourImages])
  if (isLoading) {
    return (
      <div className="py-12 px-4 sm:px-6 md:px-8 lg:px-12 mt-4">
        <div className="text-center">
          <p className="text-lg text-gray-600">Loading tours...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-12 px-4 sm:px-6 md:px-8 lg:px-12 mt-4">
        <div className="text-center">
          <p className="text-lg text-red-600">{error}</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="py-12 px-4 sm:px-6 md:px-8 lg:px-12 mt-4">
      {/* Title Section */}
      <div className="text-center mb-12">
        <h3 className="text-lg font-semibold text-[#175CD3] uppercase mb-2">
          Explore Collection
        </h3>
        <h4 className="mt-[14px] font-bold text-5xl text-[#1E1E1E]">
          Curated tours for your travel goals
        </h4>
      </div>

      {/* 4x3 Image Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-7">
        {tourImages.map((tour:any) => {
          // let test = toursCount;
          // let test_new = tour;
          // // console.log();
          return (
            <div
            key={tour.id}
            // className="relative w-full h-72 bg-cover bg-center rounded-lg shadow-md overflow-hidden cursor-pointer transform transition-transform duration-300 hover:scale-105"
            //  className="relative w-[283px] h-[283px] bg-cover bg-center rounded-3xl shadow-md overflow-hidden cursor-pointer transform transition-transform duration-300 hover:scale-105"
             className="relative w-full h-[283px] bg-cover bg-center rounded-3xl shadow-md overflow-hidden cursor-pointer transform transition-transform duration-300 hover:scale-105"
            style={{
              backgroundImage: `url("${tour.imageLink || "/assets/dummy_image_new.jpg"}")`,
            }}
            onClick={() => handleTourClick(tour.destination)}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleTourClick(tour.destination);
              }
            }}
          >
            {/* Centered text at the bottom */}
            <div className="absolute bottom-0 left-0 right-0 px-4 py-6 text-white text-center bg-gradient-to-t from-black to-transparent">
              <h4 className="text-xl font-bold uppercase mb-2">{tour.destination}</h4>
             {toursCount[`${tour.destination}`] && <ol className="flex justify-center text-sm space-x-1">
                <li className="inline">{ toursCount[`${tour.destination}`][0]?.totalPackages} tours</li>
                {/* <li className="inline mx-2">•</li>
                <li className="inline">Starting from ${tour.startingPrice}</li> */}
              </ol>}
            </div>
          </div>
          )
        })}
      </div>
    </div>
  );
};

export default TourCollectionSection;

