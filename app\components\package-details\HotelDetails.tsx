"use client";

import { useState } from 'react';
import Image from 'next/image';
import { findDisplayInFromObject, getValueFromPath } from '@/app/utility/display_in_tracker';

interface RoomDetail {
  id: string;
  roomType: string;
  status: string;
  finalPrice: string;
  actualPrice: number;
  companyMarkup: number;
  companyMarkupType: string;
  images?: Array<{ filePath: string }>;
}

interface HotelDetailsProps {
  itinerary: {
    subCode: string;
    selected_excursion: {
      hotelName?: string;
      cityName?: string;
      hotelAddress?: string;
      shortDescription?: string;
      images?: Array<{ filePath: string; DisplayIn?: string[] }>;
      roomDetails?: RoomDetail[];
      externalRatings?: Array<{
        name: string;
        rank: number;
        rankError: boolean;
        link?: string;
      }>;
      starRating?: number | string;
      inclusions?: string;
      contactNumber?: string;
    };
    agentNote?:any;
    customerNote?:any;
    adminNote?:any;
    subAdminNote?:any;
    desc: string;
    noOfNightsBooked: number;
  };
  itineraryType?:string;
  dayId: number;
  cityIndex: number;
  itineraryIndex: number;
  isExpanded?:boolean;
}
const HotelDetails: React.FC<HotelDetailsProps> = ({ 
  itinerary, 
  dayId, 
  cityIndex, 
  itineraryIndex,
  isExpanded=true,
  itineraryType="website"
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  let filtered_images = itinerary?.selected_excursion?.images?.filter((x)=>x.filePath && x.filePath!="") || [];
   filtered_images = filtered_images.filter((element:any)=>{
    if(element.displayIn || element.DisplayIn ){
      if(element.displayIn){
        return element.displayIn.includes(itineraryType?.toLowerCase());
      } else if(element.DisplayIn){
        return element.DisplayIn.map((e:any)=>{
          let data= e.split(" ")[0];
          return data.toLowerCase();
        }).includes(itineraryType?.toLowerCase());

      }
    }
    return true;
  })
  const display_in_tracker  = findDisplayInFromObject(itinerary);
  const handleImageNavigation = (direction: 'prev' | 'next') => {
    const images = filtered_images || [];
    
    if (images.length > 0) {
      const newIndex = direction === 'next' 
        ? (currentImageIndex + 1) % images.length 
        : (currentImageIndex - 1 + images.length) % images.length;
      
      setCurrentImageIndex(newIndex);
    }
  };
  return (
    <>
      <div className="rounded-lg">
        <div className="flex gap-4">
          {/* <div className="relative w-20 h-20 rounded-lg overflow-hidden"> */}
          <div className="relative w-[120px] h-[120px] rounded-lg overflow-hidden">
            {filtered_images && filtered_images.length > 0 ? (
              <>
                <Image
                  src={filtered_images[currentImageIndex]?.filePath || ''}
                  alt={itinerary.selected_excursion.hotelName || 'Hotel image'}
                  fill
                  className="object-cover"
                  onError={(e: any) => {
                    const target = e.target as HTMLElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent) {
                      parent.style.backgroundColor = '#E5E7EB';
                      parent.innerHTML = '<span class="text-gray-500 text-xs absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">No image available</span>';
                    }
                  }}
                />
                {filtered_images.length > 1 && (
                  <>
                    <div className="absolute bottom-1 left-0 right-0">
                      <div className="flex justify-center space-x-1">
                        {filtered_images.map((_, index: number) => (
                          <div
                            key={index}
                            className={`w-1 h-1 rounded-full ${
                              index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    <button
                      className="absolute left-1 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-1 transition-colors"
                      onClick={() => handleImageNavigation('prev')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor" className="w-3 h-3 text-gray-900">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                      </svg>
                    </button>
                    <button
                      className="absolute right-1 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-1 transition-colors"
                      onClick={() => handleImageNavigation('next')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor" className="w-3 h-3 text-gray-900">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                      </svg>
                    </button>
                  </>
                )}
              </>
            ) : (
              <Image
                src="/assets/hotels/hotel-placeholder.jpg"
                alt={itinerary.selected_excursion.hotelName || 'Hotel image'}
                fill
                className="object-cover"
              />
            )}
          </div>
          <div className="flex-1 flex flex-col justify-between">
            <div>
              <h4 className="font-bold text-base text-black">{itinerary.selected_excursion.hotelName}</h4>
              <div className="flex items-center gap-1 mt-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg 
                    key={star} 
                    className={`w-4 h-4 ${star <= (Number(itinerary.selected_excursion.starRating) || 0) ? 'text-yellow-400' : 'text-[#667085]'}`} 
                    fill="currentColor" 
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <div className="flex items-center gap-2 mt-2">
                <span className="text-black text-xs font-normal">{itinerary.selected_excursion.hotelAddress}</span>
                <a 
                  href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
                    `${itinerary.selected_excursion.hotelName} ${itinerary.selected_excursion.hotelAddress}`
                  )}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-[#175CD3] hover:text-blue-700 text-xs font-bold flex items-center gap-1 underline"
                >
                  Get Directions
                </a>
              </div>
              <p className="text-sm text-gray-600 mt-1">{itinerary.selected_excursion.shortDescription}</p>
            </div>
            <div className="flex flex-wrap gap-4 mt-2 text-xs">
              <div className="flex items-center gap-2">
                
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18.2988 4.79956H5.69848C4.70434 4.79956 3.89844 5.60547 3.89844 6.5996V19.1999C3.89844 20.194 4.70434 21 5.69848 21H18.2988C19.2929 21 20.0988 20.194 20.0988 19.1999V6.5996C20.0988 5.60547 19.2929 4.79956 18.2988 4.79956Z" stroke="#667085" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M15.5996 3V6.60009" stroke="#667085" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M8.39844 3V6.60009" stroke="#667085" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M3.89844 10.1997H20.0988" stroke="#667085" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>

                <span className="text-[14px] font-medium text-[#344054]">{itinerary.noOfNightsBooked} Nights</span>
              </div>
              <div className="flex items-center gap-2">
                
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M6.63283 5.47717C5.88417 5.47717 5.27679 6.09418 5.27679 6.84459V10.8509H4.92283C4.17324 10.8509 3.54886 11.454 3.54886 12.2003V18.1438C3.54592 18.3465 3.72993 18.5331 3.93283 18.5331C4.13572 18.5331 4.31974 18.3465 4.31679 18.1438V16.8003H19.6768V18.1438C19.6739 18.3465 19.8579 18.5331 20.0608 18.5331C20.2637 18.5331 20.4477 18.3465 20.4447 18.1438V12.2003C20.4447 11.4539 19.8204 10.8509 19.0708 10.8509H18.7168V6.84459C18.7168 6.09418 18.1094 5.47717 17.3608 5.47717H6.63283ZM6.63283 6.24486H17.3608C17.6904 6.24486 17.9488 6.49946 17.9488 6.84459V10.8509H16.5208C16.5735 10.6931 16.6048 10.5187 16.6048 10.3412V9.04566C16.6048 8.72759 16.5186 8.43576 16.3527 8.20004C16.1871 7.96433 15.9162 7.77428 15.5967 7.77428H13.7727C13.4533 7.77428 13.1825 7.96433 13.0167 8.20004C12.851 8.43576 12.7647 8.7276 12.7647 9.04566V10.3412C12.7647 10.5189 12.796 10.6931 12.8486 10.8509H11.1447C11.1973 10.6931 11.2286 10.5187 11.2286 10.3412V9.04566C11.2286 8.72759 11.1424 8.43576 10.9766 8.20004C10.8109 7.96433 10.54 7.77428 10.2205 7.77428H8.39658C8.07716 7.77428 7.80635 7.96433 7.64055 8.20004C7.47488 8.43576 7.38849 8.7276 7.38849 9.04566V10.3412C7.38849 10.5189 7.41983 10.6931 7.47247 10.8509H6.04453V6.84459C6.04453 6.49947 6.30288 6.24486 6.63247 6.24486H6.63283ZM8.39679 8.54187H10.2208C10.2467 8.54187 10.2872 8.55299 10.3468 8.6379C10.4064 8.72268 10.4608 8.87241 10.4608 9.04573V10.3412C10.4608 10.5145 10.4064 10.6641 10.3468 10.749C10.2872 10.8338 10.2469 10.8451 10.2208 10.8451H8.39679C8.37081 10.8451 8.33037 10.834 8.27077 10.749C8.21117 10.6641 8.15679 10.5144 8.15679 10.3411V9.04562C8.15679 8.87232 8.21117 8.72259 8.27077 8.63779C8.33037 8.55288 8.37068 8.54177 8.39679 8.54177V8.54187ZM13.7728 8.54187H15.5968C15.6227 8.54187 15.6632 8.55299 15.7228 8.6379C15.7824 8.72268 15.8368 8.87241 15.8368 9.04573V10.3412C15.8368 10.5145 15.7824 10.6641 15.7228 10.749C15.6632 10.8338 15.6229 10.8451 15.5968 10.8451H13.7728C13.7468 10.8451 13.7064 10.834 13.6468 10.749C13.5872 10.6641 13.5328 10.5144 13.5328 10.3411V9.04562C13.5328 8.87232 13.5872 8.72259 13.6468 8.63779C13.7064 8.55288 13.7467 8.54177 13.7728 8.54177V8.54187ZM4.92297 11.6186C10.0734 11.6257 14.5216 11.6186 19.071 11.6186C19.4178 11.6186 19.677 11.8755 19.677 12.2004V13.5378L4.317 13.5858V12.2003C4.317 11.8754 4.57615 11.6185 4.92303 11.6185L4.92297 11.6186ZM19.6768 14.3055V16.0328H4.31679V14.3534L19.6768 14.3055Z" fill="#667085"/>
                </svg>

                <span className="text-[14px] font-medium text-[#344054]">
                  {itinerary.selected_excursion.roomDetails?.find((room: RoomDetail) => room.id === itinerary.subCode)?.roomType || itinerary.subCode}
                </span>
              </div>

            </div>
          </div>
        </div>

        {isExpanded &&
        <>
            <div className='itinerary-content text-sm pt-4 text-black font-normal ' dangerouslySetInnerHTML={{__html:itinerary.desc}}></div>
            {itinerary?.selected_excursion?.externalRatings && itinerary.selected_excursion.externalRatings.length > 0 && (
              <div className="mt-3">
                <p className="text-xs text-[#667085] font-semibold mb-2">Ratings</p>
                <div className="flex gap-4">
                  {itinerary.selected_excursion.externalRatings.map((rating, index: number) => {
                    const isBooking = rating?.name ? rating.name?.toLowerCase().includes('booking.com') : false;
                    const isGoogle = rating?.name ? rating.name?.toLowerCase().includes('google.com') : false;
                    return (
                      <div key={index} className="flex items-center gap-2">
                        {isBooking ? (
                          <a href={`${rating?.link}`} target='_blank'>
                            <Image
                            src="/assets/ratings/booking.png"
                            alt="Booking.com Rating"
                            width={80}
                            height={20}
                            className="object-contain"
                          />
                          </a>
                        ) :
                        isGoogle? (
                          <a href={`${rating?.link}`} target='_blank'>
                            <Image
                            src="/assets/ratings/google_new.png"
                            alt="Google Rating"
                            width={50}
                            height={50}
                            className="object-contain"
                          />
                          </a>
                        ): (
                          <a href={`${rating?.link}`} target='_blank'>
                            <Image
                            src="/assets/ratings/tripadvisor.png"
                            alt="TripAdvisor Rating"
                            width={80}
                            height={20}
                            className="object-contain"
                          />
                          </a>
                        )}
                        <span className="text-sm font-bold">{rating?.rank || '-'}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Amenities Section */}
            {itinerary.selected_excursion.inclusions &&
              itinerary.selected_excursion.inclusions.split('\n').some(item => item.trim()) && (
              <div className="mt-4">
                <p className="text-xs text-[#667085] font-semibold mb-2">Amenities</p>
                <div className="text-sm text-[#344054] font-medium flex flex-wrap gap-x-2 gap-y-1">
                  {itinerary.selected_excursion.inclusions
                    .split('\n')
                    .filter(item => item.trim())
                    .map((item, idx, arr) => (
                      <span key={idx} dangerouslySetInnerHTML={{ __html: item }}>
                      </span>
                    ))}
                </div>
              </div>
            )}

            {/* Contact Section */}
           {/* {itinerary.selected_excursion.contactNumber && <div className="mt-4">
              <p className="text-xs text-[#667085] font-semibold mb-2">Contact</p>
              <div className="flex items-center gap-6 text-sm text-[#344054] font-medium">
               <div className="flex items-center gap-2">
                  
                    <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20.9994 15.9201V18.9201C21.0006 19.1986 20.9435 19.4743 20.832 19.7294C20.7204 19.9846 20.5567 20.2137 20.3515 20.402C20.1463 20.5902 19.904 20.7336 19.6402 20.8228C19.3764 20.912 19.0968 20.9452 18.8194 20.9201C15.7423 20.5857 12.7864 19.5342 10.1894 17.8501C7.77327 16.3148 5.72478 14.2663 4.18945 11.8501C2.49942 9.2413 1.44769 6.27109 1.11944 3.1801C1.09446 2.90356 1.12732 2.62486 1.21595 2.36172C1.30457 2.09859 1.44702 1.85679 1.63421 1.65172C1.82141 1.44665 2.04925 1.28281 2.30324 1.17062C2.55722 1.05843 2.83179 1.00036 3.10945 1.0001H6.10945C6.59475 0.995321 7.06524 1.16718 7.43321 1.48363C7.80118 1.80008 8.04152 2.23954 8.10944 2.7201C8.23607 3.68016 8.47089 4.62282 8.80945 5.5301C8.94399 5.88802 8.97311 6.27701 8.89335 6.65098C8.8136 7.02494 8.62831 7.36821 8.35944 7.6401L7.08945 8.9101C8.513 11.4136 10.5859 13.4865 13.0894 14.9101L14.3594 13.6401C14.6313 13.3712 14.9746 13.1859 15.3486 13.1062C15.7225 13.0264 16.1115 13.0556 16.4694 13.1901C17.3767 13.5286 18.3194 13.7635 19.2794 13.8901C19.7652 13.9586 20.2088 14.2033 20.526 14.5776C20.8431 14.9519 21.0116 15.4297 20.9994 15.9201Z" stroke="#667085" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>

                  <span>{itinerary.selected_excursion.contactNumber}</span>
                </div>
              </div>
            </div>} */}

            {
            display_in_tracker.length > 0 &&
            display_in_tracker.map((element, index) => {
              const key = element.path.split(".").at(-1).replace(/displayin$/i, "");
              const value = getValueFromPath(itinerary, element.path);
              if(!value){
                  return;
              }
              return (
                ((element.key.toLowerCase() !== "displayin") &&
                  element.value.map((itr: any) => {
                    itr.toLowerCase();
                    return itr.split(" ")[0];
                  }).includes(itineraryType.toLowerCase())) && (
                  <div
                    key={index}
                    className="bg-white border border-gray-200 rounded-lg shadow-md p-4 mb-4 mt-6"
                  >
                    <p className="text-sm font-semibold text-gray-800 mb-2">
                      {key.replace(/([A-Z])/g, ' $1').replace(/^./, (str: any) => str.toUpperCase())}
                    </p>

                    {key.toLowerCase().includes("link") ? (
                      key.toLowerCase().includes("youtube") ? (
                        <div className="w-full aspect-video">
                          <iframe
                            className="w-full h-full rounded-md"
                            src={`https://www.youtube.com/embed/${value.split("v=")[1]}`}
                            frameBorder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowFullScreen
                          />
                        </div>
                      ) : (
                        <a
                          href={value}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 underline break-words"
                        >
                          {value}
                        </a>
                      )
                    ) : (
                      <div
                        className="itinerary-content text-sm text-gray-700 leading-relaxed"
                        dangerouslySetInnerHTML={{ __html: value }}
                      />
                    )}
                  </div>
                )
              );
            })
          }

          {typeof window !== 'undefined' && localStorage.getItem('wy_user_data') && itineraryType!=='website' &&
            (() => {
              try {
                const role = JSON.parse(localStorage.getItem('wy_user_data') || '{}')?.role?.toLowerCase();

                // Normalize the role to category
                const isAdmin = role === 'admin' || role === 'subadmin';
                const isCustomer = role === 'customer';
                const isAgent = !isCustomer && !isAdmin; // All others treated as agent

                // Collect keys to show
                const noteKeys: (keyof typeof itinerary)[] = [];
                if (isCustomer) {
                  noteKeys.push('customerNote');
                }
                if (isAgent) {
                  noteKeys.push('customerNote', 'agentNote');
                }
                if (isAdmin) {
                  noteKeys.push('customerNote', 'agentNote', 'adminNote', 'subAdminNote');
                }

                // Render notes
                return (
                  <>
                    {noteKeys.map((key) =>
                      itinerary?.[key] ? (
                        <div key={key} className="bg-white border border-gray-200 rounded-lg shadow-md p-4 mb-4 mt-6">
                          <p className="text-sm font-semibold text-gray-800 mb-2">Note ({key})</p>
                          <div
                            className="itinerary-content text-sm text-gray-700 leading-relaxed"
                            dangerouslySetInnerHTML={{ __html: itinerary[key] as string }}
                          />
                        </div>
                      ) : null
                    )}
                  </>
                );
              } catch {
                return null;
              }
            })()
          }



        </>}

      </div>
      
      {isExpanded && itineraryType.toLowerCase()=="final" &&
      <div className="mt-4 bg-[#FFF7E0] rounded-b-xl rounded-t-none px-4 py-3 flex items-center justify-between">
        <div>
          <p className="font-bold text-sm text-[#101828] mb-1">Download Vouchers</p>
          <p className="text-sm text-[#101828] font-normal">Hotel Campiello-Venice-Hotel-Voucher-1-4-Dec.pdf</p>
        </div>
        <button className="ml-4 p-2 rounded-full hover:bg-[#F2E9D7] transition-colors">
          
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="#175CD3" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M7 10L12 15L17 10" stroke="#175CD3" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 15V3" stroke="#175CD3" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>

        </button>
      </div>}
    </>
  );
};

export default HotelDetails;
