"use client";

import { useEffect, useState } from 'react';
import Link from 'next/link';
import NavigationBar from '.././components/NavBar';
import Breadcrumb from '../components/BreadCrumbs';
import Footer from '.././components/Footer';
import axios from 'axios';
import { countryCodeToEmoji, countryFlags } from '../components/DestinationTours';
import { phone_code } from '../utility/country_code';

export default function MyProfile() {
  const [selectedTab, setSelectedTab] = useState('My Profile');
  const file_extensions_allowed = [".svg", ".png", ".jpg", ".jpeg", ".gif"];
  const [isDragging, setIsDragging] = useState(false);
  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);
  const [fileUpload, setFileUpload] = useState<any>(null);

  const [countries,setCountries] = useState<any>([]);

  const [isEditing, setIsEditing] = useState(false);
  const NEXT_PUBLIC_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
  const [formData, setFormData] = useState({
    id:"",
    firstName: '',
    lastName: '',
    email: '',
    mobile: '',
    alternateMobile: '',
    address: '',
    city: '',
    state: '',
    pincode: '',
    country: 'india',
    gender: '',
    profilePic:"",
    password:"",
  });
 const [formDataState, setFormDataState] = useState({
    id:"",
    firstName: '',
    lastName: '',
    email: '',
    mobile: '',
    alternateMobile: '',
    address: '',
    city: '',
    state: '',
    pincode: '',
    country: 'india',
    gender: '',
    profilePic:"",
    password:"",
  });

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);

    const file = event.dataTransfer.files[0];
    // if (file && (file.name.endsWith(".xlsx") || file.name.endsWith(".xls"))) {
    if (file && file_extensions_allowed.includes(file.name.slice(-4))) {
      handleFileUpload(file);
    }
  };

const handleFileUpload = (file: File) => {
    if (file) {
      setUploadedFileName(file.name); // Save file name
      console.log("File uploaded:", file);
      setFileUpload(file);
    } else {
    setFileUpload(null);
    }
  };
const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>)=>{
    const file = event.target.files?.[0];
     if (file) {
      handleFileUpload(file);
    } else{
      setFileUpload(null);
      setUploadedFileName(null)
    }
  }
  const handleInputChange:any = (e: React.ChangeEvent<HTMLInputElement>) => {
    let { name, value } = e.target;
    if(name === "firstName" || name === "lastName"){
      value = value.replace(/\s/g, "");
    }
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

 const fetchUserData = async () => {
     
    try {
      const response = await axios.get(`${NEXT_PUBLIC_API_BASE_URL}/api/users/${JSON.parse(localStorage.getItem('wy_user_data') as string)?.userId}`);
      setFormData((prev)=>{
        const data = response.data;
        return {
          id:data?.id,
          firstName:data?.username.split(" ")[0],
          lastName:data?.username.split(" ")[1] ? data?.username.split(" ")[1] : "",
          email:data.email,
          mobile:data.phone,
          alternateMobile:data?.altPhone || '',
          address:data?.address || '' ,
          city:data?.city || '',
          state:data?.state || '',
          country:data?.country || '',
          pincode:data?.pincode || '',
          // country:data?.country || '',
          gender:data?.gender || '',
          profilePic:data?.profilePic || '',
          password:data?.password || '',
        }
      });
      setFormDataState((prev)=>{
        const data = response.data;
        return {
          id:data?.id,
          firstName:data?.username.split(" ")[0],
          lastName:data?.username.split(" ")[1],
          email:data.email,
          mobile:data.phone,
          alternateMobile:data?.altPhone || '',
          address:data?.address || '' ,
          city:data?.city || '',
          state:data?.state || '',
          country:data?.country || '',
          pincode:data?.pincode || '',
          // country:data?.country || '',
          gender:data?.gender || '',
          profilePic:data?.profilePic || '',
          password:data?.password || '',
        }
      });
      
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };
  const uploadImage =async ()=>{
    const formData = new FormData();
    formData.append('images', fileUpload);
    try {
        const response = await axios.post(`${NEXT_PUBLIC_API_BASE_URL}/api/uploadImages/user-profile?authtoken=${localStorage.getItem('authToken')}`,formData,{
          headers: {
            'Content-Type': 'multipart/form-data',
            // 'authtoken': `Bearer ${localStorage.getItem('authToken')}`,
          }
        });
         
        if(response.status  === 200){
          return response.data[0];
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        return "";
      }
  }
  const handleSave = async () => {
    
    let formDataNew = {...formData};
    const {email,mobile,address,city,state,country,pincode,gender} = formDataNew;
    // if([email,mobile,address,city,state,country,pincode,gender].some((ele)=>ele=="")){
    if([formDataNew.firstName,email,mobile,country,gender].some((ele)=>ele=="")){
      alert("name,email,mobile,country,pincode,gender cant be empty");
      return;
    }
    if(fileUpload){
      const image_url = await uploadImage();
      formDataNew.profilePic = image_url;
    }
    let data = {
      userName:`${formDataNew.firstName} ${formDataNew.lastName}`.trim(),
      userEmail:formDataNew.email,
      phone:formDataNew.mobile,
      altPhone:formDataNew?.alternateMobile || '',
      address:formDataNew?.address || '' ,
      city:formDataNew?.city || '',
      state:formDataNew?.state || '',
      country:formDataNew?.country || '',
      pincode:formDataNew?.pincode || '',
      // country:formDataNew?.country || '',
      gender:formDataNew?.gender || '',
      profilePic:formDataNew?.profilePic || '',
      password:formDataNew?.password || '',
    }
    
    try {
        const response = await axios.post(`${NEXT_PUBLIC_API_BASE_URL}/api/user/${JSON.parse(localStorage.getItem('wy_user_data') as string)?.userId}`,data,{
          headers: {
            'Content-Type': 'application/json',
            // 'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          }
        });
         
        if(response.status  === 200){
          fetchUserData();
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(formDataState);
    setIsEditing(false);
  };

  const fetch_country =async ()=>{
  // const country_data = await fetch("https://countriesnow.space/api/v0.1/countries/");
  // const data = await country_data.json();  // parse JSON
  // console.log("country_data", data.data);
  const data = phone_code.map((ele)=>{
    return{
      country:ele?.country_en,
      country_code:ele?.country_code,
    }
  }).sort((a,b)=>a.country?.toLowerCase().localeCompare(b.country?.toLowerCase()))
  setCountries(data);
  }

  useEffect(()=>{
    fetch_country();
    fetchUserData();
  },[])
  
  return (
    <div className="min-h-screen bg-white">
      <NavigationBar />
      
      {/* Breadcrumb */}
      <div className='bg-[#F5FAFF]'>
        <Breadcrumb nav_style_class={"mt-[0px] pt-4"}/>
      {/* Tabs */}
        <div className="">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex space-x-8 items-center pt-[10px]">
              <Link 
                href="/myprofile"
                className={`pt-[6px] px-[20px] pb-[3px] rounded-t-lg ${selectedTab === 'My Profile' ? 'text-blue-600 font-manrope font-semibold text-base leading-6 tracking-normal space-y-4 bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 '}`}
              >
                My Profile
              </Link>
              <Link 
                href="/mytrips"
                className={` pt-[6px] px-[20px] pb-[3px] rounded-t-lg ${selectedTab === 'My Trips' ? 'text-blue-600 font-manrope font-semibold text-base leading-6 tracking-normal space-y-4 bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 '}`}
              >
                My Trips
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Profile Content */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg p-6">
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center gap-4">
            <img 
                // src="/profile-placeholder.jpg"
                // src="/assets/profile_pic_placeholder.jpg"
                src={formData?.profilePic || "/assets/profile_pic_placeholder.jpg"}
                alt="Profile"
                className="w-16 h-16 rounded-full object-cover"
                onError={(e) => {
                  e.currentTarget.onerror = null;
                  e.currentTarget.src = "/assets/profile_pic_placeholder.jpg";
                }}
              />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">{`${formDataState?.firstName} ${formDataState?.lastName!=undefined ? formDataState?.lastName :""}`}</h1>
                <p className="text-sm text-gray-600">Customer ID: {formDataState?.id}</p>
              </div>
            </div>
            {!isEditing && (
              <button 
                onClick={() => setIsEditing(true)}
                className="inline-flex items-center gap-2 px-4 py-2 rounded-full border border-gray-200 text-gray-600 hover:bg-gray-50"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
                  <path strokeLinecap="round" strokeLinejoin="round" d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10" />
                </svg>
                Edit Profile
              </button>
            )}
          </div>

          {/* Form */}
          <div className="grid grid-cols-2 ">
            {/* Row 1 */}
            <div className="col-span-2">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">First name</label>
                  <input 
                    type="text" 
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-4 py-2.5 rounded-lg border border-gray-300 bg-gray-50 text-gray-900 disabled:bg-gray-50 disabled:text-gray-900 enabled:bg-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Last name</label>
                  <input 
                    type="text" 
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-4 py-2.5 rounded-lg border border-gray-300 bg-gray-50 text-gray-900 disabled:bg-gray-50 disabled:text-gray-900 enabled:bg-white"
                  />
                </div>
              </div>
              <hr className="border-t border-[#E4E7EC] my-5" />
            </div>

            {/* Row 2 */}
            <div className="col-span-2">
              <div className="grid grid-cols-2 gap-6">
                <div className='col-span-2'>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-4 flex items-center pointer-events-none">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-gray-400">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" />
                      </svg>
                    </div>
                    <input 
                      type="email" 
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      className="w-full pl-12 pr-4 py-2.5 rounded-lg border border-gray-300 bg-gray-50 text-gray-900 disabled:bg-gray-50 disabled:text-gray-900 enabled:bg-white"
                    />
                  </div>
                </div>
              </div>
              <hr className="border-t border-[#E4E7EC] my-5" />
            </div>

            {/* Row 3 */}
            <div className="col-span-2">
              <div className="grid grid-cols-2 gap-6">
              <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Mobile No</label>
                  <input 
                    type="tel" 
                    name="mobile"
                    value={formData.mobile}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-4 py-2.5 rounded-lg border border-gray-300 bg-gray-50 text-gray-900 disabled:bg-gray-50 disabled:text-gray-900 enabled:bg-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Alternate Mobile No</label>
                  <input 
                    type="tel" 
                    name="alternateMobile"
                    value={formData.alternateMobile}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-4 py-2.5 rounded-lg border border-gray-300 bg-gray-50 text-gray-900 disabled:bg-gray-50 disabled:text-gray-900 enabled:bg-white"
                  />
                </div>
              </div>
              <hr className="border-t border-[#E4E7EC] my-5" />
            </div>
            {isEditing && (
            <>
            <div className="col-span-2 flex items-start gap-4">
              <img
                src={fileUpload ? URL.createObjectURL(fileUpload) : formData?.profilePic || "/assets/profile_pic_placeholder.jpg"}
                alt="Profile"
                className="w-16 h-16 rounded-full object-cover"
                onError={(e) => {
                  e.currentTarget.onerror = null;
                  e.currentTarget.src = "/assets/profile_pic_placeholder.jpg";
                }}
              />
              <div className='w-full border border-[#E4E7EC] rounded-xl aspect-[748/126] flex flex-col justify-center items-center'
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              >
                  <img
                    src={"/assets/Featured_Icon.svg"}
                    alt="upload_icon"
                    // className="w-10 h-10 mb-2 rounded-full object-cover"
                    className="w-16 h-16 mb-2 rounded-full object-cover"
                  />
                <div>
                  <p className='text-sm font-normal text-[#475467]'>
                    <button 
                    className='text-sm font-semibold text-[#175CD3] cursor-pointer' 
                    onClick={() => document.getElementById("file-upload")?.click()}>
                      Click to upload
                    </button> 
                    {" "}or drag and drop image
                  </p> 
                  <p className='text-xs font-normal text-[#475467]'>SVG, PNG, JPG or GIF (max. 800x400px)</p> 
                </div>
                <input
                  type="file"
                  // accept=".svg,.png,.jpg,.jpeg,.gif"
                  accept={file_extensions_allowed.join(",")}
                  onChange={handleFileInputChange}
                  className="hidden"
                  id="file-upload"
                />
              </div>
            </div>
            <hr className="border-t border-[#E4E7EC] my-5 col-span-2" /> 
            </>
            )
            }
            {/* Row 4 */}
            <div className="col-span-2">
              <div className="grid grid-cols-2 gap-6">
                <div className="col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                  <input 
                    type="text" 
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-4 py-2.5 rounded-lg border border-gray-300 bg-gray-50 text-gray-900 disabled:bg-gray-50 disabled:text-gray-900 enabled:bg-white"
                  />
                </div>

              </div>
              <hr className="border-t border-[#E4E7EC] my-5" />
            </div>
            {/* Row 5 */}
            <div className="col-span-2">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
                  <input 
                    type="text" 
                    name="city"
                    value={formData.city}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-4 py-2.5 rounded-lg border border-gray-300 bg-gray-50 text-gray-900 disabled:bg-gray-50 disabled:text-gray-900 enabled:bg-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">State</label>
                  <input 
                    type="text" 
                    name="state"
                    value={formData.state}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-4 py-2.5 rounded-lg border border-gray-300 bg-gray-50 text-gray-900 disabled:bg-gray-50 disabled:text-gray-900 enabled:bg-white"
                  />
                </div>
              </div>
              <hr className="border-t border-[#E4E7EC] my-5" />
            </div>

            {/* Row 6 */}
            <div className="col-span-2">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Pincode</label>
                  <input 
                    type="text" 
                    name="pincode"
                    value={formData.pincode}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-4 py-2.5 rounded-lg border border-gray-300 bg-gray-50 text-gray-900 disabled:bg-gray-50 disabled:text-gray-900 enabled:bg-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Country</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-4 flex items-center pointer-events-none">
                      {/* <span className="text-lg">🇮🇳</span> */}
                      {/* <span className="text-lg">{countryFlags[formData.country.trim().toLowerCase()]&& countryCodeToEmoji(countryFlags[formData.country.trim().toLowerCase()])}</span> */}
                      <span className="text-lg">
                        <img 
                        src={`/assets/flags/${formData.country.trim().split(" ").join("-").toLowerCase()}.svg`} 
                        className="w-5 h-5 mr-1" 
                        alt="countryImg"
                      />
                      </span>
                    </div>
                    <select
                    name="country"
                    disabled={!isEditing}
                    value={formData.country}
                    onChange={handleInputChange}  
                    className="w-full pl-12 pr-4 py-2.5 rounded-lg border border-gray-300 bg-gray-50 text-gray-900 disabled:bg-gray-50 disabled:text-gray-900 enabled:bg-white"
                    >
                      <option>Select Country</option>
                      {
                        countries.map((ele:any)=><option key={`${ele.country}-key`} value={ele.country}>{ele.country}</option>)
                        
                      }
                    </select>
                    {/* <input 
                      type="text" 
                      name="country"
                      value={formData.country}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      className="w-full pl-12 pr-4 py-2.5 rounded-lg border border-gray-300 bg-gray-50 text-gray-900 disabled:bg-gray-50 disabled:text-gray-900 enabled:bg-white"
                    /> */}
                  </div>
                </div>
              </div>
              <hr className="border-t border-[#E4E7EC] my-5" />
            </div>

            {/* Row 7 */}
            <div className="col-span-2">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                  <select
                    name="gender"
                    value={formData.gender}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-4 py-2.5 rounded-lg border border-gray-300 bg-gray-50 text-gray-900 disabled:bg-gray-50 disabled:text-gray-900 enabled:bg-white"
                    >
                      <option value={""}>Select Gender</option>
                      <option value={"Male"}>Male</option>
                      <option value={"Female"}>Female</option>
                    </select>
                  {/* <input 
                    type="text" 
                    name="gender"
                    value={formData.gender}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-4 py-2.5 rounded-lg border border-gray-300 bg-gray-50 text-gray-900 disabled:bg-gray-50 disabled:text-gray-900 enabled:bg-white"
                  /> */}
                </div>
              </div>
            </div>
          </div>


          {/* Action Buttons */}
          {isEditing && (
            <div className="flex justify-end gap-4 mt-8">
              <button
                onClick={handleCancel}
                className="px-6 py-2 rounded-full border border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="px-6 py-2 rounded-full bg-blue-600 text-white hover:bg-blue-700"
              >
                Save Changes
              </button>
            </div>
          )}
        </div>
      </div>
      <Footer />
    </div>
  );
} 