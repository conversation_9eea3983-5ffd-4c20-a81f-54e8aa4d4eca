export default function FeaturesSection() {
    return (
      <section className="container mx-auto px-4 lg:px-8 py-8 sm:py-12 lg:py-16" >
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-12 items-start">
          {/* Left Column (Text) - Always first */}
          <div className="order-1">
            <div className="flex flex-col gap-3 sm:gap-4"> 
              <h3 className="text-[#175CD3] text-sm sm:text-base lg:text-lg leading-relaxed font-semibold uppercase tracking-wide">
                Why Choose Us
              </h3>
              <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight sm:leading-snug lg:leading-[60px] text-gray-900">
                We offer best services
              </h2>
              <p className="text-black py-[30px]  text-sm sm:text-base lg:text-lg leading-relaxed tracking-wide">
                            Hay! WiseYatra there to help you find your dream holiday.
                            <br />
                            Easy you just find where you want to go and buy the ticket.
              </p>
            </div>
            
            <ul className="mt-6 sm:mt-8 space-y-6">
              {[
                { title: "Best rates", desc: "Choose your favorite location" },
                { title: "Passionate service", desc: "Choose your favorite location" },
                { title: "Customised trips", desc: "Choose your favorite location" },
                { title: "No hidden fees", desc: "Choose your favorite location" },
              ].map((feature, index) => (
                <li key={index} className="flex gap-4 items-start">
                  <div className="flex-shrink-0">
                    <div className="w-[70px] h-[70px] bg-[#175CD3] rounded-2xl flex items-center justify-center">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" fill="white"/>
                      </svg>
                    </div>
                  </div>
                  <div className="flex-1 pt-1">
                    <h3 className="text-black text-[20px] font-semibold leading-tight mb-2">
                      {feature.title}
                    </h3>
                    <p className="text-[#667085] text-[16px] font-normal leading-relaxed">
                      {feature.desc}
                    </p>
                  </div>
                </li>
              ))}
            </ul>
          </div>

          {/* Right Column (Image) - Always second */}
          <div className="order-2 flex justify-center hidden sm:block lg:justify-end">
            <div className="relative">
              <img
                src="/assets/chooseus.png"
                alt="Feature Illustration"
                className="w-full object-cover"
              />
            </div>
          </div>
        </div>
      </section>
    );
}