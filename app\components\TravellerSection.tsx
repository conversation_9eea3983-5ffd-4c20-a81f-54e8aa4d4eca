"use client";

import { useContext, useEffect, useState } from 'react';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { AppContext } from '../context/useAppContext';
import CustomDateInput from '../utility/date_input';
import toast from 'react-hot-toast';
import FileUploadComponent from './FileUpload/FileUpload';
import axios from 'axios';
import { phone_code } from '../utility/country_code';
import { useCurrencyStore } from '../store/useCurrencyStore';
import { useCuponStore } from '../store/bookingCuponStore';

interface TravellerInfo {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  passportNumber: string;
  passportIssueDate: string;
  passportExpiryDate: string;
  nationality: string;
  // passportIssueCity: string;
  mealPreference: 'veg' | 'non-veg';
  pancard:string;
  passport:string;
  gender:any;
  pancard_no:any;
}

const TravellerSection = () => {
  let NEXT_SITE_URL = process.env.NEXT_SITE_URL;
  const {setCuponApplied,setCuponData,cuponApplied,cuponData} = useCuponStore();
  const [redirect_to_url,set_redirect_to_url] = useState('');
  const convertFromINR = useCurrencyStore(state => state.convertFromINR);
  const symbols = useCurrencyStore(state => state.symbols);
  const selectedCurrency = useCurrencyStore(state => state.selectedCurrency);
  const {bookingData,setBookingData} = useContext(AppContext);
  const [uploadedFileName, setUploadedFileName] = useState("");
  const [fileUpload, setFileUpload] = useState<any>({});
  const [price_to_show,set_price_to_show]= useState<any>(0); 
  const [price_to_show_without_promo,set_price_to_show_without_promo]= useState<any>(0); 
  const [rerender_price,set_rerender_price]= useState<any>(false); 

  // const today = new Date().toISOString().split("T")[0];
  const today:any = new Date();
  const router = useRouter();
  const [countries,setCountries] = useState<any>([]);
  const [travellers, setTravellers] = useState<TravellerInfo[]>([
    {
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      passportNumber: '',
      passportIssueDate: '',
      passportExpiryDate: '',
      nationality: 'India',
      // passportIssueCity: '',
      gender: 'Male',
      pancard_no: '',
      mealPreference: 'veg',
      pancard :"",
      passport :""
    },
    {
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      passportNumber: '',
      passportIssueDate: '',
      passportExpiryDate: '',
      nationality: 'India',
      // passportIssueCity: '',
      gender: 'Male',
      pancard_no: '',
      mealPreference: 'veg',
      pancard :"",
      passport :""
    }
  ]);
  const [date,setDate] = useState("");
  const [update_pan_card,set_update_pan_card] = useState(false);
  const [promoCode, setPromoCode] = useState('');
  const [isPromoApplied, setIsPromoApplied] = useState(false);
  const [promoData, setPromoData] = useState<any>({});
  const [isDisbaled,setIsDisabled] = useState(true);
  const [promoError, setPromoError] = useState<string | null>(null);
  const [promoLoading, setPromoLoading] = useState(false);
  const [packageData, setPackageData] = useState<any>(null);
  const [errorMessage,setErrormessage] = useState<any>({
    travelling_date:""
  });
  const upload_document =async(index:any,field:any)=>{
    if(!fileUpload[`${index}`]){
      fileUpload[`${index}`] ={}
    }
    setFileUpload((prev:any)=>{
      const data: any = { ...prev }; // shallow clone of outer object
      const inner = { ...(data[index] || {}) }
      inner[`loading_${field}`] = true;
      data[index] = inner;
      return data
    });
    const formData = new FormData();
    formData.append('images', fileUpload);
    try {
      const response = await axios.post(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/uploadImages/upload-document?authtoken=${localStorage.getItem('authToken')}`,formData,{
        headers: {
          'Content-Type': 'multipart/form-data',
          // 'authtoken': `Bearer ${localStorage.getItem('authToken')}`,
        }
      });
        
      if(response.status  === 200){
        setTravellers((prev:any)=>{
          let data =JSON.parse(JSON.stringify(prev));
          data[index][`${field}`] = response.data[0];
          return prev
        })
        // return response.data[0];
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      return "";
    } finally{
       setFileUpload((prev:any)=>{
      const data: any = { ...prev }; // shallow clone of outer object
      const inner = { ...(data[index] || {}) }
      inner[`loading_${field}`] = false;
      data[index] = inner;
      return data
    });
    }
  }
  useEffect(() => {
    // Fetch package data when component mounts
    const fetchPackageData = async () => {
      const searchParams = new URLSearchParams(window.location.search);
       const custom = searchParams.get('custom');
       const API_END_POINT = custom=="true" ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/packages/${bookingData.packageCode}` : `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/packages/website/${bookingData.packageCode}`
      try {
        const response = await fetch(`${API_END_POINT}`);
        const data = await response.json();
        setPackageData(data);
      } catch (error) {
        console.error('Error fetching package data:', error);
      }
    };

    if (bookingData.packageCode) {
      fetchPackageData();
    }
  }, [bookingData.packageCode]);

  const addTraveller = () => {
    setTravellers([...travellers, {
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      passportNumber: '',
      passportIssueDate: '',
      passportExpiryDate: '',
      nationality: 'India',
      // passportIssueCity: '',
      gender: 'Male',
      pancard_no: '',
      mealPreference: 'veg',
      pancard :"",
      passport :""
    }]);
  };

  const removeTraveller = (index: number) => {
    setTravellers(travellers.filter((_, i) => i !== index));
  };

  const updateTraveller = (index: number, field: keyof TravellerInfo, value: string) => {
    const newTravellers = [...travellers];
    newTravellers[index] = {
      ...newTravellers[index],
      [field]: value
    };
    setTravellers(newTravellers);
  };

  const handlePromoCode = async (cuponcode_passed:string='') => {
    if (isPromoApplied) {
      setPromoCode('');
      setIsPromoApplied(false);
      setPromoError(null);
      return;
    }

    if (!promoCode.trim() && cuponcode_passed!='') {
      setPromoError('Please enter a promo code');
      return;
    }

    if (!packageData.packageCode) {
      setPromoError('Package code not found');
      return;
    }

    try {
      setPromoLoading(true);
      setPromoError(null);
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/coupons/apply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          couponCode: cuponcode_passed?cuponcode_passed:promoCode,
          // packageCode: bookingData.packageCode,
          packageCode: packageData.packageCode,
          price: price_to_show,
        })
      });

      const data = await response.json();

      console.log(data);

      if (response.ok) {
        setIsPromoApplied(true);
        setPromoData(data);
        setPromoError(null);
      } else {
        setPromoError(data.message || 'Invalid promo code');
        setIsPromoApplied(false);
      }
    } catch (error) {
      setPromoError('Failed to validate promo code. Please try again.');
      setIsPromoApplied(false);
    } finally {
      setPromoLoading(false);
    }
  };
  const applyCodeWhenReturned = async (code:string)=>{
     if (isPromoApplied) {
      setPromoCode('');
      setIsPromoApplied(false);
      setPromoError(null);
      return;
    }

    if (!code) {
      setPromoError('Please enter a promo code');
      return;
    }
    let package_test =packageData;
    if(!package_test || price_to_show_without_promo ==0){
      return;
    }
    if (!package_test.packageCode) {
      setPromoError('Package code not found');
      return;
    }
    try {
      setPromoLoading(true);
      setPromoError(null);
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/coupons/apply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          couponCode: code,
          // packageCode: bookingData.packageCode,
          packageCode: packageData.packageCode,
          // price: price_to_show,
          price: price_to_show_without_promo,
        })
      });
      const data = await response.json();

      console.log(data);

      if (response.ok) {
        setIsPromoApplied(true);
        setPromoData(data);
        setPromoError(null);
      } else {
        setPromoError(data.message || 'Invalid promo code');
        setIsPromoApplied(false);
      }
    } catch (error) {
      setPromoError('Failed to validate promo code. Please try again.');
      setIsPromoApplied(false);
    } finally {
      setPromoLoading(false);
    }
  }
  const handleConfirmBooking = () => {
    const test = packageData;
    console.log("packageData",packageData);
    setCuponApplied(isPromoApplied);
    if (isPromoApplied) {
      setCuponData({
      code: promoCode,
      discout_price : price_to_show_without_promo - price_to_show,
      finalPrice : promoData?.finalPrice,

      // discount: 20,
      });
    } else {
      setCuponData({});
    }
    setBookingData({travellers,date,rule:bookingData?.rule || null, packageData:packageData});
    const searchParams = new URLSearchParams(window.location.search);
    const custom = searchParams.get('custom');
    let url_redirect =  custom=="true"? "/tours/travellers/checkout?custom=true":'/tours/travellers/checkout'
    router.push(url_redirect);
  };

  const fetch_country =async ()=>{
  // const country_data = await fetch("https://countriesnow.space/api/v0.1/countries/");
  // const data = await country_data.json();  // parse JSON
  // console.log("country_data", data.data);
  // setCountries(data.data);
  const data = phone_code.map((ele)=>{
      return{
        country:ele?.country_en,
        country_code:ele?.country_code,
      }
    }).sort((a,b)=>a.country?.toLowerCase().localeCompare(b.country?.toLowerCase()))
    setCountries(data);
  }
  useEffect(()=>{
     
    const empty = travellers.some((element:any)=>{

      return Object.keys(element).some((innerElement:any)=>{
        if(innerElement=="pancard" || innerElement =="passport"){
          return false;
        }
        if( innerElement =="pancard_no" && (element[innerElement] == "" || element[innerElement] == null) && element.nationality.toLowerCase() =="india"){
          return true;
        } 
        if(innerElement =="pancard_no" &&(element[innerElement] == "" || element[innerElement] == null) ){
          return false;
        }
        return element[innerElement] == "" || element[innerElement] == null;
      })
    })
    const hasAnyError = Object.keys(errorMessage).some((key) => {
      const value = errorMessage[key];
    
      if (typeof value === "object" && value !== null) {
        return Object.values(value).some((v) => v !== "");
      }
    
      return value !== "";
    });
    
    // console.log(errorMessage);
    if((!date && packageData?.itineraryType?.toLowerCase()!=="final" && packageData?.itineraryType?.toLowerCase()!=="quotation")|| (date ==""&& packageData?.itineraryType?.toLowerCase()!=="final" && packageData?.itineraryType?.toLowerCase()!=="quotation") || empty || hasAnyError){
      setIsDisabled(true);
    } else {
      setIsDisabled(false);
    }
  },[travellers,date])

  useEffect(()=>{
    console.log("bookingData",bookingData);
    fetch_country();
    bookingData.travellers.length>0 && setTravellers(bookingData.travellers);
    bookingData.date && setDate(bookingData.date);
    const test = bookingData?.packageData;
    // if(cuponApplied){
    //   setPromoCode(cuponData?.code);
    //   let code = cuponData?.code;
    //   applyCodeWhenReturned(code);
    // }
    if(bookingData?.packageData){
      setPackageData(bookingData.packageData);
    } else if(!bookingData.packageCode){
      window.location.href = "/";
      setCuponData({});
      setCuponApplied(false);
    }
    set_rerender_price(!rerender_price);
    
  },[])
  useEffect(()=>{
    if(cuponApplied){
      setPromoCode(cuponData?.code);
      let code = cuponData?.code;
      applyCodeWhenReturned(code);
    }
    let url = `/tours/package-details?query=${packageData?.packageCode}`;
    // let url = `/?query=${packageData?.packageCode}`;
    set_redirect_to_url(url);
  },[packageData,price_to_show_without_promo])
  useEffect(()=>{
    const is_custom = typeof window !== "undefined" && new URLSearchParams(window.location.search).get('custom') === 'true';
    let price_to_show_without_promo_state = 0;
     if(!bookingData?.rule){
        if(!is_custom){
          let total = (packageData?.priceSummary?.netSellingPrice)*(travellers.length);
          set_price_to_show_without_promo(total);
          price_to_show_without_promo_state=total;
        } else{
          const total = (packageData?.priceSummary?.netSellingPrice / packageData?.noOfAdults)*travellers.length;
          set_price_to_show_without_promo(total);
          price_to_show_without_promo_state=total;

        }
      } else{
        if(!is_custom){
          let total = (packageData?.priceSummary?.netSellingPrice)*(travellers.length);
          total = total -(total*(bookingData?.rule?.discountPercentage/100))
          set_price_to_show_without_promo(total);
          price_to_show_without_promo_state=total;

        } else{
          let total = (packageData?.priceSummary?.netSellingPrice / packageData?.noOfAdults)*travellers.length;
          total = total -(total*(bookingData?.rule?.discountPercentage/100));
          set_price_to_show_without_promo(total);
          price_to_show_without_promo_state=total;

        }
      }
    if(isPromoApplied){
      if(!bookingData?.rule){
        if(!is_custom){
          let total = (packageData?.priceSummary?.netSellingPrice)*(travellers.length);
          set_price_to_show(total-(price_to_show_without_promo_state - promoData?.finalPrice));
        } else{
          const total = (packageData?.priceSummary?.netSellingPrice / packageData?.noOfAdults)*travellers.length;
          set_price_to_show(total-(price_to_show_without_promo_state - promoData?.finalPrice));
        }
      } else{
        if(!is_custom){
          let total = (packageData?.priceSummary?.netSellingPrice)*(travellers.length);
          total = total -(total*(bookingData?.rule?.discountPercentage/100))
          set_price_to_show(total-(price_to_show_without_promo_state - promoData?.finalPrice));
        } else{
          let total = (packageData?.priceSummary?.netSellingPrice / packageData?.noOfAdults)*travellers.length;
          total = total -(total*(bookingData?.rule?.discountPercentage/100));
          set_price_to_show(total-(price_to_show_without_promo_state - promoData?.finalPrice));
        }
      }
    } else{
      if(!bookingData?.rule){
        if(!is_custom){
          let total = (packageData?.priceSummary?.netSellingPrice)*(travellers.length);
          set_price_to_show(total);
        } else{
          const total = (packageData?.priceSummary?.netSellingPrice / packageData?.noOfAdults)*travellers.length;
          set_price_to_show(total);
        }
      } else{
        if(!is_custom){
          let total = (packageData?.priceSummary?.netSellingPrice)*(travellers.length);
          total = total -(total*(bookingData?.rule?.discountPercentage/100))
          set_price_to_show(total);
        } else{
          let total = (packageData?.priceSummary?.netSellingPrice / packageData?.noOfAdults)*travellers.length;
          total = total -(total*(bookingData?.rule?.discountPercentage/100));
          set_price_to_show(total);
        }
      }
    }
  },[packageData,rerender_price,isPromoApplied])

  useEffect(()=>{
    if(update_pan_card){
      set_update_pan_card(false);
       
      setTravellers((prev)=>{
        let data:any = [...prev];
        data = data.map((ele:any)=>{
          if(ele.nationality.toLowerCase() !=="india"){
            ele.pancard ="";
            ele.pancard_no = "";
          }
          return ele;
        })
        return data
      })
    }
  },[update_pan_card])
 
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-[0]">
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Left Column - Traveller Information */}
        <div className="flex-1">
          {/* Title and Travelling Date */}
          <div className="mb-8">
            {/* <a href={`${process.env.NEXT_SITE_URL}/tours/package-details?query=${packageData?.packageCode}`} target='_blank'> */}
            <a href={redirect_to_url} target='_blank'>
              <h1 className="text-[30px] font-bold text-[#1E1E1E] mb-3" >
                {packageData?.packageTitle}
              </h1>
            </a>
            <div className="flex flex-wrap mb-4 gap-[40px]">
              {packageData?.hotelIncluded &&
              <div className="flex items-center gap-2">
                <img src="/assets/hotels_included.svg" alt="Hotels" className="h-[19.10262680053711px] w-[21px]" />
                <span className="font-[700] text-[14px] text-[#000000] font-[Manrope] ">Hotels included</span>
              </div>}

              {packageData?.transferIncluded &&
              <div className="flex items-center gap-2">
                <img src="/assets/transfer_included.svg" alt="Transfers" className="h-[28px] w-[28px]" />
                <span className="font-[700] text-[14px] text-[#000000] font-[Manrope] ">Transfers included</span>
              </div>}

              {packageData?.activityIncluded && 
              <div className="flex items-center gap-2">
                <img src="/assets/activites_included.svg" alt="Activities" className="h-[28px] w-[28px]" />
                <span className="font-[700] text-[14px] text-[#000000] font-[Manrope] ">Activities included</span>
              </div>}
              {packageData?.flightIncluded && 
              <div className="flex items-center gap-2">
                {/* <img src="/assets/activites_included.svg" alt="Activities" className="h-[28px] w-[28px]" /> */}
                <img src="/assets/transfer_included.svg" alt="Transfers" className="h-[28px] w-[28px]" />
                <span className="font-[700] text-[14px] text-[#000000] font-[Manrope] ">Flights included</span>
              </div>}
            </div>
            <div className="flex flex-wrap gap-2">
              <span className="px-3 py-1 bg-gray-100 rounded-full text-[12px] text-[#344054] font-medium">Adventure</span>
              <span className="px-3 py-1 bg-gray-100 rounded-full text-[12px] text-[#344054] font-medium">Culture</span>
              <span className="px-3 py-1 bg-gray-100 rounded-full text-[12px] text-[#344054] font-medium">Honeymoon</span>
              <span className="px-3 py-1 bg-gray-100 rounded-full text-[12px] text-[#344054] font-medium">Western Europe</span>
            </div>
          </div>

          {/* Travelling Date Section */}
          {(packageData && packageData?.itineraryType?.toLowerCase()!=="final" && packageData?.itineraryType?.toLowerCase()!=="quotation") && <div className="mb-8 mt-[48px]">
            <div className="bg-blue-50 p-4 rounded-xl relative">
              <div className="absolute -top-3 left-4">
                {/* <span className="text-sm font-bold text-gray-900 bg-blue-100 px-3 py-1 rounded-xl"> */}
                <span className="text-base font-bold text-[#000000] bg-blue-100 px-3 py-1 rounded-md">
                  Travelling Date
                </span>
              </div>
            <div className="space-y-2 mt-3">
                <div className="text-sm text-[#344054] font-medium">
                  When are you planning to travel
                </div>
                <div className="flex items-center gap-2">
                  <CustomDateInput
                   value ={date}
                   minDate={today} 
                   error_message={errorMessage.travelling_date}
                  // onChange ={(e:any)=>setDate(e.target.value)}
                  // disabled={bookingData?.rule?true:false}
                  disabled={packageData?.applicableDiscountRules.length>0?true:false}
                  onChange={(date:any) => {
                    // const selectedDate = e.target.value;
                    const selectedDate = date;
                    setDate(selectedDate);
                    const selected = new Date(selectedDate).setHours(0, 0, 0, 0);
                    const todayOnly = new Date().setHours(0, 0, 0, 0);
                    if (selected >= todayOnly) {
                      setErrormessage((prev:any)=>({
                        ...prev,
                        travelling_date:""
                      }))
                    } else{
                       setErrormessage((prev:any)=>({
                        ...prev,
                        travelling_date:"Please select a valid date"
                      }))
                    }
                  }}
                  />
                  {/* <input
                    type="date"
                    className="border rounded-lg px-2.5 py-1 text-xs text-[#344054] bg-white w-[214px] h-[44px]"
                    placeholder="When are you planning to travel"
                    value ={date}
                    min={today} 
                    onChange ={(e)=>setDate(e.target.value)}
                    // height: 44px;
                    // width: 214px;
                  /> */}
                  <span className="text-xs text-gray-500 font-normal">Prices may differ based on your travel dates.</span>
                </div>
              </div>
            </div>
          </div>}

          {/* Traveller Information */}
          <div>
            <h2 className="text-sm font-bold text-gray-900 mb-4">
              Please enter the traveller information below
            </h2>
            
            {travellers.map((traveller, index) => (
              <div key={index} className="mb-8 bg-blue-50 rounded-xl p-6 relative mt-[48px]">
                <div className="absolute -top-3 left-4">
                  <span className="text-base font-bold text-[#000000] rounded-md  bg-blue-100 px-3 py-1 ">
                    Traveller {index + 1}
                  </span>
                </div>
                {index > 1 && (
                  <div className="absolute top-[-19px] right-4">
                    <button
                      onClick={() => removeTraveller(index)}
                      className="text-red-600 hover:text-red-700 p-1.5 rounded-full text-sm font-medium"
                    >
                      <img src="/assets/delete_icon.svg" className="w-[24px] h-[26px]"/>
                    </button>
                  </div>
                )}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-3">
                  {/* First Name */}
                  <div>
                    <label className="text-sm font-medium block text-[#344054] mb-1">
                      *First Name
                    </label>
                    <input
                      type="text"
                      value={traveller.firstName}
                      // const alphanumericValue = e.target.value.replace(/[^a-zA-Z0-9]/g, '');
                      //   updateTraveller(index, 'passportNumber', alphanumericValue);
                      onChange={(e) =>{
                        const alphaValue = e.target.value.replace(/[^a-zA-Z ]/g, '');
                        updateTraveller(index, 'firstName', alphaValue)
                      }}
                      className="w-full border rounded-lg px-3 py-2 text-base font-normal text-[#101828]"
                      style={{height:"44px",width:"214px"}}
                      required
                    />
                  </div>

                  {/* Last Name */}
                  <div>
                    <label className="block text-sm font-medium text-[#344054] mb-1">
                      *Last Name
                    </label>
                    <input
                      type="text"
                      value={traveller.lastName}
                      onChange={(e) => {
                        const alphaValue = e.target.value.replace(/[^a-zA-Z ]/g, '');
                        updateTraveller(index, 'lastName',alphaValue)
                      }}
                      className="w-full border rounded-lg px-3 py-2 text-base font-normal text-[#101828]"
                      style={{height:"44px",width:"214px"}}
                      required
                    />
                  </div>

                  {/* Date of Birth */}
                  <div>
                    <label className="block text-sm font-medium text-[#344054] mb-1">
                      *Date of Birth
                    </label>
                    <CustomDateInput
                      value={traveller.dateOfBirth}
                      // onChange={(e:any) => updateTraveller(index, 'dateOfBirth', e.target.value)}
                      maxDate={today} // disable future dates
                      required
                      height={"44px"}
                      width={"214px"}
                      error_message={errorMessage[`traveller_${index}`]?.dateOfBirth||""}
                      onChange={(date:any) => {
                    // const selectedDate = e.target.value;
                    const selectedDate = date;
                          updateTraveller(index, 'dateOfBirth', selectedDate);
                          const selected = new Date(selectedDate).setHours(0, 0, 0, 0);
                          const todayOnly = new Date().setHours(0, 0, 0, 0);
                          if (selected <= todayOnly) {
                            setErrormessage((prev:any)=>{
                              const key_for_obj = `traveller_${index}`;
                              const obj = {...prev};
                              if(obj[key_for_obj]){
                                obj[key_for_obj].dateOfBirth=""
                              } else{
                                obj[key_for_obj]={
                                  dateOfBirth:""
                                }
                              }
                                return obj
                            })
                          } else{
                            setErrormessage((prev:any)=>{
                              const key_for_obj = `traveller_${index}`;
                              const obj = {...prev};
                              if(obj[key_for_obj]){
                                obj[key_for_obj].dateOfBirth="Please select a valid date"
                              } else{
                                obj[key_for_obj]={
                                  dateOfBirth:"Please select a valid date"
                                }
                              }
                                return obj
                            })
                          }
                        }}
                      />
                    {/* <input
                      type="date"
                      value={traveller.dateOfBirth}
                      onChange={(e) => updateTraveller(index, 'dateOfBirth', e.target.value)}
                      className="w-full border rounded-lg px-3 py-2 text-base font-normal text-[#101828]"
                      max={today} // disable future dates
                      required
                    /> */}
                  </div>

                  {/* Passport Number */}
                  <div>
                    <label className="block text-sm font-medium text-[#344054] mb-1">
                      *Passport Number
                    </label>
                    <input
                      type="text"
                      value={traveller.passportNumber}
                      // onChange={(e) => updateTraveller(index, 'passportNumber', e.target.value)}
                      onChange={(e) => {
                        const alphanumericValue = e.target.value.replace(/[^a-zA-Z0-9]/g, '');
                        updateTraveller(index, 'passportNumber', alphanumericValue);
                      }}

                      className="w-full border rounded-lg px-3 py-2 text-base font-normal text-[#101828]"
                      style={{height:"44px",width:"214px"}}
                      required
                    />
                  </div>

                  {/* Passport Issue Date */}
                  <div>
                    <label className="block text-sm font-medium text-[#344054] mb-1">
                      *Passport Issue Date
                    </label>
                    <CustomDateInput
                      value={traveller.passportIssueDate}
                      // onChange={(e:any) => updateTraveller(index, 'passportIssueDate', e.target.value)}
                      maxDate={today}
                      required
                      height={"44px"}
                      width={"214px"}
                      error_message={errorMessage[`traveller_${index}`]?.passportIssueDate||""}
                      onChange={(date:any) => {
                    // const selectedDate = e.target.value;
                    const selectedDate = date;
                          updateTraveller(index, 'passportIssueDate', selectedDate);
                          const selected = new Date(selectedDate).setHours(0, 0, 0, 0);
                          const todayOnly = new Date().setHours(0, 0, 0, 0);
                           if (selected <= todayOnly) {
                            setErrormessage((prev:any)=>{
                              const key_for_obj = `traveller_${index}`;
                              const obj = {...prev};
                              if(obj[key_for_obj]){
                                obj[key_for_obj].passportIssueDate=""
                              } else{
                                obj[key_for_obj]={
                                  passportIssueDate:""
                                }
                              }
                                return obj
                            })
                          } else{
                            setErrormessage((prev:any)=>{
                              const key_for_obj = `traveller_${index}`;
                              const obj = {...prev};
                              if(obj[key_for_obj]){
                                obj[key_for_obj].passportIssueDate="Please select a valid date"
                              } else{
                                obj[key_for_obj]={
                                  passportIssueDate:"Please select a valid date"
                                }
                              }
                                return obj
                            })
                          }
                        }}
                      />
                  </div>

                  {/* Passport Expiry Date */}
                  <div>
                    <label className="block text-sm font-medium text-[#344054] mb-1">
                      *Passport Expiry Date
                    </label>
                    <CustomDateInput
                      value={traveller.passportExpiryDate}
                      // onChange={(e:any) => updateTraveller(index, 'passportExpiryDate', e.target.value)}
                      minDate={today}
                      required
                      height={"44px"}
                      width={"214px"}
                      error_message={errorMessage[`traveller_${index}`]?.passportExpiryDate||""}
                       onChange={(date:any) => {
                         
                    // const selectedDate = e.target.value;
                    const selectedDate = date;
                          updateTraveller(index, 'passportExpiryDate',selectedDate);
                           const selected = new Date(selectedDate).setHours(0, 0, 0, 0);
                            const todayOnly = new Date().setHours(0, 0, 0, 0);

                              if (selected >= todayOnly) {
                          // if (selectedDate >= today) {
                            setErrormessage((prev:any)=>{
                              const key_for_obj = `traveller_${index}`;
                              const obj = {...prev};
                              if(obj[key_for_obj]){
                                obj[key_for_obj].passportExpiryDate=""
                              } else{
                                obj[key_for_obj]={
                                  passportExpiryDate:""
                                }
                              }
                                return obj
                            })
                          } else{
                            setErrormessage((prev:any)=>{
                              const key_for_obj = `traveller_${index}`;
                              const obj = {...prev};
                              if(obj[key_for_obj]){
                                obj[key_for_obj].passportExpiryDate="Please select a valid date"
                              } else{
                                obj[key_for_obj]={
                                  passportExpiryDate:"Please select a valid date"
                                }
                              }
                                return obj
                            })
                          }
                        }}
                      />
                  </div>

                  {/* Nationality */}
                  <div>
                    <label className="block text-sm font-medium text-[#344054] mb-1">
                      *Nationality
                    </label>
                    <select
                      value={traveller.nationality}
                      onChange={(e) => {
                        if(e.target.value.toLowerCase() !=="india"){
                          set_update_pan_card(true);
                          // updateTraveller(index, 'pancard_no', "");
                        }
                        updateTraveller(index, 'nationality', e.target.value);
                      }}
                      className="w-full border rounded-lg px-3 py-2 text-base font-normal text-[#101828]"
                      style={{height:"44px",width:"214px"}}
                      required
                    >
                      {/* <option value="India">India</option> */}
                      {
                        countries.map((ele:any)=><option key={`${ele.country}-key`} value={ele.country}>{ele.country}</option>)
                      }
                      {/* Add more nationality options as needed */}
                    </select>
                  </div>

                  {/* Gender */}
                  <div>
                    <label className="block text-sm font-medium text-[#344054] mb-1">
                      *Gender
                    </label>
                    <select
                      value={traveller.gender}
                      onChange={(e) => updateTraveller(index, 'gender', e.target.value)}
                      className="w-full border rounded-lg px-3 py-2 text-base font-normal text-[#101828]"
                      style={{height:"44px",width:"214px"}}
                      required
                    >
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                    </select>
                  </div>
                  {/* Passport Number */}
                  {travellers[`${index}`].nationality.toLowerCase() == "india" && <div>
                    <label className="block text-sm font-medium text-[#344054] mb-1">
                      *Pancard Number
                    </label>
                    <input
                      type="text"
                      value={traveller.pancard_no}
                      // onChange={(e) => updateTraveller(index, 'passportNumber', e.target.value)}
                      onChange={(e) => {
                        const alphanumericValue = e.target.value.replace(/[^a-zA-Z0-9]/g, '');
                        updateTraveller(index, 'pancard_no', alphanumericValue);
                      }}

                      className="w-full border rounded-lg px-3 py-2 text-base font-normal text-[#101828]"
                      style={{height:"44px",width:"214px"}}
                      required
                    />
                  </div>}
                  {/* Passport Issue City */}
                  {/* <div>
                    <label className="block text-sm font-medium text-[#344054] mb-1">
                      *Passport Issue City
                    </label>
                    <input
                      type="text"
                      value={traveller.passportIssueCity}
                      onChange={(e) => {
                        const alphaValue = e.target.value.replace(/[^a-zA-Z ]/g, '');
                        updateTraveller(index, 'passportIssueCity', alphaValue)}}
                      className="w-full border rounded-lg px-3 py-2 text-base font-normal text-[#101828]"
                      style={{height:"44px",width:"214px"}}
                      max={today}
                      required
                    />
                  </div> */}
                  {/* <div></div> */}

                  {/* Meal Preference */}
                  {/* <div>
                    <label className="block text-sm font-medium text-[#344054] mb-1">
                      *Meal Preference
                    </label>
                    <div className="flex gap-4">
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          checked={traveller.mealPreference === 'veg'}
                          onChange={() => updateTraveller(index, 'mealPreference', 'veg')}
                          className="text-blue-600"
                        />
                        <span className="text-sm text-[#344054] font-medium">🥗 Veg</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          checked={traveller.mealPreference === 'non-veg'}
                          onChange={() => updateTraveller(index, 'mealPreference', 'non-veg')}
                          className="text-blue-600"
                        />
                        <span className="text-sm text-[#344054] font-medium">🍖 Non-Veg</span>
                      </label>
                    </div>
                  </div> */}  
                </div>
                <div className='flex justify-around gap-3 mt-4 '>
                  <div className='w-full'>
                    <label className="block text-sm font-medium text-[#344054] mb-1">
                      *Passport Document
                    </label>
                    <FileUploadComponent 
                    uploadedFileName = {fileUpload?.[index]?.passport_name || ""} 
                    setUploadedFileName = {(name:any)=>{
                      setFileUpload((prev:any)=>{
                        const data: any = { ...prev }; // shallow clone of outer object
                        const inner = { ...(data[index] || {}) }
                        inner["passport_name"] = name;
                        data[index] = inner;
                        return data
                        // const data:any ={...prev};
                        // if (!data[index]) {
                        //   data[index] = {};
                        // }
                        // data[`${index}`]["passport_name"] = name;
                        // return data
                      });
                    }}
                    fileUpload = {fileUpload?.[index]?.passport || null} 
                    setFileUpload = {(file:any)=>{
                      setFileUpload((prev:any)=>{
                        const data:any ={...prev};
                        if (!data[index]) {
                          data[index] = {};
                        }
                        data[`${index}`]["passport"] = file;
                        return data
                      });
                    }}
                    file_extensions_allowed={[".pdf"]} 
                    showPictureBeside={false}
                    showNameBelow={true}
                    className={"grow"}
                    id={`${index}_passport`}
                    upload_msg={"to upload scanned passport"}
                    loading={
                      fileUpload?.[index]?.loading_passport 
                    }

                    upload_document={()=>{
                      upload_document(index,"passport");
                    }}
                    uploaded={travellers[`${index}`].passport !==""}
                    />
                  </div>
                  {travellers[`${index}`].nationality.toLowerCase()=="india" &&
                   <div className='w-full'>
                    <label className="block text-sm font-medium text-[#344054] mb-1">
                      *Pancard Document
                    </label>
                    <FileUploadComponent 
                    uploadedFileName = {fileUpload?.[index]?.pancard_name || ""} 
                    setUploadedFileName = {(name:any)=>{
                      setFileUpload((prev:any)=>{
                        const data: any = { ...prev }; // shallow clone of outer object
                        const inner = { ...(data[index] || {}) }
                        inner["pancard_name"] = name;
                        data[index] = inner;
                        return data
                      });
                    }}
                    fileUpload = {fileUpload?.[index]?.pancard || null} 
                    setFileUpload = {(file:any)=>{
                      setFileUpload((prev:any)=>{
                        const data:any ={...prev};
                        if (!data[index]) {
                          data[index] = {};
                        }
                        data[`${index}`]["pancard"] = file;
                        return data
                      });
                    }}
                    file_extensions_allowed={[".pdf"]} 
                    showPictureBeside={false}
                    showNameBelow={true}
                    className={"grow"}
                    id={`${index}_pancard`}
                    upload_msg={"to upload scanned pancard"}
                    loading={
                      fileUpload?.[index]?.loading_pancard
                    }
                    uploaded={travellers[`${index}`].pancard !==""}
                    upload_document={()=>{
                      upload_document(index,"pancard")
                    }}
                  />
                  </div>}
                  </div>
              </div>
            ))}

            {/* Add Traveller Button */}
            <button
              onClick={addTraveller}
              className="flex items-center gap-2 text-[#175CD3] hover:text-blue-700  bg-[#EFF8FF] px-4 py-2 rounded-full text-sm font-semibold "
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
              </svg>
              Add Traveller
            </button>
          </div>
        </div>

        {/* Right Column - Price Summary */}
        <div className="lg:w-80">
          <div className="rounded-xl border border-gray-100 shadow-sm">
            <div className="bg-orange-50 rounded-t-xl p-4">
              <div className="mb-1">
                <span className="text-sm font-medium text-[#667085]">Amount Payable</span>
              </div>
              <div className="flex items-baseline gap-2">
                <span className="text-3xl font-bold text-[#000000]">{symbols[`${selectedCurrency}`]} {Math.round(convertFromINR(price_to_show)).toLocaleString()}</span>
                {isPromoApplied && (
                  // <span className="text-base font-medium text-[#98A2B3] line-through">{symbols[`${selectedCurrency}`]} {((Math.round((typeof window !== "undefined" && new URLSearchParams(window.location.search).get('custom') !== 'true')? convertFromINR(packageData?.priceSummary?.grossSellingPrice):convertFromINR((packageData?.priceSummary?.grossSellingPrice)/packageData?.noOfAdults)) || 82500) * travellers.length).toLocaleString()}</span>
                  <span className="text-base font-medium text-[#98A2B3] line-through">{symbols[`${selectedCurrency}`]} {Math.round(price_to_show_without_promo).toLocaleString()}</span>
                )}
              </div>
              <div className="text-xs font-medium text-[#667085]">Inclusive of all taxes</div>
            </div>

            <div className="p-4 space-y-4 bg-white">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-xs font-semibold text-[#667085]">{symbols[`${selectedCurrency}`]} {Math.round((typeof window !== "undefined" && new URLSearchParams(window.location.search).get('custom') !== 'true')? convertFromINR(packageData?.priceSummary?.netSellingPrice):convertFromINR((packageData?.priceSummary?.netSellingPrice)/packageData?.noOfAdults))?.toLocaleString() || '82,500'} x {travellers.length} PAX {bookingData?.rule && `- ${bookingData?.rule?.discountPercentage}%`}</span>
                  {/* <span className='text-xs font-semibold text-[#667085]'>₹{((Math.round((typeof window !== "undefined" && new URLSearchParams(window.location.search).get('custom') !== 'true')? packageData?.priceSummary?.netSellingPrice:(packageData?.priceSummary?.netSellingPrice)/packageData?.noOfAdults) || 82500) * travellers.length).toLocaleString()}</span> */}
                  <span className='text-xs font-semibold text-[#667085]'>{symbols[`${selectedCurrency}`]} {Math.round(price_to_show_without_promo).toLocaleString()}</span>
                </div>
                {isPromoApplied && (
                  <div className="flex justify-between text-sm">
                    <span className="text-xs font-semibold text-[#667085]">Discount <span className=" text-xs font-semibold text-[#12B76A]">({promoCode} Applied)</span></span>
                    <span className="text-[#667085] text-xs font-semibold">- {symbols[`${selectedCurrency}`]} {Math.round(price_to_show_without_promo - price_to_show).toLocaleString()}</span>
                  </div>
                )}
              </div>

              {/* Promo code section */}
              <div className="space-y-2">
                <label className="block text-[#344054] text-sm font-medium">Promotional code</label>
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <input
                      type="text"
                      value={promoCode}
                      onChange={(e) => {
                        setPromoCode(e.target.value);
                        setPromoError(null);
                      }}
                      className={`w-full border rounded-lg px-3 py-2 pr-8 text-gray-900 ${promoError ? 'border-red-500' : ''} ${isPromoApplied ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                      placeholder="Enter promo code"
                      disabled={isPromoApplied}
                    />
                    {promoCode && isPromoApplied && (
                      <div className="absolute right-2 top-1/2 -translate-y-1/2">
                        <img src="/assets/green_check.svg" className="w-[16px] h-[16px]"/>
                      </div>
                    )}
                  </div>
                  <button 
                    onClick={()=>{
                      handlePromoCode()
                    }}
                    disabled={promoLoading}
                    className={`text-[#175CD3] text-sm bg-blue-50 px-3 py-1 rounded-full font-semibold ${promoLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {promoLoading ? 'Applying...' : isPromoApplied ? 'Remove' : 'Apply'}
                  </button>
                </div>
                {promoError && (
                  <p className="text-red-500 text-sm mt-1">{promoError}</p>
                )}
              </div>

              <button
                onClick={handleConfirmBooking}
                disabled={isDisbaled}
                // className="w-full bg-blue-600 text-white py-3 rounded-full hover:bg-blue-700 font-medium"
                className={`w-full bg-blue-600 text-white py-3 rounded-full text-sm font-semibold ${isDisbaled?"opacity-70":"hover:bg-blue-700"}`}
              >
                Confirm Booking
              </button>
              {isDisbaled &&
                <p className='text-xs text-center text-red-800'>
                  Please fill all fields to continue
                </p>
              }
              <div className="text-xs font-medium text-[#475467] text-center">
                By confirming booking, you agree to our{' '}
                <a href="/pages/terms-conditions" target='_blank' className="text-xs hover:underline font-bold text-[#000000]">Terms & Conditions</a>
                {' '}·{' '}
                <a href="/pages/refund-policy" target='_blank' className="text-xs hover:underline font-bold text-[#000000]">Cancellation Policy</a>
              </div>
            </div>
          </div>

          <div className="mt-4 bg-blue-50 rounded-xl p-4">
            <h3 className=" text-[#000000] text-xl font-bold">Need help?</h3>
            <p className=" mt-1 text-base font-semibold text-[#000000]">
              We are ready to help you 24x7 365 days.
            </p>
            <div className="mt-4">
              <a
                href="tel:+919717559499"
                className="flex items-center justify-center gap-2 bg-blue-100  hover:text-blue-800 px-4 py-2 rounded-full
                text-base font-semibold text-[#175CD3]
                "
              >
                <img src="/assets/call_icon.svg" className='w-[20px] h-[20px]' />
                Call us at +91 97175 59499
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TravellerSection;
