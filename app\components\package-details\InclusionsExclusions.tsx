"use client";

import { useEffect, useState } from 'react';
import { PackageDetails } from '@/app/types/PackageDetails';

interface InclusionsExclusionsProps {
  packageData: PackageDetails;
  expandedSections?: string[];
  toggleSection?: (section: string) => void;
  from_download_pdf?:boolean;
}

const InclusionsExclusions: React.FC<InclusionsExclusionsProps> = ({
  packageData,
  expandedSections: externalExpandedSections,
  toggleSection: externalToggleSection,
  from_download_pdf=false
}) => {
  function isHTMLContent(str: string) {
    const doc = new DOMParser().parseFromString(str, 'text/html');
    return Array.from(doc.body.childNodes).some(node => node.nodeType === 1); // Node.ELEMENT_NODE
  }
  // Use internal state if external props are not provided
  const [internalExpandedSections, setInternalExpandedSections] = useState<string[]>(['inclusions', 'exclusions']);

  // Use either external or internal state/functions
  const expandedSections = externalExpandedSections || internalExpandedSections;

  // State for expanded inclusion/exclusion items
  const [expandedInclusionIdx, setExpandedInclusionIdx] = useState<any[]>([]);
  const [expandedExclusionIdx, setExpandedExclusionIdx] = useState<any[]>([]);

  const toggleSection = (section: string) => {
    if (externalToggleSection) {
      externalToggleSection(section);
    } else {
      setInternalExpandedSections((prev: string[]) =>
        prev.includes(section)
          ? prev.filter((s: string) => s !== section)
          : [...prev, section]
      );
    }
  };
 const filteredInclusion = packageData.inclusions
      .filter(inc => inc.included);
    const filteredExclusion =packageData.inclusions.filter(inc => inc.excluded);
  const handleInclusionExclusionAllAccordianView = ()=>{
    if((expandedInclusionIdx.length ==filteredInclusion.filter((x)=>!!x.desc).length) && (expandedExclusionIdx.length ==filteredExclusion.filter((x)=>!!x.desc).length) ){
      setExpandedInclusionIdx([]);
      setExpandedExclusionIdx([]);
    } else{
      const data = filteredInclusion.map((item, index) => {
        const hasDesc = !!item.desc;
        const expanded = expandedInclusionIdx.includes(index);
        if(hasDesc){
          return index;
        }
      }).filter((x)=>x);
      const exclusion_data = filteredExclusion.map((item, index) => {
         const hasDesc = !!item.desc;
        const expanded = expandedInclusionIdx.includes(index);
        if(hasDesc){
          return index;
        }
      }).filter((x)=>x);
      setExpandedInclusionIdx(data);
      setExpandedExclusionIdx(exclusion_data);
      
    }
  }
  useEffect(()=>{
    if(from_download_pdf){
      const data = filteredInclusion.map((item, index) => {
        const hasDesc = !!item.desc;
        const expanded = expandedInclusionIdx.includes(index);
        if(hasDesc){
          return index;
        }
      }).filter((x)=>x);
      const exclusion_data = filteredExclusion.map((item, index) => {
         const hasDesc = !!item.desc;
        const expanded = expandedInclusionIdx.includes(index);
        if(hasDesc){
          return index;
        }
      }).filter((x)=>x);
      setExpandedInclusionIdx(data);
      setExpandedExclusionIdx(exclusion_data);
    }
  },[])
  return (
    <div className="space-y-4 mt-6 sm:mt-8">
      <div className='flex flex-col md:flex-row justify-between items-center'>
        <div></div>
        <button type="button" className="text-sm sm:text-sm font-semibold text-blue-600 cursor-pointer"
        onClick={handleInclusionExclusionAllAccordianView}
        >
          {(expandedInclusionIdx.length ==filteredInclusion.filter((x)=>!!x.desc).length) && (expandedExclusionIdx.length ==filteredExclusion.filter((x)=>!!x.desc).length)? "Hide All":"Expand All"}
        </button>
      </div>
      <section id="inclusion" className="bg-green-50 rounded-xl p-3 sm:p-4">
        <button
          onClick={() => toggleSection('inclusions')}
          className="w-full flex items-center justify-between"
          aria-expanded={expandedSections.includes('inclusions')}
        >
          <h2 className="text-base sm:text-lg font-semibold text-gray-900">Inclusions</h2>
          <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full border-2 border-blue-700 flex items-center justify-center bg-white">
            {expandedSections.includes('inclusions') ? (
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 text-blue-700">
                <path strokeLinecap="round" strokeLinejoin="round" d="M18 12H6" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 text-blue-700">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m6-6H6" />
              </svg>
            )}
          </div>
        </button>
        {expandedSections.includes('inclusions') && 
        <div className={`mt-2 space-y-2 transition-all duration-300 ${expandedSections.includes('inclusions') ? '' : ' opacity-0 overflow-hidden'
          }`}>
          <ul className="space-y-2 text-sm sm:text-base">
            {packageData.inclusions
              .filter(inc => inc.included)
              .map((item, index) => {
                let test = packageData.inclusions;
                 
                const hasDesc = !!item.desc;
                const expanded = expandedInclusionIdx.includes(index);
                return (
                  <li key={index} className="flex flex-col gap-1">
                    <div className="flex items-center gap-2 justify-between">
                      <div className="flex items-center gap-2">
                        <svg className="h-4 w-4 text-green-600 flex-shrink-0 mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-gray-700 font-medium mt-0.5">{item.name}</span>
                      </div>
                      {hasDesc && (
                        <button
                          className="w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 border-blue-700 flex items-center justify-center bg-white ml-2"
                          // onClick={() => setExpandedInclusionIdx(expanded ? null : index)}
                          onClick={() => setExpandedInclusionIdx((prev:any)=>{
                            let data = [...prev];
                            if (expanded) {
                              return data.filter((i:any) => i !== index);
                            } else {
                              return [...data, index];
                            }

                          })}
                          aria-label={expanded ? 'Collapse description' : 'Expand description'}
                        >
                          {expanded ? (
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-3.5 h-3.5 text-blue-700">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M18 12H6" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-3.5 h-3.5 text-blue-700">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m6-6H6" />
                            </svg>
                          )}
                        </button>
                      )}
                    </div>
                    {hasDesc && expanded && (
                      isHTMLContent(item.desc) ? (
                          <div
                            className="ml-6 text-gray-500 text-xs sm:text-sm itinerary-content"
                            dangerouslySetInnerHTML={{ __html: item.desc }}
                          ></div>
                        ) : (
                          <div className="ml-6 text-gray-500 text-xs sm:text-sm itinerary-content">
                            {item.desc.split('\n').map((line, i) => (
                              <p key={i} className="mb-1">{line}</p>
                            ))}
                          </div>
                        )
                    )}
                  </li>
                );
              })}
          </ul>
        </div>}
      </section>

      {/* Exclusions */}
      <section id="exclusion" className="bg-red-50 rounded-xl p-3 sm:p-4">
        <button
          onClick={() => toggleSection('exclusions')}
          className="w-full flex items-center justify-between"
          aria-expanded={expandedSections.includes('exclusions')}
        >
          <h2 className="text-base sm:text-lg font-semibold text-gray-900">Exclusions</h2>
          <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full border-2 border-blue-700 flex items-center justify-center bg-white">
            {expandedSections.includes('exclusions') ? (
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 text-blue-700">
                <path strokeLinecap="round" strokeLinejoin="round" d="M18 12H6" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 text-blue-700">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m6-6H6" />
              </svg>
            )}
          </div>
        </button>
        {expandedSections.includes('exclusions') && <div className={`mt-2 space-y-2 transition-all duration-300 ${expandedSections.includes('exclusions') ? '' : ' opacity-0 overflow-hidden'
          }`}>
          <ul className="space-y-2 text-sm sm:text-base">
            {packageData.inclusions
              .filter(inc => inc.excluded)
              .map((item, index) => {
                const hasDesc = !!item.desc;
                const expanded = expandedExclusionIdx.includes(index);
                return (
                  <li key={index} className="flex flex-col gap-1">
                    <div className="flex items-center gap-2 justify-between">
                      <div className="flex items-center gap-2">
                        <svg className="h-4 w-4 text-red-600 flex-shrink-0 mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        <span className="text-gray-700 font-medium mt-0.5">{item.name}</span>
                      </div>
                      {hasDesc && (
                        <button
                          className="w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 border-blue-700 flex items-center justify-center bg-white ml-2"
                          // onClick={() => setExpandedExclusionIdx(expanded ? null : index)}
                          onClick={() => setExpandedExclusionIdx((prev)=>{
                            let data = [...prev];
                            if (expanded) {
                              return data.filter((i:any) => i !== index);
                            } else {
                              return [...data, index];
                            }
                          })}
                          aria-label={expanded ? 'Collapse description' : 'Expand description'}
                        >
                          {expanded ? (
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-3.5 h-3.5 text-blue-700">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M18 12H6" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={3} stroke="currentColor" className="w-3.5 h-3.5 text-blue-700">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m6-6H6" />
                            </svg>
                          )}
                        </button>
                      )}
                    </div>
                    {hasDesc && expanded && (
                      isHTMLContent(item.desc) ? (
                          <div
                            className="ml-6 text-gray-500 text-xs sm:text-sm itinerary-content"
                            dangerouslySetInnerHTML={{ __html: item.desc }}
                          ></div>
                        ) : (
                          <div className="ml-6 text-gray-500 text-xs sm:text-sm itinerary-content">
                            {item.desc.split('\n').map((line, i) => (
                              <p key={i} className="mb-1">{line}</p>
                            ))}
                          </div>
                        )
                    )}
                  </li>
                );
              })}
          </ul>
        </div>}
      </section>
    </div>
  );
};

export default InclusionsExclusions;
