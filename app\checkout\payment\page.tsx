// line number 474
"use client";

import { useState, Suspense, useEffect, useContext } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Layout from '@/app/components/Layout';
import axios from 'axios';
import crypto from 'crypto';
import { useCurrencyStore } from '@/app/store/useCurrencyStore';
import { AppContext } from '@/app/context/useAppContext';

// Currency symbols mapping
const symbols: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  INR: '₹',
  AUD: 'A$',
  CAD: 'C$',
  JPY: '¥',
  CNY: '¥',
  // Add more currencies as needed
};

// Debug log environment variables
console.log('Environment Variables:', {
  PAYSHARP_API_URL: process.env.NEXT_PUBLIC_PAYSHARP_API_URL,
  PAYSHARP_API_KEY: process.env.NEXT_PUBLIC_PAYSHARP_API_SECRET_KEY ? '***' + process.env.NEXT_PUBLIC_PAYSHARP_API_SECRET_KEY.slice(-4) : 'Not set'
});

// Paysharp API configuration
const PAYSHARP_API_KEY = process.env.NEXT_PUBLIC_PAYSHARP_API_SECRET_KEY;
const PAYSHARP_API_URL = process.env.NEXT_PUBLIC_PAYSHARP_API_URL;

if (!PAYSHARP_API_KEY || !PAYSHARP_API_URL) {
  const errorMsg = 'Missing required PaySharp API configuration. Please check your environment variables.';
  console.error(errorMsg);
  throw new Error(errorMsg);
}

// PayU API configuration
const PAYU_MERCHANT_KEY = process.env.NEXT_PUBLIC_PAYU_MERCHANT_KEY;
const PAYU_MERCHANT_SALT = process.env.NEXT_PUBLIC_PAYU_MERCHANT_SALT;
const PAYU_API_URL = process.env.NEXT_PUBLIC_PAYU_API_URL || 'https://test.payu.in/_payment';

// PayU Bank List with official bank codes
const PAYU_BANKS = [
  { code: 'SBI', name: 'State Bank of India' },
  { code: 'HDFC', name: 'HDFC Bank' },
  { code: 'ICICI', name: 'ICICI Bank' },
  { code: 'AXIS', name: 'Axis Bank' },
  { code: 'KOTAK', name: 'Kotak Mahindra Bank' },
  { code: 'YES', name: 'Yes Bank' },
  { code: 'PNB', name: 'Punjab National Bank' },
  { code: 'BOB', name: 'Bank of Baroda' },
  { code: 'IDBI', name: 'IDBI Bank' },
  { code: 'CANARA', name: 'Canara Bank' }
];

// Generate hash for PayU according to documentation
const generatePayUHash = (txnid: string, amount: string, productinfo: string, firstname: string, email: string) => {
  const hashString = `${PAYU_MERCHANT_KEY}|${txnid}|${amount}|${productinfo}|${firstname}|${email}|||||||||||${PAYU_MERCHANT_SALT}`;
  return crypto.createHash('sha512').update(hashString).digest('hex');
};

// Define user details interface
interface UserDetails {
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  customer_id?: number; // Make it optional since it might not be in all responses
}

// Helper to fetch user details from API
const fetchUserDetails = async (): Promise<UserDetails> => {
  try {
    const userData = localStorage.getItem('wy_user_data');
    const user = userData ? JSON.parse(userData) : null;
    const userId = user?.userId;
    
    if (!userId) throw new Error('User not logged in');
    
    const url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/users/${userId}`;
    const response = await axios.get(url);
    console.log(response.data);
    
    // Return user details with fallback values
    return {
      customer_name: response.data?.username || user?.username || 'Customer Name',
      customer_email: response.data?.email || user?.email || '<EMAIL>',
      customer_phone: response.data?.phone || user?.phone || '9999999999',
      customer_id: response.data?.id || user?.userId || 0,
    };
  } catch (err) {
    console.error('Failed to fetch user details:', err);
    // Try to get user data from localStorage as fallback
    try {
      const userData = localStorage.getItem('wy_user_data');
      const user = userData ? JSON.parse(userData) : null;
      return {
        customer_name: user?.username || 'Customer Name',
        customer_email: user?.email || '<EMAIL>',
        customer_phone: user?.phone || '9999999999',
      };
    } catch (e) {
      return {
        customer_name: 'Customer Name',
        customer_email: '<EMAIL>',
        customer_phone: '9999999999',
      };
    }
  }
};

// Helper function to validate UPI ID using PayU's VPA validation API
const validateUpiId = async (vpa: string): Promise<boolean> => {
  try {
    // Basic UPI ID format validation first
    const upiRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9]+$/;
    
    if (!vpa || !vpa.trim()) {
      console.log('UPI ID is empty');
      return false;
    }
    
    // Check if it matches the UPI ID format
    const isValidFormat = upiRegex.test(vpa);
    if (!isValidFormat) {
      console.log('Invalid UPI ID format');
      return false;
    }

    // Skip API validation in development if keys are not set
    if (process.env.NODE_ENV === 'development' && (!PAYU_MERCHANT_KEY || !PAYU_MERCHANT_SALT)) {
      console.warn('Skipping VPA validation in development mode due to missing keys');
      return true;
    }

    if (!PAYU_MERCHANT_KEY || !PAYU_MERCHANT_SALT) {
      console.error('PayU merchant key or salt is not configured');
      return false;
    }

    // Prepare the validation request
    const command = 'validateVPA';
    const hashString = `${PAYU_MERCHANT_KEY}|${command}|${vpa}|${PAYU_MERCHANT_SALT}`;
    const hash = crypto.createHash('sha512').update(hashString).digest('hex');
    
    const formData = new URLSearchParams();
    formData.append('key', PAYU_MERCHANT_KEY);
    formData.append('command', command);
    formData.append('var1', vpa);
    formData.append('hash', hash);

    // Log the validation attempt
    console.log(`Initiating VPA validation for: ${vpa}`);
    
    // Make the API request to PayU's VPA validation endpoint
    const response = await fetch('https://test.payu.in/merchant/postservice?form=2', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      body: formData.toString()
    });

    if (!response.ok) {
      const errorMsg = `PayU API request failed with status ${response.status}`;
      console.error(errorMsg);
      throw new Error(errorMsg);
    }

    const data = await response.json();
    
    // Log the full response for debugging
    console.log('PayU VPA validation response received:', data);
    
    // Check if VPA is valid
    if (data.status === 'SUCCESS' && data.isVPAValid === 1) {
      console.log('✅ VPA validation SUCCESSFUL:', {
        vpa: vpa,
        payerName: data.payerAccountName || 'Not available',
        isAutoPayEnabled: data.isAutoPayVPAValid === 1 ? 'Yes' : 'No'
      });
      return true;
    } else {
      console.error('❌ VPA validation FAILED:', {
        vpa: vpa,
        status: data.status,
        isVPAValid: data.isVPAValid,
        reason: 'Invalid VPA or validation failed',
        response: data
      });
      return false;
    }
  } catch (error) {
    console.error('UPI validation error:', error);
    // In case of API failure, we can choose to proceed with the payment
    // and let PayU handle the validation during the actual payment
    console.warn('Proceeding with payment despite validation error');
    return true;
  }
};

// UPI payment handler
const handleUpiPayment = async (
  upiId: string, 
  amount: string, 
  packageId: string,
  bookingId: string | number = 0,
  installmentId: string | number = 0
) => {
  // Convert to numbers if they're strings
  const numericBookingId = Number(bookingId);
  const numericInstallmentId = Number(installmentId);
  const searchParams = new URLSearchParams(window.location.search);
  try {
    // First validate the UPI ID
    console.log('Validating UPI ID before payment:', upiId);
    const isUpiValid = await validateUpiId(upiId);
    
    if (!isUpiValid) {
      const errorMsg = 'The UPI ID you entered is not valid. Please check and try again.';
      console.error('Payment aborted:', errorMsg);
      // throw new Error(errorMsg);
    }
    
    console.log('UPI validation successful, proceeding with payment initialization...');

    // Fetch user details
    const userDetails = await fetchUserDetails();
     const bookingId = searchParams?.get('bookingId') || '';
    const installmentId = searchParams?.get('installmentId') || '';
    // Initialize payment with our backend
    const paymentInitData: PaymentInitData = {
      packageId,
      customerId: userDetails.customer_id || 0,
      bookingId: +bookingId,
      customerEmail: userDetails.customer_email,
      installmentId: +installmentId,
      extra: packageId,
      paymentModeId:4
    };

    const payuParams = await initializePayment(paymentInitData);

    // Ensure we have a merchant key
    const merchantKey = payuParams.merchantKey || PAYU_MERCHANT_KEY;
    if (!merchantKey) {
      throw new Error('Merchant key is not configured');
    }

    // Generate transaction ID if not provided
    const txnId = payuParams.transactionId || `WY${Date.now()}`;
    const productInfo = payuParams.productName || `Package-${packageId}`;
    const firstName = userDetails.customer_name;
    const lastName = userDetails.customer_name.split(' ').slice(1).join(' ') || 'User';
    // const amountWithOneDecimal = Number(amount).toFixed(1);
    const amountWithOneDecimal = Number(amount).toFixed(2);
    
    // Prepare payment data for submission
    const paymentData: PaymentSubmissionData = {
      key: merchantKey,
      txnid: txnId,
      amount: amountWithOneDecimal,
      productinfo: productInfo,
      firstname: firstName,
      lastname: lastName,
      email: userDetails.customer_email,
      phone: userDetails.customer_phone,
      surl: `${window.location.origin}/api/payment/response`,
      furl: `${window.location.origin}/api/payment/response`,
      hash: payuParams.payUHash || '',
      
      // UPI specific fields
      pg: 'UPI',
      bankcode: 'UPI',
      vpa: upiId,
      
      // Additional parameters
      //service_provider: 'payu_paisa',
      // udf1: packageId,
      udf1: installmentId,
      // udf2: numericBookingId.toString(),
      // udf3: numericInstallmentId.toString(),
      udf2: bookingId,
      udf3: packageId.toString(),
      udf4: "",
      udf5: "",
      debug: '1',
    };
    // Submit the payment form
    submitPaymentForm(paymentData, payuParams.PAYU_API_URL || PAYU_API_URL);
    
  } catch (error) {
    console.error('UPI payment error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to initiate UPI payment';
    alert(errorMessage);
    throw error; // Re-throw to allow error handling by the caller
  }
};

// Interface for payment initialization data
interface PaymentInitData {
  packageId: string;
  customerId: number;
  bookingId: number;
  customerEmail: string;
  installmentId: number;
  extra: string;
  paymentModeId?:number;
}

// Interface for payment response data
interface PaymentResponseData {
  merchantKey?: string;
  transactionId?: string;
  payUHash?: string;
  productName?: string;
  PAYU_API_URL?: string;
  [key: string]: any;
}

// Interface for payment submission data
interface PaymentSubmissionData {
  key: string;
  txnid: string;
  hash: string;
  amount: any;
  productinfo: string;
  firstname: string;
  lastname: string;
  email: string;
  phone: string;
  surl: string;
  furl: string;
  [key: string]: any; // For additional payment method specific fields
}

/**
 * Initialize payment with the backend and get payment parameters
 */
async function initializePayment(
  paymentData: PaymentInitData,
): Promise<PaymentResponseData> {
  try {
    console.log('Initializing payment with data:', paymentData);

    const response = await axios.post<PaymentResponseData>(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/payment`,
      paymentData,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        timeout: 30000, // 30 seconds timeout
      }
    );

    if (!response.data || typeof response.data !== 'object') {
      throw new Error('Invalid response from payment gateway');
    }

    console.log('Received payment parameters:', response.data);
    return response.data;
  } catch (error) {
    console.error('Payment initialization error:', error);
    throw new Error(
      axios.isAxiosError(error)
        ? error.response?.data?.message || error.message
        : 'Failed to initialize payment'
    );
  }
}

/**
 * Submit payment form to the payment gateway
 */
function submitPaymentForm(
  paymentData: PaymentSubmissionData,
  gatewayUrl: string
): void {
  // Log payment data (masking sensitive information)
  console.log('Submitting payment with data:', {
    ...paymentData,
    ...(paymentData.ccnum && { ccnum: '•••• •••• •••• ' + paymentData.ccnum.slice(-4) }),
    ...(paymentData.ccvv && { ccvv: '•••' }),
    hash: paymentData.hash ? '•••' + paymentData.hash.slice(-8) : 'not generated',
  });

  // Create form
  const form = document.createElement('form');
  form.method = 'POST';
  form.action = gatewayUrl;
  form.target = '_self';
  form.style.display = 'none';

  // Add all fields to the form
  Object.entries(paymentData).forEach(([key, value]) => {
    if (value != null) {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = key;
      input.value = value.toString();
      form.appendChild(input);
    }
  });

  // Submit the form
  document.body.appendChild(form);
  console.log('Submitting form to:', form.action);
  form.submit();
}

// Card payment handler
const handleCardPayment = async (
  cardDetails: any,
  amount: string,
  packageId: string,
  bookingId: string | number,
  installmentId: string | number
) => {
  // Convert to numbers if they're strings
  const numericBookingId = Number(bookingId);
  const numericInstallmentId = Number(installmentId);
  
  // Validate they're valid numbers
  if (isNaN(numericBookingId) || isNaN(numericInstallmentId)) {
    throw new Error('Invalid booking ID or installment ID');
  }

  try {
    // Validate required fields
    if (!bookingId || !installmentId) {
      throw new Error('Booking ID and Installment ID are required');
    }

    // Validate card details
    if (!cardDetails?.cardNumber?.trim() || !cardDetails?.expiryDate?.trim() || 
        !cardDetails?.cvv?.trim() || !cardDetails?.cardName?.trim()) {
      throw new Error('Please fill in all card details');
    }

    // Validate expiry date format (MM/YY or MM/YYYY)
    const expiryMatch = cardDetails.expiryDate.match(/^(0[1-9]|1[0-2])\/(\d{2}|\d{4})$/);
    if (!expiryMatch) {
      throw new Error('Please enter a valid expiry date in MM/YY or MM/YYYY format');
    }

    // Fetch user details
    const userDetails = await fetchUserDetails();
    
    // Initialize payment with our backend
    const paymentInitData: PaymentInitData = {
      packageId,
      customerId: userDetails.customer_id || 0,
      bookingId: numericBookingId,
      customerEmail: userDetails.customer_email,
      installmentId: numericInstallmentId,
      extra: packageId,
      paymentModeId:1
    };

    const payuParams = await initializePayment(paymentInitData);
    debugger;
    // Ensure we have a merchant key
    const merchantKey = payuParams.merchantKey || process.env.NEXT_PUBLIC_PAYU_MERCHANT_KEY;
    if (!merchantKey) {
      throw new Error('Merchant key is not configured');
    }

    // Prepare payment data for submission
    const paymentData: PaymentSubmissionData = {
      key: merchantKey,
      txnid: payuParams.transactionId || `TXN${Date.now()}`,
      hash: payuParams.payUHash || '',
      // amount: Number(amount).toFixed(1),
      amount: Number(amount).toFixed(2),
      productinfo: payuParams.productName || `Package: ${packageId}`,
      firstname: userDetails.customer_name,
      lastname: cardDetails.cardName.split(' ').slice(1).join(' ') || 'Lastname',
      email: userDetails.customer_email,
      phone: userDetails.customer_phone,
      surl: `${window.location.origin}/api/payment/response`,
      furl: `${window.location.origin}/api/payment/response`,
      
      // Card specific fields
      ccnum: cardDetails.cardNumber.replace(/\s/g, ''),
      ccname: cardDetails.cardName,
      ccvv: cardDetails.cvv,
      ccexpmon: (() => {
        const [month] = cardDetails.expiryDate.split('/');
        return month ? month.padStart(2, '0') : '01';
      })(),
      ccexpyr: (() => {
        const parts = cardDetails.expiryDate.split('/');
        return parts.length > 1 ? parts[1].slice(-2) : '25';
      })(),
      
      // Additional parameters
      pg: 'CC',
      //service_provider: 'payu_paisa',
      udf1: numericInstallmentId.toString(),
      udf2: numericBookingId.toString(),
      udf3: packageId,
      udf4: "",
      udf5: "",
      debug: '1',
      //test: process.env.NODE_ENV === 'development' ? '1' : '0'
    };
    console.log('payment_data test',paymentData)
    // Submit the payment form
    submitPaymentForm(paymentData, payuParams.PAYU_API_URL || PAYU_API_URL);

  } catch (error) {
    console.error('Card payment error:', error);
    throw new Error(
      error instanceof Error 
        ? error.message 
        : 'Failed to process card payment'
    );
    // Rethrow for any error boundaries or further processing
    throw error;
  }
};

// Net Banking payment handler
// const handleNetBankingPayment = async (bankCode: string, amount: string, packageId: string) => {
//   try {
//     const userDetails = await fetchUserDetails();
//     const txnid = `TXN_${Date.now()}`;
//     const productinfo = `Payment for package ${packageId}`;
//     const firstname = userDetails.customer_name;
//     const email = userDetails.customer_email;
//     const phone = userDetails.customer_phone;

//     const hash = generatePayUHash(txnid, amount, productinfo, firstname, email);
//     // Create a form and submit it to PayU
//     const form = document.createElement('form');
//     form.method = 'POST';
//     if (!PAYU_API_URL) {
//       console.warn('PAYU_API_URL is undefined. Please check your environment variables.');
//     }
//     form.action = PAYU_API_URL || '';
//     form.target = '_self';

//     const formData = {
//       key: PAYU_MERCHANT_KEY || '',
//       txnid: txnid,
//       amount: amount,
//       productinfo: productinfo,
//       firstname: firstname,
//       email: email,
//       phone: phone,
//       surl: `${window.location.origin}/api/payment/response`,
//       furl: `${window.location.origin}/api/payment/response`,
//       hash: hash,
//       pg: 'NB',
//       bankcode: bankCode,
//       mode: 'NB'
//     };

//     // Add form fields
//     Object.entries(formData).forEach(([key, value]) => {
//       const input = document.createElement('input');
//       input.type = 'hidden';
//       input.name = key;
//       input.value = value;
//       form.appendChild(input);
//     });

//     // Add form to document and submit
//     document.body.appendChild(form);
//     form.submit();
//   } catch (error) {
//     console.error('Net banking payment error:', error);
//     alert('Failed to initiate net banking payment. Please try again.');
//   }
// };

const handleNetbankingPayment = async (
  bankCode: string, // e.g., 'SBIN' for SBI
  amount: string,
  packageId: string,
  bookingIdTest: string | number,
  installmentIdTest: string | number
) => {
   const searchParams = new URLSearchParams(window.location.search);
  const bookingId = searchParams?.get('bookingId') || '';
  const installmentId = searchParams?.get('installmentId') || '';
  const numericBookingId = Number(bookingId);
  const numericInstallmentId = Number(installmentId);

  if (isNaN(numericBookingId) || isNaN(numericInstallmentId)) {
    throw new Error('Invalid booking ID or installment ID');
  }

  try {
    if (!bankCode) {
      throw new Error('Bank code is required for netbanking');
    }

    const userDetails = await fetchUserDetails();

    const paymentInitData: PaymentInitData = {
      packageId,
      customerId: userDetails.customer_id || 0,
      bookingId: numericBookingId,
      customerEmail: userDetails.customer_email,
      installmentId: numericInstallmentId,
      extra: packageId,
      paymentModeId: 3 // Use 2 or appropriate enum/ID for Netbanking
    };
    const payuParams = await initializePayment(paymentInitData);

    const merchantKey = payuParams.merchantKey || process.env.NEXT_PUBLIC_PAYU_MERCHANT_KEY;
    if (!merchantKey) throw new Error('Merchant key is not configured');

    const paymentData: PaymentSubmissionData = {
      key: merchantKey,
      txnid: payuParams.transactionId || `TXN${Date.now()}`,
      hash: payuParams.payUHash || '',
      amount: +amount,
      productinfo: payuParams.productName || `Package: ${packageId}`,
      firstname: userDetails.customer_name,
      lastname: userDetails.customer_name.split(' ')[1],
      email: userDetails.customer_email,
      phone: userDetails.customer_phone,
      surl: `${window.location.origin}/api/payment/response`,
      furl: `${window.location.origin}/api/payment/response`,
      
      // Payment type = NetBanking
      pg: 'NB',
      bankcode: bankCode, // 👈 required for Net Banking
      
      udf1: numericInstallmentId.toString(),
      udf2: numericBookingId.toString(),
      udf3: packageId,
      udf4: "",
      udf5: "",
      debug: '1',
    };

    console.log('netbanking payment data', paymentData);
    submitPaymentForm(paymentData, payuParams.PAYU_API_URL || PAYU_API_URL);

  } catch (error) {
    console.error('Netbanking payment error:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to process netbanking payment');
  }
};


const PaymentPageContent = ({bookingDataNew}:any) => {
  const [paymentPlatformCharges, setPaymentPlatformCharges] = useState({
    credit_card: 0,
    net_banking: 0,
    upi: 0,
    neft: 0,
  });
  
  // Get currency state from store
  const { selectedCurrency, convertFromINR } = useCurrencyStore();
  const router = useRouter();
  const searchParams = useSearchParams();
  const current_installment = bookingDataNew?.bookingInstallmentsInfo?.find((ele:any)=>ele?.id ==searchParams.get('installmentId'));

  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  const [upiId, setUpiId] = useState('');
  const [upiError, setUpiError] = useState('');
  const [selectedBank, setSelectedBank] = useState('');
  const [cardDetails, setCardDetails] = useState({
    cardNumber: '',
    expiryDate: '',  // Changed from validTill to expiryDate
    cvv: '',
    cardName: ''    // Changed from fullName to cardName
  });
  
  // Get payment details from URL parameters with proper guards
  // const amount = searchParams?.get('amount') || '0';
  // const packageId = searchParams?.get('packageId') || '';
  // const title = searchParams?.get('title') || '';
  // const duration = searchParams?.get('duration') || '';
  const amount = current_installment?.amount;
  const packageId = bookingDataNew?.websitePackageInfo? bookingDataNew?.websitePackageInfo?.packageCode : bookingDataNew?.packageInfo?.packageCode ;
  const title = bookingDataNew?.websitePackageInfo? bookingDataNew?.websitePackageInfo?.packageTitle : bookingDataNew?.packageInfo?.packageTitle ;
  const duration = bookingDataNew?.websitePackageInfo? bookingDataNew?.websitePackageInfo?.noOfDays : bookingDataNew?.packageInfo?.noOfDays ;

  // Decode the title to show properly
  const decodedTitle = decodeURIComponent(title);
  
  // Add error handling for missing required parameters
  useEffect(() => {
    if (!searchParams) {
      console.error('Search params not available');
      return;
    }

    const bookingId = searchParams.get('bookingId');
    const installmentId = searchParams.get('installmentId');

    // if (!amount || amount === '0') {
    //   console.error('Amount is required');
    //   router.push('/checkout');
    //   return;
    // }

    // if (!packageId) {
    //   console.error('Package ID is required');
    //   router.push('/checkout');
    //   return;
    // }

    if (!bookingId) {
      console.error('Booking ID is required');
      router.push('/checkout');
      return;
    }

    if (!installmentId) {
      console.error('Installment ID is required');
      router.push('/checkout');
      return;
    }
  }, [searchParams, amount, packageId, router]);

  const handleMethodChange = (method: string) => {
    if (selectedMethod === method) {
      // Reset the respective form data when closing an accordion
      if (method === 'card') {
        setCardDetails({
          cardNumber: '',
          expiryDate: '',  // Changed from validTill to expiryDate
          cvv: '',
          cardName: ''    // Changed from fullName to cardName
        });
      } else if (method === 'upi') {
        setUpiId('');
      } else if (method === 'netbanking') {
        setSelectedBank('');
      }
      setSelectedMethod(null);
    } else {
      setSelectedMethod(method);
    }
  };

  const handleCardInputChange = (field: string, value: string) => {
    let formattedValue = value;

    if (field === 'cardNumber') {
      // Remove any non-digit characters
      const digitsOnly = value.replace(/\D/g, '');
      // Add hyphens after every 4 digits
      formattedValue = digitsOnly.replace(/(\d{4})(?=\d)/g, '$1-');
      // Limit to 19 characters (16 digits + 3 hyphens)
      formattedValue = formattedValue.slice(0, 19);
    } else if (field === 'expiryDate') {  // Changed from validTill to expiryDate
      // Remove any non-digit characters
      const digitsOnly = value.replace(/\D/g, '');
      // Add forward slash after 2 digits
      if (digitsOnly.length > 2) {
        formattedValue = `${digitsOnly.slice(0, 2)}/${digitsOnly.slice(2, 4)}`;
      } else {
        formattedValue = digitsOnly;
      }
      // Limit to 5 characters (MM/YY)
      formattedValue = formattedValue.slice(0, 5);
    }

    setCardDetails(prev => ({
      ...prev,
      [field]: formattedValue
    }));
  };

  const isCardFormValid = () => {
    // Remove hyphens and forward slash for validation
    const cardNumber = cardDetails.cardNumber.replace(/-/g, '');
    const expiryDate = cardDetails.expiryDate.replace('/', '');  // Changed from validTill to expiryDate
    
    return cardNumber.length === 16 &&
           expiryDate.length === 4 &&
           cardDetails.cvv.trim() !== '' &&
           cardDetails.cardName.trim() !== '';  // Changed from fullName to cardName
  };

  const handleCardSubmit = () => {
    if (!isCardFormValid()) {
      alert('Please fill in all card details');
      return;
    }
    const bookingId = searchParams?.get('bookingId') || '';
    const installmentId = searchParams?.get('installmentId') || '';
    
    if (!bookingId || !installmentId) {
      alert('Invalid booking details. Please try again.');
      router.push('/checkout');
      return;
    }
    
    handleCardPayment(
      cardDetails, 
      (Math.round(((parseFloat(amount) +
  (parseFloat(amount) * paymentPlatformCharges.credit_card) / 100) * 100))/100).toString(), 
      packageId,
      bookingId,
      installmentId
    );
  };

  const handleUpiSubmit = async () => {
    setUpiError('');
    
    if (!upiId) {
      setUpiError('Please enter a valid UPI ID');
      return;
    }
    
    // Basic UPI ID format validation
    const upiRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9]+$/;
    if (!upiRegex.test(upiId)) {
      setUpiError('Please enter a valid UPI ID (e.g., example@upi)');
      return;
    }
    
    try {
      // await handleUpiPayment(upiId, amount, packageId);
      await handleUpiPayment(upiId, Math.round(
                  parseFloat(amount) + (parseFloat(amount) * paymentPlatformCharges.upi) / 100
                ).toString(), packageId);
    } catch (error) {
      setUpiError(error instanceof Error ? error.message : 'Payment failed. Please try again.');
    }
  };
  
  useEffect(() => {
    // let test = bookingData;
    // console.log('bookingData',bookingData);
    const NEXT_PUBLIC_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

    const fetchPaymentModes = async () => {
      try {
        const response = await axios.get(`${NEXT_PUBLIC_API_BASE_URL}/api/payment-mode-fees/all`);
        const data = response.data;

        const updatedCharges = {
          credit_card: 0,
          net_banking: 0,
          upi: 0,
          neft: 0,
        };

        data.forEach((item:any) => {
          switch (item.paymentMode) {
            case 'CREDIT_CARD':
              // updatedCharges.credit_card = 0;
              updatedCharges.credit_card = item.feePercentage;;
              break;
            case 'NET_BANKING':
              // updatedCharges.net_banking = 0;
              updatedCharges.net_banking = item.feePercentage;
              break;
            case 'UPI':
              // updatedCharges.upi = 0;
              updatedCharges.upi = item.feePercentage;
              break;
            case 'NEFT':
              // updatedCharges.neft = 0;
              updatedCharges.neft = item.feePercentage;
              break;
            default:
              break;
          }
        });

        setPaymentPlatformCharges(updatedCharges);
      } catch (error) {
        console.error('Error fetching payment mode fees:', error);
      }
    };

    fetchPaymentModes();
  }, []);
  return (
    <div className="max-w-7xl mx-auto p-4 pb-52">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Left Column - Booking Details */}
        <div className="md:w-1/3">
          <h2 className="text-lg font-medium mb-4">Booking Details</h2>
          <div className="space-y-4 bg-gray-50 p-6 rounded-lg">
            <p className="text-sm text-gray-600">Package ID: {packageId}</p>
            <a href={`/tours/package-details?query=${packageId}${!bookingDataNew?.websitePackageInfo && "&custom=true"}`} target='_blank'><h3 className="text-lg font-medium">{decodedTitle}</h3></a>
            <p className="text-sm text-gray-600">{duration} {duration>1?'days':'day'}</p>
            <div className="mt-6">
              <p className="text-sm text-gray-600">Total Payable</p>
              <p className="text-3xl font-bold">{symbols[`${selectedCurrency}`]} {Math.round(convertFromINR(+amount)).toLocaleString()}</p>
              {
                selectedCurrency!="INR" &&
                <>
                  <p className='mt-2'>Payment will happen in INR which will be:</p>
                  <p>₹ {amount}</p>
                </>
              }
            </div>
            <div className="flex items-center gap-2 text-blue-600">
              
<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.0938 6.75L7.96873 10.6875L5.90625 8.71875M2.8125 7.5V3.375C2.8125 3.22582 2.87176 3.08274 2.97725 2.97725C3.08274 2.87176 3.22582 2.8125 3.375 2.8125H14.625C14.7742 2.8125 14.9173 2.87176 15.0227 2.97725C15.1282 3.08274 15.1875 3.22582 15.1875 3.375V7.5C15.1875 13.4073 10.1738 15.3645 9.1727 15.6964C9.06079 15.7349 8.93921 15.7349 8.8273 15.6964C7.8262 15.3645 2.8125 13.4073 2.8125 7.5Z" stroke="#175CD3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

              <span className="text-sm font-bold">100% Safe and Secure Payment</span>
            </div>
          </div>
        </div>

        {/* Right Column - Payment Methods */}
        <div className="md:w-2/3">
          <h2 className="text-lg font-medium mb-4">Select Payment Method</h2>
          
          {/* Debit/Credit Card Accordion */}
          <div className="mb-3">
            <div className={`border rounded-lg overflow-hidden ${selectedMethod === 'card' ? 'bg-blue-50 border-[#175CD3]' : 'bg-white border-gray-200 hover:border-[#175CD3]'}`}>
              <button 
                onClick={() => handleMethodChange('card')}
                className="w-full flex items-center justify-between p-4"
              >
                <div className="flex items-center gap-3">
                  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M28 6H4C3.46957 6 2.96086 6.21071 2.58579 6.58579C2.21071 6.96086 2 7.46957 2 8V24C2 24.5304 2.21071 25.0391 2.58579 25.4142C2.96086 25.7893 3.46957 26 4 26H28C28.5304 26 29.0391 25.7893 29.4142 25.4142C29.7893 25.0391 30 24.5304 30 24V8C30 7.46957 29.7893 6.96086 29.4142 6.58579C29.0391 6.21071 28.5304 6 28 6ZM28 8V11H4V8H28ZM28 24H4V13H28V24ZM26 21C26 21.2652 25.8946 21.5196 25.7071 21.7071C25.5196 21.8946 25.2652 22 25 22H21C20.7348 22 20.4804 21.8946 20.2929 21.7071C20.1054 21.5196 20 21.2652 20 21C20 20.7348 20.1054 20.4804 20.2929 20.2929C20.4804 20.1054 20.7348 20 21 20H25C25.2652 20 25.5196 20.1054 25.7071 20.2929C25.8946 20.4804 26 20.7348 26 21ZM18 21C18 21.2652 17.8946 21.5196 17.7071 21.7071C17.5196 21.8946 17.2652 22 17 22H15C14.7348 22 14.4804 21.8946 14.2929 21.7071C14.1054 21.5196 14 21.2652 14 21C14 20.7348 14.1054 20.4804 14.2929 20.2929C14.4804 20.1054 14.7348 20 15 20H17C17.2652 20 17.5196 20.1054 17.7071 20.2929C17.8946 20.4804 18 20.7348 18 21Z" fill="#175CD3"/>
                  </svg>
                  <span className={`font-bold ${selectedMethod === 'card' ? 'text-[#175CD3]' : ''}`}>Debit / Credit Card</span>
                </div>
                <svg className={`w-5 h-5 transition-transform ${selectedMethod === 'card' ? 'rotate-180 text-[#175CD3]' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {selectedMethod === 'card' && (
                <div className="p-4">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-1">Card Number</label>
                      <input
                        type="text"
                        value={cardDetails.cardNumber}
                        onChange={(e) => handleCardInputChange('cardNumber', e.target.value)}
                        placeholder="1234-5678-9012-3456"
                        className="w-full p-3 border rounded-lg text-sm"
                        maxLength={19}
                      />
                    </div>
                    <div className="flex gap-4">
                      <div className="w-1/2">
                        <label className="block text-sm font-bold text-gray-700 mb-1">Valid Till</label>
                        <input
                          type="text"
                          value={cardDetails.expiryDate}
                          onChange={(e) => handleCardInputChange('expiryDate', e.target.value)}
                          placeholder="MM/YY"
                          className="w-full p-3 border rounded-lg text-sm"
                          maxLength={5}
                        />
                      </div>
                      <div className="w-1/2">
                        <label className="block text-sm font-bold text-gray-700 mb-1">CVV/CVC No.</label>
                        <input
                          type="password"
                          value={cardDetails.cvv}
                          onChange={(e) => handleCardInputChange('cvv', e.target.value)}
                          placeholder="•••"
                          className="w-full p-3 border rounded-lg text-sm"
                          maxLength={3}
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-1">Full Name</label>
                      <input
                        type="text"
                        value={cardDetails.cardName}
                        onChange={(e) => handleCardInputChange('cardName', e.target.value)}
                        placeholder="Shalini Saxena"
                        className="w-full p-3 border rounded-lg text-sm"
                      />
                    </div>
                    <button
                      disabled={!isCardFormValid()}
                      onClick={handleCardSubmit}
                      className={`w-full py-3 rounded-2xl text-sm font-medium transition-colors
                        ${isCardFormValid()
                          ? 'bg-blue-600 hover:bg-blue-700 text-white'
                          : 'bg-[#C5C6CC] cursor-not-allowed text-white'}`}
                    >
                      {/* Pay ₹{parseInt(amount).toLocaleString()} */}
                      Pay {symbols[selectedCurrency] || '₹'} {Math.round(
                  convertFromINR(parseFloat(amount) + ((parseFloat(amount) * paymentPlatformCharges.credit_card) / 100))
                ).toLocaleString()}{" "}(inc.{paymentPlatformCharges.credit_card}% payment gateway charges.)
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Net Banking Accordion */}
          <div className="mb-3">
            <div className={`border rounded-lg overflow-hidden ${selectedMethod === 'netbanking' ? 'bg-blue-50 border-[#175CD3]' : 'bg-white border-gray-200 hover:border-[#175CD3]'}`}>
              <button
                onClick={() => handleMethodChange('netbanking')}
                className="w-full flex items-center justify-between p-4"
              >
                <div className="flex items-center gap-3">
                  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 12.9999H6V20.9999H4C3.73478 20.9999 3.48043 21.1052 3.29289 21.2928C3.10536 21.4803 3 21.7346 3 21.9999C3 22.2651 3.10536 22.5194 3.29289 22.707C3.48043 22.8945 3.73478 22.9999 4 22.9999H28C28.2652 22.9999 28.5196 22.8945 28.7071 22.707C28.8946 22.5194 29 22.2651 29 21.9999C29 21.7346 28.8946 21.4803 28.7071 21.2928C28.5196 21.1052 28.2652 20.9999 28 20.9999H26V12.9999H29C29.2176 12.9996 29.4292 12.9285 29.6026 12.7971C29.7761 12.6658 29.902 12.4815 29.9612 12.2721C30.0205 12.0627 30.0098 11.8398 29.9308 11.637C29.8519 11.4343 29.709 11.2628 29.5238 11.1486L16.5238 3.14862C16.3662 3.05176 16.1849 3.00049 16 3.00049C15.8151 3.00049 15.6338 3.05176 15.4762 3.14862L2.47625 11.1486C2.29103 11.2628 2.14811 11.4343 2.06916 11.637C1.99021 11.8398 1.97955 12.0627 2.03878 12.2721C2.09801 12.4815 2.22391 12.6658 2.39738 12.7971C2.57085 12.9285 2.78242 12.9996 3 12.9999ZM8 12.9999H12V20.9999H8V12.9999ZM18 12.9999V20.9999H14V12.9999H18ZM24 20.9999H20V12.9999H24V20.9999ZM16 5.17362L25.4675 10.9999H6.5325L16 5.17362ZM31 25.9999C31 26.2651 30.8946 26.5194 30.7071 26.707C30.5196 26.8945 30.2652 26.9999 30 26.9999H2C1.73478 26.9999 1.48043 26.8945 1.29289 26.707C1.10536 26.5194 1 26.2651 1 25.9999C1 25.7346 1.10536 25.4803 1.29289 25.2928C1.48043 25.1052 1.73478 24.9999 2 24.9999H30C30.2652 24.9999 30.5196 25.1052 30.7071 25.2928C30.8946 25.4803 31 25.7346 31 25.9999Z" fill="#175CD3"/>
                  </svg>
                  <span className={`font-bold ${selectedMethod === 'netbanking' ? 'text-[#175CD3]' : ''}`}>Net Banking</span>
                </div>
                <svg className={`w-5 h-5 transition-transform ${selectedMethod === 'netbanking' ? 'rotate-180 text-[#175CD3]' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {selectedMethod === 'netbanking' && (
                <div className="p-4 bg-blue-50">
                  <p className="text-sm font-medium text-gray-700 mb-3">Select Bank from the List</p>
                  <div className="space-y-4">
                    <div className="relative">
                      <select
                        className="w-full p-3 bg-white border border-gray-300 rounded-lg appearance-none text-gray-600 pr-10"
                        value={selectedBank}
                        onChange={(e) => setSelectedBank(e.target.value)}
                      >
                        <option value="" disabled>Select Bank</option>
                        {PAYU_BANKS.map((bank) => (
                          <option key={bank.code} value={bank.code}>
                            {bank.name}
                          </option>
                        ))}
                      </select>
                      <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                        <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                    <button
                      className={`w-full py-3 text-white rounded-full font-medium transition-colors
                        ${selectedBank 
                          ? 'bg-[#175CD3] hover:bg-blue-700' 
                          : 'bg-[#C5C6CC] cursor-not-allowed'}`}
                      disabled={!selectedBank}
                      onClick={() => {
                        if (selectedBank) {
                          const totalAmount = parseFloat(amount) + ((parseFloat(amount) * paymentPlatformCharges.net_banking) / 100);
                          // handleNetBankingPayment(selectedBank, totalAmount.toString(), packageId);
                          handleNetbankingPayment(selectedBank, (Math.round(((parseFloat(amount) +
                        (parseFloat(amount) * paymentPlatformCharges.net_banking) / 100) * 100))/100).toString(), packageId,'',
                            '');
                        }
                      }}
                    >
                      Pay {symbols[selectedCurrency] || '₹'} {Math.round(
                        convertFromINR(parseFloat(amount) + ((parseFloat(amount) * paymentPlatformCharges.net_banking) / 100))
                      ).toLocaleString()}{" "}(inc.{paymentPlatformCharges.net_banking}% payment gateway charges.)
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* UPI Accordion */}
          <div className="mb-3">
            <div className={`border rounded-lg overflow-hidden ${selectedMethod === 'upi' ? 'bg-blue-50 border-[#175CD3]' : 'bg-white border-gray-200 hover:border-[#175CD3]'}`}>
              <button
                onClick={() => handleMethodChange('upi')}
                className="w-full flex items-center justify-between p-4"
              >
                <div className="flex items-center gap-3">
                  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M26 10C26 10.2652 25.8946 10.5196 25.7071 10.7071C25.5196 10.8946 25.2652 11 25 11H20.9813C20.9925 11.165 21 11.3313 21 11.5C20.9977 13.4884 20.2068 15.3947 18.8007 16.8007C17.3947 18.2068 15.4884 18.9977 13.5 19H11.5863L20.6725 27.26C20.7715 27.3478 20.8521 27.4544 20.9096 27.5736C20.9671 27.6928 21.0003 27.8222 21.0074 27.9543C21.0145 28.0865 20.9953 28.2187 20.951 28.3434C20.9066 28.4681 20.8379 28.5827 20.7489 28.6806C20.6599 28.7785 20.5524 28.8578 20.4325 28.9139C20.3126 28.97 20.1828 29.0017 20.0506 29.0072C19.9184 29.0127 19.7864 28.9919 19.6623 28.9461C19.5381 28.9002 19.4244 28.8302 19.3275 28.74L8.3275 18.74C8.17814 18.6042 8.07336 18.4265 8.02693 18.2301C7.9805 18.0336 7.99459 17.8278 8.06735 17.6395C8.14011 17.4512 8.26814 17.2894 8.4346 17.1753C8.60106 17.0611 8.79817 17 9 17H13.5C14.9582 16.9983 16.3562 16.4184 17.3873 15.3873C18.4184 14.3562 18.9983 12.9582 19 11.5C19 11.3313 18.9913 11.165 18.9762 11H9C8.73478 11 8.48043 10.8946 8.29289 10.7071C8.10536 10.5196 8 10.2652 8 10C8 9.73478 8.10536 9.48043 8.29289 9.29289C8.48043 9.10536 8.73478 9 9 9H18.3962C17.9357 8.09745 17.2347 7.33968 16.3707 6.8103C15.5067 6.28092 14.5133 6.0005 13.5 6H9C8.73478 6 8.48043 5.89464 8.29289 5.70711C8.10536 5.51957 8 5.26522 8 5C8 4.73478 8.10536 4.48043 8.29289 4.29289C8.48043 4.10536 8.73478 4 9 4H25C25.2652 4 25.5196 4.10536 25.7071 4.29289C25.8946 4.48043 26 4.73478 26 5C26 5.26522 25.8946 5.51957 25.7071 5.70711C25.5196 5.89464 25.2652 6 25 6H18.5925C19.4845 6.82542 20.1631 7.85483 20.57 9H25C25.2652 9 25.5196 9.10536 25.7071 9.29289C25.8946 9.48043 26 9.73478 26 10Z" fill="#175CD3"/>
                  </svg>
                  <span className={`font-bold ${selectedMethod === 'upi' ? 'text-[#175CD3]' : ''}`}>UPI</span>
                </div>
                <svg className={`w-5 h-5 transition-transform ${selectedMethod === 'upi' ? 'rotate-180 text-[#175CD3]' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {selectedMethod === 'upi' && (
                <div className="p-4">
                  <p className="text-sm text-gray-600 mb-4">Enter UPI ID</p>
                  <div className="space-y-4">
                    <div>
                      <div className="relative">
                        <input
                          type="text"
                          value={upiId}
                          onChange={(e) => {
                            setUpiId(e.target.value);
                            // Clear error when user starts typing
                            if (upiError) setUpiError('');
                          }}
                          placeholder="example@upi"
                          className={`w-full p-3 border rounded-lg text-sm ${upiError ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {upiError && (
                          <p className="mt-1 text-sm text-red-600">{upiError}</p>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={async () => {
                        try {
                          // First validate the UPI ID format
                          const isValidFormat = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9]+$/.test(upiId);
                          if (!isValidFormat) {
                            setUpiError('Please enter a valid UPI ID (e.g., example@upi)');
                            return;
                          }
                          
                          // Then validate with PayU
                          console.log('Initiating UPI validation for:', upiId);
                          const isUpiValid = await validateUpiId(upiId);
                          
                          if (!isUpiValid) {
                            setUpiError('This UPI ID is not valid. Please check and try again.');
                            return;
                          }
                          
                          // Only proceed with payment if validation passes
                          await handleUpiPayment(upiId, amount, packageId);
                        } catch (error) {
                          console.error('Error during UPI validation:', error);
                          setUpiError('Failed to validate UPI ID. Please try again.');
                        }
                      }}
                      disabled={!upiId}
                      className={`w-full py-3 rounded-2xl text-sm font-medium transition-colors
                        ${upiId
                          ? 'bg-blue-600 hover:bg-blue-700 text-white'
                          : 'bg-[#C5C6CC] cursor-not-allowed text-white'}`}
                    >
                      Pay {symbols[selectedCurrency] || '₹'} {Math.round(
                        convertFromINR(parseFloat(amount) + ((parseFloat(amount) * paymentPlatformCharges.upi) / 100))
                      ).toLocaleString()}{" "}(inc.{paymentPlatformCharges.upi}% payment gateway charges.)
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
          {/* NEFT Accordian */}
          <div className="mb-3">
            <div className={`border rounded-lg overflow-hidden ${selectedMethod === 'neft' ? 'bg-blue-50 border-[#175CD3]' : 'bg-white border-gray-200 hover:border-[#175CD3]'}`}>
              <button
                onClick={() => handleMethodChange('neft')}
                className="w-full flex items-center justify-between p-4"
              >
                <div className="flex items-center gap-3">
                  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 12.9999H6V20.9999H4C3.73478 20.9999 3.48043 21.1052 3.29289 21.2928C3.10536 21.4803 3 21.7346 3 21.9999C3 22.2651 3.10536 22.5194 3.29289 22.707C3.48043 22.8945 3.73478 22.9999 4 22.9999H28C28.2652 22.9999 28.5196 22.8945 28.7071 22.707C28.8946 22.5194 29 22.2651 29 21.9999C29 21.7346 28.8946 21.4803 28.7071 21.2928C28.5196 21.1052 28.2652 20.9999 28 20.9999H26V12.9999H29C29.2176 12.9996 29.4292 12.9285 29.6026 12.7971C29.7761 12.6658 29.902 12.4815 29.9612 12.2721C30.0205 12.0627 30.0098 11.8398 29.9308 11.637C29.8519 11.4343 29.709 11.2628 29.5238 11.1486L16.5238 3.14862C16.3662 3.05176 16.1849 3.00049 16 3.00049C15.8151 3.00049 15.6338 3.05176 15.4762 3.14862L2.47625 11.1486C2.29103 11.2628 2.14811 11.4343 2.06916 11.637C1.99021 11.8398 1.97955 12.0627 2.03878 12.2721C2.09801 12.4815 2.22391 12.6658 2.39738 12.7971C2.57085 12.9285 2.78242 12.9996 3 12.9999ZM8 12.9999H12V20.9999H8V12.9999ZM18 12.9999V20.9999H14V12.9999H18ZM24 20.9999H20V12.9999H24V20.9999ZM16 5.17362L25.4675 10.9999H6.5325L16 5.17362ZM31 25.9999C31 26.2651 30.8946 26.5194 30.7071 26.707C30.5196 26.8945 30.2652 26.9999 30 26.9999H2C1.73478 26.9999 1.48043 26.8945 1.29289 26.707C1.10536 26.5194 1 26.2651 1 25.9999C1 25.7346 1.10536 25.4803 1.29289 25.2928C1.48043 25.1052 1.73478 24.9999 2 24.9999H30C30.2652 24.9999 30.5196 25.1052 30.7071 25.2928C30.8946 25.4803 31 25.7346 31 25.9999Z" fill="#175CD3"/>
                  </svg>
                  <span className={`font-bold ${selectedMethod === 'neft' ? 'text-[#175CD3]' : ''}`}>NEFT</span>
                </div>
                <svg className={`w-5 h-5 transition-transform ${selectedMethod === 'neft' ? 'rotate-180 text-[#175CD3]' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {selectedMethod === 'neft' && (
                <div className="p-4 bg-blue-50">

                  <div  className={`w-full py-3 rounded-2xl text-sm font-medium text-center mb-2 transition-colors
                        ${true
                          ? 'bg-blue-600 text-white'
                          : 'bg-blue-600 text-white'}`}>
                  Amount to be paid {symbols[`${selectedCurrency}`]} {Math.round(convertFromINR(parseFloat(amount) + ((parseFloat(amount) * paymentPlatformCharges.neft) / 100))).toLocaleString()}
                {" "}(inc.{paymentPlatformCharges.neft}% payment gateway charges.)
                  </div>
                   <p className="text-sm text-gray-800">
                        <strong className="font-semibold mr-1">To make payment via RTGS or NEFT or IMPS</strong>use the bank details listed below:
                      </p>
                 <div className="space-y-3 text-sm text-gray-800">
                 

                  <p className="flex">
                    <strong className="w-1/2">Name of the Beneficiary:</strong>
                    <span className="w-1/2">Right Holiday Technologies Private Limited</span>
                  </p>

                  <p className="flex">
                    <strong className="w-1/2">Account No:</strong>
                    <span className="w-1/2">502000 2734 6691</span>
                  </p>

                  <p className="flex">
                    <strong className="w-1/2">IFSC code:</strong>
                    <span className="w-1/2">HDFC0009117</span>
                  </p>

                  <p className="flex">
                    <strong className="w-1/2">Bank:</strong>
                    <span className="w-1/2">HDFC Bank</span>
                  </p>

                  <p className="flex">
                    <strong className="w-1/2">Branch:</strong>
                    <span className="w-1/2">Vasant Kunj Presidential Park branch</span>
                  </p>

                  <p className="flex">
                    <strong className="w-1/2">Account Type:</strong>
                    <span className="w-1/2">Current</span>
                  </p>

                  <p className="flex">
                    <strong className="w-1/2">Swift Code:</strong>
                    <span className="w-1/2">HDFCINBB</span>
                  </p>

                  <p className="text-red-600 font-medium">
                    Please send us the screenshot or transaction receipt to track your payment confirmation.
                  </p>
                </div>

                  {/* <div className="space-y-4">
                    <div className="relative">
                      <select
                        className="w-full p-3 bg-white border border-gray-300 rounded-lg appearance-none text-gray-600 pr-10"
                        value={selectedBank}
                        onChange={(e) => setSelectedBank(e.target.value)}
                      >
                        <option value="" disabled>Select Bank</option>
                        {PAYU_BANKS.map((bank) => (
                          <option key={bank.code} value={bank.code}>
                            {bank.name}
                          </option>
                        ))}
                      </select>
                      <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                        <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                    <button
                      className={`w-full py-3 text-white rounded-full font-medium transition-colors
                        ${selectedBank 
                          ? 'bg-[#175CD3] hover:bg-blue-700' 
                          : 'bg-[#C5C6CC] cursor-not-allowed'}`}
                      disabled={!selectedBank}
                      onClick={() => selectedBank && handleNetBankingPayment(selectedBank, amount, packageId)}
                    >
                      Pay ₹{parseInt(amount).toLocaleString()}
                    </button>
                  </div> */}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const PaymentPageRender = () => {
  const [bookingDataNew,setBookingDataNew] = useState<any>({});
  const {bookingData,setBookingData} = useContext(AppContext);

  const searchParams = useSearchParams();
  const bookingId = searchParams?.get('bookingId');
  const installmentId = searchParams?.get('installmentId');
  const current_installment = bookingDataNew?.bookingInstallmentsInfo?.find((ele:any)=>ele?.id ==installmentId);
  const amount = current_installment?.amount;
  const packageId = bookingDataNew?.websitePackageInfo? bookingDataNew?.websitePackageInfo?.packageCode : bookingDataNew?.packageInfo?.packageCode ;
  const title = bookingDataNew?.websitePackageInfo? bookingDataNew?.websitePackageInfo?.packageTitle : bookingDataNew?.packageInfo?.packageTitle ;
  const duration = bookingDataNew?.websitePackageInfo? bookingDataNew?.websitePackageInfo?.noOfDays : bookingDataNew?.packageInfo?.noOfDays ;
  const custom =!bookingDataNew?.websitePackageInfo ? 'true' : 'false';
  // const amount = searchParams?.get('amount') || '0';
  // const packageId = searchParams?.get('packageId') || '';
  // const title = searchParams?.get('title') || '';
  // const duration = searchParams?.get('duration') || '';
  // const custom = searchParams.get('custom');
  const url_packagedetail = custom === "true"
      ? `/tours/package-details?custom=true&query=${packageId}`
      : `/tours/package-details?query=${packageId}`;

   const url_traveller = custom === "true"
      ? `/tours/package-details/travellers?custom=true`
      : `/tours/package-details/travellers`;
      let breadcrumbItems:any=[];
      debugger;
      if(bookingData.travellers[0].firstName=''){
        breadcrumbItems = [
      {
        label: "Tours",
        href: "/vacation/tours"
      },
      {
        label: "Package-Details",
        href: url_packagedetail
      },
      {
        label: "Travellers",
        href: url_traveller
      },
      {
        label: "Checkout",
        href: "/tours/travellers/checkout"
      },
      {
        label: "Payment",
        // href: "/last"
      }
    ];

      } else{
        breadcrumbItems = [
            {
              label: "Payment Detail",
              href: `/payment-detail/${bookingId}`
            },
            {
              label: "Payment",
              // href: "/last"
            }
        ];

      }
    useEffect(()=>{
      const fetchBooking = async()=>{
        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/booking/${bookingId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          },
          });
          const response_json_for_booking= await response.json();
          setBookingDataNew(response_json_for_booking);
          if(!response_json_for_booking || !response_json_for_booking?.bookingInstallmentsInfo?.find((ele:any)=>ele?.id ==installmentId)){
            alert('No booking/installment found');
            window.location.href = '/';
          }
        } catch (error) {
          alert('something went wrong in fetching the booking');
        }
      }
      fetchBooking();
    },[])

  return (
    <Layout breadcrumbItems={breadcrumbItems}>
      <Suspense fallback={
        <div className="max-w-7xl mx-auto p-4 pb-52">
          <div className="flex justify-center items-center min-h-[400px]">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#175CD3]"></div>
          </div>
        </div>
      }>
        <PaymentPageContent bookingDataNew={bookingDataNew}/>
      </Suspense>
    </Layout>
  );
};

const PaymentPage =() =>{
  return(
    <Suspense fallback={
        <div className="max-w-7xl mx-auto p-4 pb-52">
          <div className="flex justify-center items-center min-h-[400px]">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#175CD3]"></div>
          </div>
        </div>
      }>
      <PaymentPageRender/>
     </Suspense>
  )
}
export default PaymentPage; 
