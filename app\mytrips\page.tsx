"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import NavigationBar from "../components/NavBar";
import Breadcrumb from "../components/BreadCrumbs";
import Footer from "../components/Footer";
import axios from "axios";
import { countryCodeToEmoji, countryFlags } from "../components/DestinationTours";
import { addDays, format, isAfter, isBefore, isEqual, parseISO } from "date-fns";
import { useRouter } from "next/navigation";
import { useDataStore } from "../store/dataStore";

interface Trip {
  id: string;
  title: string;
  image: string;
  categories: string[];
  startDate: string;
  endDate: string;
  duration: {
    days: number;
    nights: number;
  };
  countries: {
    name: string;
    flag: string;
  }[];
  paymentDue?: {
    amount: number;
    dueDate: string;
  };
  status: "upcoming" | "past";
}

export default function MyTrips() {
  const router = useRouter();
  const setData = useDataStore((state) => state.setData);
  const [selectedTab, setSelectedTab] = useState("My Trips");
  const [tripTab, setTripTab] = useState("Upcoming Trips");
  const [allTripsNew, setAllTripsNew] = useState([]);

  const allTrips: Trip[] = [
    {
      id: "T1",
      title: "Paris & Berlin in 6 days with included Disneyland visit",
      image: "/assets/tours/imageone.png",
      categories: ["Adventure", "Culture", "Honeymoon", "Western Europe"],
      startDate: "24 March 2025",
      endDate: "29 March 2025",
      duration: {
        days: 6,
        nights: 5
      },
      countries: [
        { name: "France", flag: "🇫🇷" },
        { name: "Germany", flag: "🇩🇪" }
      ],
      paymentDue: {
        amount: 35000.00,
        dueDate: "second instalment"
      },
      status: "upcoming"
    },
    {
      id: "T2",
      title: "Paris & Berlin in 6 days with included Disneyland visit",
      image: "/assets/tours/imagetwo.png",
      categories: ["Adventure", "Culture", "Honeymoon", "Western Europe"],
      startDate: "24 March 2025",
      endDate: "29 March 2025",
      duration: {
        days: 6,
        nights: 5
      },
      countries: [
        { name: "France", flag: "🇫🇷" },
        { name: "Germany", flag: "🇩🇪" }
      ],
      status: "upcoming"
    },
    {
      id: "T3",
      title: "Swiss Alps Adventure with Zermatt and Lucerne",
      image: "/assets/tours/imagethree.png",
      categories: ["Adventure", "Nature", "Mountains", "Western Europe"],
      startDate: "15 January 2024",
      endDate: "22 January 2024",
      duration: {
        days: 8,
        nights: 7
      },
      countries: [
        { name: "Switzerland", flag: "🇨🇭" }
      ],
      status: "past"
    },
    {
      id: "T4",
      title: "Italian Riviera and Tuscany Explorer",
      image: "/assets/tours/imagefour.png",
      categories: ["Culture", "Food & Wine", "Coastal", "Western Europe"],
      startDate: "10 December 2023",
      endDate: "17 December 2023",
      duration: {
        days: 8,
        nights: 7
      },
      countries: [
        { name: "Italy", flag: "🇮🇹" }
      ],
      status: "past"
    }
  ];

  const today = new Date();

const filteredTrips = allTripsNew?.length>0 ?  allTripsNew.map((booking: any) => {
    const startDate = new Date(booking.travelDate);
    const endDate = addDays(startDate, booking.websitePackageInfo?.noOfDays || 0);

    const isUpcoming = isAfter(startDate, today) || isEqual(startDate, today);
    const isOngoing =
      isBefore(startDate, today) &&
      (isAfter(endDate, today) || isEqual(endDate, today));

    return {
      ...booking,
      isUpcoming,
      isOngoing,
    };
  })
  .filter((booking: any) => {
    if (tripTab === "Upcoming Trips") {
      return (booking.isUpcoming || booking.isOngoing) && booking?.bookingInstallmentsInfo[0]?.paymentStatus !== "PENDING";
    } else if(tripTab === "Past Trips"){
      // Past trips tab (optional, you can remove this block if not needed)
      return (!booking.isUpcoming && !booking.isOngoing) && booking?.bookingInstallmentsInfo[0]?.paymentStatus !== "PENDING";
    } else if(tripTab === "Payment Pending"){
      return booking?.bookingInstallmentsInfo[0]?.paymentStatus == "PENDING";
    }
  }) 
  // .sort((a: any, b: any) => {
  //   // Ongoing trips first
  //   if (a.isOngoing && !b.isOngoing) return -1;
  //   if (!a.isOngoing && b.isOngoing) return 1;

  //   // Then sort by start date
  //   return new Date(a.travelDate).getTime() - new Date(b.travelDate).getTime();
  // }) 
  : [];
  useEffect(()=>{
    const fetchAllTrips = async () =>{
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/booking/history?authtoken=${localStorage.getItem('authToken')}`, {
          // http://*************/api/booking/history
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
        },
      });
        const response_json= await response.json();      
        setAllTripsNew(response_json);
      } catch (error) {
        const redirectPath = '/mytrips';
        const queryString = new URLSearchParams({ redirect: redirectPath }).toString();
        router.push(`/login?${queryString}`);
        console.log(error);
      }
    }
    fetchAllTrips();
  },[])
  return (
    <div className="min-h-screen bg-white">
      <NavigationBar />

    {/* Breadcrumb */}
    <div className='bg-[#F5FAFF]'>
        <Breadcrumb nav_style_class={"mt-[0px] pt-4"}/>
      {/* Tabs */}
        <div className="">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex space-x-8 items-center pt-[10px]">
            <Link 
                href="/myprofile"
                className={`pt-[6px] px-[20px] pb-[3px] rounded-t-lg ${selectedTab === 'My Profile' ? 'text-blue-600 font-manrope font-semibold text-base leading-6 tracking-normal space-y-4 bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 '}`}
              >
                My Profile
              </Link>
             <Link 
              href="/mytrips"
              className={` pt-[6px] px-[20px] pb-[3px] rounded-t-lg ${selectedTab === 'My Trips' ? 'text-blue-600 font-manrope font-semibold text-base leading-6 tracking-normal space-y-4 bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 '}`}
            >
              My Trips
            </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Trip Tabs */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="flex space-x-4 mb-6">
          <button
            onClick={() => setTripTab("Upcoming Trips")}
            className={`px-4 py-2 rounded-full text-sm font-medium ${
              tripTab === "Upcoming Trips"
                ? "bg-blue-50 text-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            Active Trips
          </button>
          <button
            onClick={() => setTripTab("Past Trips")}
            className={`px-4 py-2 rounded-full text-sm font-medium ${
              tripTab === "Past Trips"
                ? "bg-blue-50 text-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            Past Trips
          </button>
          <button
            onClick={() => setTripTab("Payment Pending")}
            className={`px-4 py-2 rounded-full text-sm font-medium ${
              tripTab === "Payment Pending"
                ? "bg-blue-50 text-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            Payment Pending
          </button>
        </div>

        {/* Trip Cards */}
        <div className="space-y-6">
          {
          filteredTrips.length >0 ?
          // filteredTrips.splice()
          // filteredTrips.slice(15,16).map((trip:any, index) => (
          filteredTrips.map((trip:any, index) => {
            return(
              <div key={trip.id}>
              <div className="bg-white rounded-lg overflow-hidden">
                <div className="flex">
                  <div className="w-80 h-50">
                    <img 
                      src={trip?.websitePackageInfo?.packageMainImage || "/assets/dummy_image_new.jpg"} 
                      alt={trip?.websitePackageInfo?.packageTitle || ""}
                      className="w-full h-full object-cover rounded-2xl"
                    />
                  </div>
                  <div className="flex-1 p-6">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="flex items-center text-sm text-gray-500">
                          {
                          trip?.websitePackageInfo&&
                          [trip?.websitePackageInfo?.packageType, trip?.websitePackageInfo?.packageTheme, ...trip?.websitePackageInfo?.region?.split(",")]
                            ?.filter(Boolean)
                            .map((attribute, index, array) => {
                              return (
                                <span key={`${index}-attribute`} className="flex items-center text-xs text-[#667085] font-[Manrope] font-normal ">
                                  {attribute}
                                  {index < array.length - 1 && <span className="mx-[1px]">•</span>}
                                </span>
                                  )
                                      })
                          }
                          </div>
                        <a href={`/tours/package-details?query=${trip.websitePackageInfo?.packageCode}${(trip.websitePackageInfo?.itineraryType?.toLowerCase()=='quotation'  ||  trip.websitePackageInfo?.itineraryType?.toLowerCase()=='final')?'&custom=true':'&custom=true'}`} target="_blank">
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">{trip?.websitePackageInfo?.packageTitle || ""}</h3>
                        </a>
                         <div className="flex flex-wrap gap-2 mb-2">
                          {trip?.websitePackageInfo && Array.isArray(trip?.websitePackageInfo?.country) ? (
                            trip?.websitePackageInfo?.country.flatMap((country: string) => 
                              country.split(',').map((singleCountry: string, index: number) => (
                                <div key={`${country}-${index}`} className="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full">
                                  <img 
                                    src={`/assets/flags/${singleCountry.trim().split(" ").join("-").toLowerCase()}.svg`} 
                                    className="w-3 h-2.5 mr-1" 
                                    alt="countryImg"
                                  />
                                  <span className="text-[12px] text-black">{singleCountry.trim()}</span>
                                </div>
                              ))

                            )
                          ) : (trip?.websitePackageInfo && trip?.websitePackageInfo?.country) ? (
                            trip?.websitePackageInfo?.country.split(',').map((singleCountry: string, index: number) => (
                              <div key={`${index}-country`} className="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full">
                                {/* <span className="text-sm">{countryFlags[singleCountry.trim().toLowerCase()] || ''}</span> */}
                                <img 
                                    src={`/assets/flags/${singleCountry.trim().split(" ").join("-").toLowerCase()}.svg`} 
                                    className="w-3 h-2.5 mr-1" 
                                    alt="countryImg"
                                  />
                                <span className="text-[12px] text-black">{singleCountry.trim()}</span>
                    </div>
                            ))
                          ) : null}
                        </div>
                      </div>
                      <button onClick={()=>{
                        //  setData(trip);
                        router.push(`/payment-detail/${trip.id}`);
                            // router.push(`/tours/package-details?query=${trip.websitePackageInfo?.packageCode}&custom=true`)
                            // router.push(`/tours/package-details?query=${trip.websitePackageInfo?.packageCode}`)
                          }} className="px-6 py-2.5 bg-blue-600 text-white rounded-full text-sm font-medium hover:bg-blue-700">
                        View Details
                      </button>
                    </div>

                    <div className="mt-6 grid grid-cols-3 gap-6 text-black">
                      <div>
                        <p className="text-sm text-gray-400 mb-1">Starts</p>
                        {/* <p className="font-medium">{format(addDays(parseISO(trip?.travelDate), 1), 'yyyy-MM-dd')}</p> */}
                        <p className="font-medium">{format(addDays(parseISO(trip?.travelDate), 0), 'yyyy-MM-dd')}</p>
                      </div>
                     <div>
                    <p className="text-sm text-gray-400 mb-1">Ends</p>
                    {trip?.travelDate && trip?.websitePackageInfo?.noOfDays && (
                      <p className="font-medium">
                        {/* {format(addDays(new Date(format(addDays(parseISO(trip?.travelDate), 1), 'yyyy-MM-dd')), trip.websitePackageInfo.noOfDays), 'yyyy-MM-dd')} */}
                        {format(addDays(new Date(format(addDays(parseISO(trip?.travelDate), 0), 'yyyy-MM-dd')), trip.websitePackageInfo.noOfDays), 'yyyy-MM-dd')}
                      </p>
                    )}
                  </div>
                      <div>
                        <p className="text-sm text-gray-400 mb-1">Duration</p>
                        <p className="font-medium">{trip?.websitePackageInfo?.noOfDays} Days {trip?.websitePackageInfo?.noOfNights} Nights</p>
                      </div>
                    </div>
                  </div>
                </div>
                {/* trip.bookingInstallmentsInfo.some((element: any) => element.paymentStatus == "PENDING")) */}
                {/* {trip.paymentDue && trip.status === "upcoming" && ( */}
                {/* {trip.bookingInstallmentsInfo.some((element: any) => element.paymentStatus == "PENDING") && (new Date(trip.travelDate)>= new Date()) && ( */}
                {trip?.bookingInstallmentsInfo && trip?.bookingInstallmentsInfo.some((element: any) => element.paymentStatus == "PENDING") && (trip.isUpcoming || trip.isOngoing) && (
                  <div className="mt-6 border-gray-200">
                    <div className="px-6 py-4 bg-orange-50 rounded-lg">
                      <div className="flex items-center justify-between">
                        <p className="text-orange-800">
                          {/* Your payment is due for the {trip.paymentDue.dueDate}. Please clear your dues of ₹ {trip.paymentDue.amount.toLocaleString("en-IN")} today. */}
                          Your payment is due for the {trip.bookingInstallmentsInfo.find((element: any) => element.paymentStatus == "PENDING").paymentDate}. Please clear your dues of ₹ {trip.bookingInstallmentsInfo.find((element: any) => element.paymentStatus == "PENDING").amount.toLocaleString("en-IN")} today.
                        </p>
                        <button className="px-4 py-2 bg-blue-100 text-blue-600 rounded-full text-sm font-medium hover:bg-blue-200">
                          Make Payment
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              {index < filteredTrips.length - 1 && (
                <div className="border-b border-gray-200 my-6"></div>
              )}
            </div>
            )
          })
          : (
            <div className="flex flex-col items-center justify-center py-12">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-16 h-16 text-gray-300 mb-4">
                <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
              </svg>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">No trips found</h3>
              <p className="text-gray-500 text-center max-w-md">
                You have no trips in this category. {tripTab == "Upcoming Trips" ? "Please book a trip to see it here." : ""}
              </p>
            </div>
          )


          }
        </div>
      </div>
      <Footer />
    </div>
  );
} 