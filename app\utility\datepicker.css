.react-datepicker-wrapper {
  @apply w-full;
}

.react-datepicker-popper {
  @apply z-[100];
}

.react-datepicker {
  @apply font-sans border border-gray-100 rounded-xl shadow-lg bg-white !important;
  margin-top: 0.5rem;
}

.react-datepicker__header {
  @apply bg-white border-b border-gray-100 rounded-t-xl !important;
  padding-top: 1rem;
}

.react-datepicker__current-month {
  @apply text-black font-medium mb-2 !important;
}

.react-datepicker__day-name {
  @apply text-gray-500 font-normal !important;
  margin: 0.5rem;
  width: 2rem;
}

.react-datepicker__day {
  @apply text-gray-900 hover:bg-gray-50 rounded-full !important;
  margin: 0.5rem;
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
}

.react-datepicker__day--selected {
  @apply bg-blue-500 text-white hover:bg-blue-600 !important;
}

.react-datepicker__day--disabled {
  @apply text-gray-300 hover:bg-transparent cursor-not-allowed !important;
}

.react-datepicker__navigation {
  @apply top-4 !important;
}

.react-datepicker__navigation-icon::before {
  @apply border-gray-400 !important;
}

.react-datepicker__day--keyboard-selected {
  @apply bg-gray-50 text-gray-900 !important;
}

.react-datepicker__triangle {
  display: none;
}

.react-datepicker__month-container {
  @apply p-2;
}

.react-datepicker__input-container {
  @apply w-full relative;
}

.react-datepicker__input-container input {
  @apply w-full px-4 py-2.5 rounded-lg border border-gray-100 focus:outline-none focus:ring-1 focus:ring-gray-200 focus:border-gray-200 transition-all pl-10 text-gray-900 placeholder-gray-500 text-sm bg-white !important;
  cursor: pointer;
} 