/* Form Styles */
.formInput {
  @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm;
}

.formInput:focus {
  @apply border-blue-500 ring-blue-500;
}

/* Card Hover Effects */
.contactCard {
  @apply transition-transform duration-300;
}

.contactCard:hover {
  @apply transform scale-105;
}

/* Image Container */
.imageContainer {
  @apply relative overflow-hidden rounded-xl;
  height: calc(100% - 2rem);
}

/* Submit Button Animation */
.submitButton {
  @apply transition-all duration-300;
}

.submitButton:hover {
  @apply transform scale-105;
}

/* Responsive Padding */
@media (min-width: 640px) {
  .formContainer {
    @apply px-8;
  }
}

@media (min-width: 1024px) {
  .formContainer {
    @apply px-12;
  }
} 