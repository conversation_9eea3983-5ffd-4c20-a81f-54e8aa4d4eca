"use client";

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import "react-datepicker/dist/react-datepicker.css";
import DatePicker from 'react-datepicker';
import "./datepicker.css";
import SearchResults from './SearchResults';
import axios from 'axios';
import toast from 'react-hot-toast';

interface SavedState {
  selectedTrips: any[];
  selectedTransport: any;
  destination: string;
  date: string | null;
  travellers: number;
  destinationName: string;
}

const Customized = () => {
  const searchParams = useSearchParams();
  const [destination, setDestination] = useState('');
  const [destinationName, setDestinationName] = useState('');
  const [travelDate, setTravelDate] = useState<Date | null>(null);
  const [travellers, setTravellers] = useState(1);
  const [showResults, setShowResults] = useState(false);
  const [destinationList, setDestinationList] = useState<any[]>([]);
  const [isRestoring, setIsRestoring] = useState(true);

  const isFormValid = destination !== '' && travelDate !== null && travellers > 0;
  // Restore saved state from localStorage
  useEffect(() => {
    const restoreState = () => {
      try {
        const savedState = localStorage.getItem('pendingItinerary');
        if (savedState) {
          const parsedState: SavedState = JSON.parse(savedState);
          
          // Only restore if we have valid data
          if (parsedState.destination) {
            setDestination(parsedState.destination);
            setDestinationName(parsedState.destinationName || '');
            setTravellers(parseInt(parsedState.travellers as any) || 1);
            
            if (parsedState.date) {
              setTravelDate(new Date(parsedState.date));
            }
            
            // Show results if we have selected trips
            if (parsedState.selectedTrips?.length > 0) {
              setShowResults(true);
            }
          }
        }
      } catch (error) {
        console.error('Error restoring state:', error);
      } finally {
        setIsRestoring(false);
      }
    };

    // Only restore state if we're not already showing results
    if (!showResults) {
      restoreState();
    } else {
      setIsRestoring(false);
    }
  }, [showResults]);

  // Fetch destinations
  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/destinations`);
        if (response.status === 200) {
          setDestinationList(response.data);
          
          // If we have a destination in URL params, set it
          const destParam = searchParams?.get('destination');
          if (destParam && !destination) {
            const dest = response.data.find((d: any) => d.id === destParam);
            if (dest) {
              setDestination(dest.id);
              setDestinationName(dest.name);
              setShowResults(true);
            }
          }
        } else {
          toast.error("Failed to load destinations. Please try again.");
        }
      } catch (error) {
        console.error('Error fetching destinations:', error);
        toast.error("Failed to load destinations. Please try again.");
      }
    };

    fetchDestinations();
  }, [searchParams, destination]);

  const handleSearch = () => {
    if (!isFormValid) return;
    setShowResults(true);
  };

  if (isRestoring) {
    return (
      <div className="flex justify-center items-center min-h-[50vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (showResults) {
    return (
      <SearchResults 
        date={travelDate} 
        destination={destination} 
        destinationName={destinationName}
        travellers={travellers}
        onEdit={() => setShowResults(false)}
      />
    );
  }

  return (
    <div className="w-full bg-white py-8 sm:py-12 lg:py-16">
      <div className="max-w-5xl mx-auto text-center px-4 sm:px-6">
        <h1 className="text-2xl sm:text-3xl lg:text-[40px] font-bold text-gray-900 mb-2 sm:mb-3">
          Create Your Own Package
        </h1>
        <p className="text-base sm:text-lg text-gray-600 mb-8 sm:mb-12">
          Get a personalised day-by-day itinerary for your vacation
        </p>

        <div className="space-y-6 sm:space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 w-full">
            {/* Trip Date Input */}
            <div>
              <label className="block text-xs sm:text-sm text-gray-600 mb-1.5 sm:mb-2 text-left">
                Trip Date
              </label>
              <div className="relative">
                <div className="absolute left-3 sm:left-3.5 top-1/2 -translate-y-1/2 pointer-events-none z-10">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3.5 sm:w-4 h-3.5 sm:h-4 text-gray-400">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
                  </svg>
                </div>
                <DatePicker
                  selected={travelDate}
                  onChange={(date) => setTravelDate(date)}
                  minDate={new Date()}
                  placeholderText="When are you travelling?"
                  dateFormat="dd/MM/yyyy"
                  popperClassName="react-datepicker-popper"
                  popperPlacement="bottom-start"
                  showPopperArrow={false}
                  className="w-full px-3 sm:px-4 py-2 sm:py-2.5 rounded-full border border-gray-100 focus:outline-none focus:ring-1 focus:ring-gray-200 focus:border-gray-200 transition-all pl-8 sm:pl-10 text-gray-900 placeholder-gray-500 text-xs sm:text-sm bg-white cursor-pointer"
                />
              </div>
            </div>

            {/* Number of Travellers Input */}
            <div>
              <label className="block text-xs sm:text-sm text-gray-600 mb-1.5 sm:mb-2 text-left">
                Number of Travellers
              </label>
              <div className="relative">
                <div className="absolute left-3 sm:left-3.5 top-1/2 -translate-y-1/2 pointer-events-none z-10">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3.5 sm:w-4 h-3.5 sm:h-4 text-gray-400">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                  </svg>
                </div>
                <input
                  type="number"
                  min="1"
                  max="50"
                  value={travellers}
                  onChange={(e) => setTravellers(Math.max(1, parseInt(e.target.value) || 1))}
                  className="w-full px-3 sm:px-4 py-2 sm:py-2.5 rounded-full border border-gray-100 focus:outline-none focus:ring-1 focus:ring-gray-200 focus:border-gray-200 transition-all pl-8 sm:pl-10 text-gray-900 text-xs sm:text-sm"
                />
              </div>
            </div>

            {/* Destination Input */}
            <div>
              <label className="block text-xs sm:text-sm text-gray-600 mb-1.5 sm:mb-2 text-left">
                Destination
              </label>
              <div className="relative">
                <select
                  className="w-full px-3 sm:px-4 py-2 sm:py-2.5 rounded-full border border-gray-100 focus:outline-none focus:ring-1 focus:ring-gray-200 focus:border-gray-200 transition-all appearance-none bg-white pl-8 sm:pl-10 text-gray-900 text-xs sm:text-sm"
                  value={destination}
                  onChange={(e) => {
                    setDestination(e.target.value);
                    setDestinationName(e.target.selectedOptions[0].text);
                  }}
                >
                  <option value="" className="text-gray-500">Choose destination</option>
                  {
                    destinationList.length >0 &&
                    destinationList.map((des:any)=>{
                      return (
                        <option key={des?.id} value={`${des?.id}`}>{des?.destinationName}</option>
                      )
                    })
                  }
                  {/* <option value="italy">Italy</option>
                  <option value="france">France</option>
                  <option value="spain">Spain</option> */}
                </select>
                <div className="absolute left-3 sm:left-3.5 top-1/2 -translate-y-1/2 pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3.5 sm:w-4 h-3.5 sm:h-4 text-gray-400">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                  </svg>
                </div>
                <div className="absolute right-3 sm:right-3.5 top-1/2 -translate-y-1/2 pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3 sm:w-3.5 h-3 sm:h-3.5 text-gray-400">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Search Button */}
          <button
            onClick={handleSearch}
            className={`px-6 sm:px-8 py-2 sm:py-2.5 rounded-full transition-colors text-xs sm:text-sm ${
              isFormValid 
                ? 'bg-blue-500 text-white hover:bg-blue-600' 
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            }`}
          >
            Search Now
          </button>
        </div>
      </div>
    </div>
  );
};

export default Customized;
