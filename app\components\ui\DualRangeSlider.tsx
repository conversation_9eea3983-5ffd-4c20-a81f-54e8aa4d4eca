'use client';

import React from 'react';

interface DualRangeSliderProps {
  min: number;
  max: number;
  step?: number;
  value: { min: number; max: number };
  onChange: (value: { min: number; max: number }) => void;
  formatLabel?: (value: number) => string;
  className?: string;
  trackClassName?: string;
  thumbClassName?: string;
  disabled?: boolean;
}

const DualRangeSlider: React.FC<DualRangeSliderProps> = ({
  min,
  max,
  step = 1,
  value,
  onChange,
  formatLabel = (val) => val.toString(),
  className = '',
  trackClassName = '',
  thumbClassName = '',
  disabled = false,
}) => {
  const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMin = Number(e.target.value);
    if (newMin <= value.max - step && !disabled) {
      onChange({ min: newMin, max: value.max });
    }
  };

  const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMax = Number(e.target.value);
    if (newMax >= value.min + step && !disabled) {
      onChange({ min: value.min, max: newMax });
    }
  };

  const getPercentage = (val: number) => ((val - min) / (max - min)) * 100;

  return (
    <div className={`relative ${className}`}>
      {/* Value Display */}
      <div className="mb-6">
        <p className="text-sm text-gray-600 mb-2">Selected range</p>
        <p className="text-black text-lg font-medium">
          {formatLabel(value.min)} - {formatLabel(value.max)}
        </p>
      </div>

      {/* Slider Container */}
      <div className="relative h-6 mb-4">
        {/* Track Background */}
        <div className={`absolute top-2 w-full h-2 bg-gray-200 rounded-full ${trackClassName}`}></div>

        {/* Active Track */}
        <div
          className="absolute top-2 h-2 bg-blue-600 rounded-full"
          style={{
            left: `${getPercentage(value.min)}%`,
            width: `${getPercentage(value.max) - getPercentage(value.min)}%`,
          }}
        ></div>

        {/* Min Range Input */}
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value.min}
          onChange={handleMinChange}
          disabled={disabled}
          className={`absolute w-full h-6 appearance-none bg-transparent cursor-pointer dual-range-slider ${thumbClassName}`}
          style={{ zIndex: 1 }}
        />

        {/* Max Range Input */}
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value.max}
          onChange={handleMaxChange}
          disabled={disabled}
          className={`absolute w-full h-6 appearance-none bg-transparent cursor-pointer dual-range-slider ${thumbClassName}`}
          style={{ zIndex: 2 }}
        />
      </div>

      {/* Range Labels */}
      <div className="flex justify-between text-sm text-gray-500 mt-2">
        <span>{formatLabel(min)}</span>
        <span>{formatLabel(max)}</span>
      </div>

      {/* CSS Styles */}
      <style jsx>{`
        .dual-range-slider {
          -webkit-appearance: none;
          background: transparent;
          pointer-events: auto;
        }
        
        .dual-range-slider::-webkit-slider-thumb {
          -webkit-appearance: none;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: #ffffff;
          border: 2px solid #2563eb;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          pointer-events: auto;
          transition: all 0.2s ease;
        }
        
        .dual-range-slider::-webkit-slider-thumb:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        .dual-range-slider::-moz-range-thumb {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: #ffffff;
          border: 2px solid #2563eb;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          pointer-events: auto;
          transition: all 0.2s ease;
        }
        
        .dual-range-slider::-moz-range-thumb:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        .dual-range-slider::-webkit-slider-track {
          background: transparent;
        }
        
        .dual-range-slider::-moz-range-track {
          background: transparent;
        }
        
        .dual-range-slider:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
        
        .dual-range-slider:disabled::-webkit-slider-thumb {
          cursor: not-allowed;
        }
        
        .dual-range-slider:disabled::-moz-range-thumb {
          cursor: not-allowed;
        }
      `}</style>
    </div>
  );
};

export default DualRangeSlider;
