import { useCurrencyStore } from "@/app/store/useCurrencyStore";
import CustomDateInput from "@/app/utility/date_input";
import {
  areIntervalsOverlapping,
  endOfMonth,
  format,
  isBefore,
  isWithinInterval,
  parse,
  parseISO,
  startOfMonth,
  startOfToday,
} from "date-fns";
import React, { useEffect, useState } from "react";

const DiscountModal = ({
  applicableDiscountRulesVisible,
  discountRules = [],
  packageData,
  onClose,
  onSelectDate,
}: any) => {
  const convertFromINR = useCurrencyStore((state) => state.convertFromINR);
  const symbols = useCurrencyStore((state) => state.symbols);
  const selectedCurrency = useCurrencyStore((state) => state.selectedCurrency);

  const [selectedRule, setSelectedRule] = useState<any>(null);
  const [selectedStartDate, setSelectedStartDate] = useState("");
  const [selectedMonth, setSelectedMonth] = useState("");
  const [filteredRules, setFilteredRules] = useState(discountRules);

  const originalPrice = packageData?.priceSummary?.netSellingPrice ?? 0;
  const grossPrice = packageData?.priceSummary?.grossSellingPrice ?? originalPrice;
  const baseDiscountAmount = grossPrice - originalPrice;

  const handleProceed = () => {
    if (filteredRules.length == 0 && selectedStartDate) {
      onSelectDate(selectedStartDate, null);
      return;
    }
    if (selectedRule && selectedStartDate) {
      onSelectDate(selectedStartDate, selectedRule);
    }
  };

  const handleSelectDeparture = (e: any) => {
    setSelectedStartDate("");
    setSelectedRule(null);
    setSelectedMonth(e.target.value);
  };

  useEffect(() => {
    if (!selectedMonth) {
      setFilteredRules(discountRules);
      return;
    }

    let data = discountRules.filter((rule: any) => {
      const monthStart = startOfMonth(parse(selectedMonth, "MMMM yyyy", new Date()));
      const monthEnd = endOfMonth(parse(selectedMonth, "MMMM yyyy", new Date()));
      const ruleStart = parse(format(parseISO(rule.startDate), "dd-MM-yyyy"), "dd-MM-yyyy", new Date());
      const ruleEnd = parse(format(parseISO(rule.endDate), "dd-MM-yyyy"), "dd-MM-yyyy", new Date());
      return areIntervalsOverlapping(
        { start: monthStart, end: monthEnd },
        { start: ruleStart, end: ruleEnd }
      );
    });

    setFilteredRules(data);
  }, [selectedMonth]);

  const parsedMonth = parse(selectedMonth, "MMMM yyyy", new Date());
  let minDate = startOfMonth(parsedMonth);
  const maxDate = endOfMonth(parsedMonth);

  const today = startOfToday();
  if (isBefore(minDate, today)) {
    minDate = today;
  }

  if (!applicableDiscountRulesVisible || discountRules.length == 0) return null;

  return (
<div className="z-50 fixed inset-0 bg-black bg-opacity-50  flex items-center justify-center px-2 sm:px-4 py-6 overflow-auto">
  <div className="bg-white w-full max-w-2xl rounded-2xl relative shadow-xl flex flex-col max-h-[80vh]">
    {/* Header */}
    <div className="p-4 border-b relative">
      <button
        onClick={onClose}
        className="absolute top-4 right-5 text-gray-400 hover:text-black text-2xl font-bold"
      >
        ×
      </button>
      <h2 className="text-xl font-semibold text-gray-800">
        Available Discount Offers
      </h2>
    </div>

    {/* Static Info Section */}
    <div className="px-4 pt-4 space-y-4">
      {grossPrice > originalPrice && (
        <div className="p-3 rounded-md bg-yellow-50 border border-yellow-300 text-yellow-800 text-sm font-medium space-y-1">
          {/* <div>
            🎁 <strong>Base Discount Applied:</strong> {symbols[selectedCurrency]}{" "}
            {Math.round(convertFromINR(baseDiscountAmount)).toLocaleString()} OFF
          </div> */}
          <div>
            💰 <strong>Price After Base Discount:</strong> {symbols[selectedCurrency]}{" "}
            {Math.round(convertFromINR(originalPrice)).toLocaleString()}
          </div>
          <div>
            📅 <strong>Additional Discounts</strong> will be applied on this base-discounted price.
          </div>
        </div>
      )}

      {/* Departure Month Selector */}
      <div>
        <label className="text-sm font-medium text-gray-700 block mb-1">
          Select Departure Month:
        </label>
        <select
          value={selectedMonth}
          onChange={handleSelectDeparture}
          className="w-full p-2 border rounded-md text-sm border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">-- Choose Month --</option>
          {Array.from({ length: 12 }).map((_, index) => {
            const date = new Date();
            date.setMonth(date.getMonth() + index);
            const month = date.toLocaleString("default", { month: "long" });
            const year = date.getFullYear();
            const value = `${month} ${year}`;
            return (
              <option key={value} value={value}>
                {value}
              </option>
            );
          })}
        </select>
      </div>
    </div>

    {/* Scrollable Discount Rules */}
    <div className="overflow-y-auto px-4 py-4 space-y-4 flex-1 mt-2">
      {filteredRules.length === 0 ? (
        <p className="text-sm text-gray-500 text-center">No additional discounts available.</p>
      ) : (
        filteredRules.map((rule: any) => {
          const discountedPrice = Math.round(
            originalPrice * (1 - rule.discountPercentage / 100)
          );
          const isSelected = selectedRule?.ruleId === rule.ruleId;

          return (
            <div
              key={rule.ruleId}
              className={`transition-all duration-300 border p-4 rounded-lg shadow-sm ${
                isSelected ? "border-green-600 bg-green-50" : "border-gray-200 bg-white"
              }`}
            >
              <div className="flex justify-between items-center">
                <h3 className="text-md font-semibold text-gray-800">{rule.ruleName}</h3>
                <span className="text-sm font-medium text-green-600">
                  {rule.discountPercentage}% OFF
                </span>
              </div>

              <div className="mt-2 p-2 rounded bg-blue-50 border border-blue-200 text-sm text-blue-900 font-medium flex items-center gap-2">
                📅 Valid from:
                <span className="font-semibold">
                  {format(parseISO(rule.startDate), "dd-MM-yyyy")}
                </span>
                to
                <span className="font-semibold">
                  {format(parseISO(rule.endDate), "dd-MM-yyyy")}
                </span>
              </div>

              {/* <p className="text-sm mt-1">
                <span className="font-medium">Additional Discount:</span>{" "}
                {rule.discountPercentage}% OFF on {symbols[selectedCurrency]}{" "}
                {Math.round(convertFromINR(originalPrice)).toLocaleString()}
              </p> */}

              <p className="text-sm mt-1">
                <span className="font-medium">Discounted Price:</span>{" "}
                {symbols[selectedCurrency]}{" "}
                {Math.round(convertFromINR(discountedPrice)).toLocaleString()}
              </p>

              <button
                className={`mt-3 px-4 py-1.5 text-sm rounded font-medium transition ${
                  isSelected
                    ? "bg-green-600 text-white"
                    : "bg-blue-600 text-white hover:bg-blue-700"
                }`}
                onClick={() => {
                  setSelectedRule(rule);
                  setSelectedStartDate("");
                }}
              >
                {isSelected ? "Selected" : "Select This Offer"}
              </button>

              {isSelected && (
                <div className="mt-4">
                  <label className="text-sm font-medium text-gray-700">
                    Choose Start Date:
                  </label>
                  <CustomDateInput
                    value={selectedStartDate}
                    minDate={
                      isBefore(parse(rule.startDate, "yyyy-MM-dd", new Date()), startOfToday())
                        ? today
                        : parse(rule.startDate, "yyyy-MM-dd", new Date())
                    }
                    maxDate={new Date(rule.endDate)}
                    height={"44px"}
                    width={"214px"}
                    from_discount={true}
                    onChange={(date: any) => setSelectedStartDate(date)}
                  />
                </div>
              )}
            </div>
          );
        })
      )}
    </div>

    {/* Footer with date + proceed */}
    <div className="p-4 border-t bg-white">
      {filteredRules.length === 0 && (
        <div className="mb-2">
          <label className="text-sm font-medium text-gray-700 block mb-1">
            Choose Start Date for selected month:
          </label>
          <CustomDateInput
            value={selectedStartDate}
            onChange={(date: any) => setSelectedStartDate(date)}
            minDate={minDate}
            maxDate={maxDate}
            height="44px"
            width="214px"
          />
        </div>
      )}
      <button
        onClick={handleProceed}
        disabled={!selectedRule && filteredRules.length !== 0 && !selectedStartDate}
        className={`w-full px-5 py-2.5 rounded-lg text-white font-semibold transition duration-200 text-center ${
          (selectedRule || filteredRules.length === 0) && selectedStartDate
            ? "bg-green-600 hover:bg-green-700"
            : "bg-gray-400 cursor-not-allowed"
        }`}
      >
        Proceed to Booking
      </button>
    </div>
  </div>
</div>


  );
};

export default DiscountModal;





// import { useCurrencyStore } from "@/app/store/useCurrencyStore";
// import CustomDateInput from "@/app/utility/date_input";
// import { areIntervalsOverlapping, endOfMonth, format, isBefore, isWithinInterval, parse, parseISO, startOfMonth, startOfToday } from "date-fns";
// import React, { useEffect, useState } from "react";
// // http://localhost:3000/tours/package-details?query=WYT00372
// // https://stage-api.wiseyatra.com/api/packages/website/WYT00025
// const DiscountModal = ({
//   applicableDiscountRulesVisible,
//   discountRules = [],
//   packageData,
//   onClose,
//   onSelectDate,
// }:any) => {
//   const convertFromINR = useCurrencyStore(state => state.convertFromINR);
//   const symbols = useCurrencyStore(state => state.symbols);
//   const selectedCurrency = useCurrencyStore(state => state.selectedCurrency);
//   const [selectedRule, setSelectedRule] = useState<any>(null);
//   const [selectedStartDate, setSelectedStartDate] = useState("");
//   const [selectedMonth, setSelectedMonth] = useState("");
//   const [filteredRules, setFilteredRules] = useState(discountRules);

//   const originalPrice = packageData?.priceSummary?.netSellingPrice ?? 0;


//   const handleProceed = () => {
//     if(filteredRules.length==0 && selectedStartDate ){
//       onSelectDate(selectedStartDate, null);
//       return;
//     }
//     if (selectedRule && selectedStartDate) {
//       onSelectDate(selectedStartDate, selectedRule);
//     }
//   };

//   const handleSelectDeparture=(e:any)=>{
//     setSelectedStartDate("");
//     setSelectedRule(null);
//     setSelectedMonth(e.target.value);
//   }
//   useEffect(()=>{
//     console.log("selectedMonth",selectedMonth);
//     console.log("discountRules",discountRules);
//     console.log("filteredRules",filteredRules);
//     if(!selectedMonth){
//       setFilteredRules(discountRules);
//       return;
//     }
//     let data = discountRules.filter((rule:any) => {
//     const monthStart = startOfMonth(parse(selectedMonth, "MMMM yyyy", new Date()));
//     const monthEnd = endOfMonth(parse(selectedMonth, "MMMM yyyy", new Date()));
//     const ruleStart = parse(format(parseISO(rule.startDate), 'dd-MM-yyyy'), 'dd-MM-yyyy', new Date());
//     const ruleEnd = parse(format(parseISO(rule.endDate), 'dd-MM-yyyy'), 'dd-MM-yyyy', new Date());
//     return areIntervalsOverlapping(
//     { start: monthStart, end: monthEnd },
//     { start: ruleStart, end: ruleEnd }
//     );   
//     // const selectedMonthDate = parse(selectedMonth, "MMMM yyyy", new Date());
//     // const start = parseISO(rule.startDate);
//     // const end = parseISO(rule.endDate);
//     // return isWithinInterval(selectedMonthDate, { start, end });
//   })
//   setFilteredRules(data);

//   },[selectedMonth])
  
  
//   const parsedMonth = parse(selectedMonth, "MMMM yyyy", new Date());
//   let minDate = startOfMonth(parsedMonth);
//   const maxDate = endOfMonth(parsedMonth);

//   // Prevent past dates
//   const today = startOfToday();
//   if (isBefore(minDate, today)) {
//   minDate = today;
//   }

//   if (!applicableDiscountRulesVisible || discountRules.length==0 ) return null;

//   return (
//     <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center px-4">
//       <div className="bg-white w-full max-w-xl rounded-2xl p-6 relative shadow-xl transition-all duration-300 ease-in-out">
//         {/* Close Button */}
//         <button
//           onClick={onClose}
//           className="absolute top-3 right-4 text-gray-400 hover:text-black text-2xl font-bold"
//         >
//           ×
//         </button>

//         <h2 className="text-2xl font-semibold mb-6 text-gray-800">
//           {/* 🎉 Available Discount Offers */}
//            Available Discount Offers
//         </h2>
//         <div className="mb-4">
//         <label className="text-sm font-medium text-gray-700 block mb-1">
//           Select Departure Month:
//         </label>
//         <select
//           value={selectedMonth}
//           // onChange={(e) => setSelectedMonth(e.target.value)}
//           onChange={handleSelectDeparture}
//           // className="w-full p-2 border rounded-md text-sm border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
//           className="w-full p-2 border rounded-md text-sm border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
//         >
//           <option value="">-- Choose Month --</option>
//           {Array.from({ length: 12 }).map((_, index) => {
//             const date = new Date();
//             date.setMonth(date.getMonth() + index);
//             const month = date.toLocaleString("default", { month: "long" });
//             const year = date.getFullYear();
//             const value = `${month} ${year}`;

//             return (
//               <option key={value} value={value}>
//                 {value}
//               </option>
//             );
//           })}
//         </select>
//       </div>

//         {filteredRules.length === 0 && (
//           <p className="text-sm text-gray-500 text-center">No discounts available.</p>
//         )}

//         <div className="space-y-4 max-h-[400px] overflow-y-auto pr-2">
//           {filteredRules.map((rule:any) => {
//             const discountedPrice = Math.round(
//               originalPrice * (1 - rule.discountPercentage / 100)
//             );
//             const isSelected = selectedRule?.ruleId === rule.ruleId;

//             return (
//               <div
//                 key={rule.ruleId}
//                 className={`transition-all duration-300 border p-4 rounded-lg shadow-sm ${
//                   isSelected
//                     ? "border-green-600 bg-green-50"
//                     : "border-gray-200 bg-white"
//                 }`}
//               >
//                 <div className="flex justify-between items-center">
//                   <h3 className="text-md font-semibold text-gray-800">
//                     {rule.ruleName}
//                   </h3>
//                   <span className="text-sm font-medium text-green-600">
//                     {rule.discountPercentage}% OFF
//                   </span>
//                 </div>

// 								<div className="mt-2 p-2 rounded bg-blue-50 border border-blue-200 text-sm text-blue-900 font-medium flex items-center gap-2">
// 									📅 Valid from: 
// 									<span className="font-semibold">{format(parseISO(rule.startDate), 'dd-MM-yyyy')}</span> 
// 									to 
// 									<span className="font-semibold">{format(parseISO(rule.endDate), 'dd-MM-yyyy')}</span>
// 								</div>

//                 <p className="text-sm mt-1">
//                   <span className="font-medium">Discounted Price:</span>{" "}
//                   {symbols[`${selectedCurrency}`]} {Math.round(convertFromINR(discountedPrice)).toLocaleString()}
//                 </p>

//                 <button
//                   className={`mt-3 px-4 py-1.5 text-sm rounded font-medium transition ${
//                     isSelected
//                       ? "bg-green-600 text-white"
//                       : "bg-blue-600 text-white hover:bg-blue-700"
//                   }`}
//                   onClick={() => {
//                     setSelectedRule(rule);
//                     setSelectedStartDate("");
//                   }}
//                 >
//                   {isSelected ? "Selected" : "Select This Offer"}
//                 </button>

//                 {isSelected && (
//                   <div className="mt-4">
//                     <label className="text-sm font-medium text-gray-700">
//                       Choose Start Date:
//                     </label>
//                      <CustomDateInput
//                       value={selectedStartDate}
//                       minDate={isBefore(parse(rule.startDate, 'yyyy-MM-dd', new Date()), startOfToday()) ? today : parse(rule.startDate, 'yyyy-MM-dd', new Date())}
//                       maxDate={new Date(rule.endDate)}
//                       height={"44px"}
//                       width={"214px"}
//                       from_discount={true}
//                       onChange={(date:any) => setSelectedStartDate(date)}
//                       />
//                   </div>
//                 )}
//               </div>
//             );
//           })}
//         </div>

//         {/* Footer */}
//         <div className="flex flex-col sm:flex-row sm:items-end sm:justify-between gap-4 mt-6">
//         {/* Date Picker Section */}
//         <div>
//         {
//         filteredRules.length==0 &&
//         <>
//         <label className="text-sm font-medium text-gray-700 block mb-1">
//         Choose Start Date for selected month:
//         </label>
//         <CustomDateInput
//         value={selectedStartDate}
//         onChange={(date: any) => setSelectedStartDate(date)}
//         minDate={minDate}
//         maxDate={maxDate}
//         height="44px"
//         width="214px"
//         />
//         </>
//         }
//         </div>

//         {/* Proceed Button */}
//         <button
//         onClick={handleProceed}
//         disabled={!selectedRule && filteredRules.length !== 0 && !selectedStartDate}
//         className={`px-5 py-2.5 rounded-lg text-white font-semibold transition duration-200 min-w-[180px] text-center ${
//         (selectedRule || filteredRules.length === 0) && selectedStartDate
//         ? "bg-green-600 hover:bg-green-700"
//         : "bg-gray-400 cursor-not-allowed"
//         }`}
//         >
//         Proceed to Booking
//         </button>
//         </div>

//       </div>
//     </div>
//   );
// };

// export default DiscountModal;
