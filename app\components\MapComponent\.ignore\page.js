


// With city name and not lat long
// // pages/map.tsx
// import dynamic from 'next/dynamic';

// const MapComponent = dynamic(() => import('./MapComponent'), {
// 	ssr: false,
// });

// export default function MapPage({packageData}:any) {
// 	const result: any[] = [];
// 	const days = packageData.days || [];
// 	days.forEach((day:any, dayIndex:any) => {
// 		day.cities.forEach((city:any, cityIndex:any) => {
// 			// const name = city.title.split(";")[0].trim();
// 			const name = city.title.trim();
// 			const excursions = city.itineraries;
// 			const city_name = city.itineraries[0].selected_excursion?.cityName
// 			// Fallback coordinates
// 			let lat = 0, lng = 0;

// 			// for (const ex of excursions) {
// 			//   const selected = ex.selected_excursion;
// 			//   if (selected?.meetingPointLocationLatitude && selected?.meetingPointLocationLongitude) {
// 			//     lat = parseFloat(selected.meetingPointLocationLatitude);
// 			//     lng = parseFloat(selected.meetingPointLocationLongitude);
// 			//     break;
// 			//   }
// 			//   if (selected?.hotelLocationLatitude && selected?.hotelLocationLongitude) {
// 			//     lat = parseFloat(selected.hotelLocationLatitude);
// 			//     lng = parseFloat(selected.hotelLocationLongitude);
// 			//     break;
// 			//   }
// 			// }

// 			const isFirst = dayIndex === 0 && cityIndex === 0;
// 			const isLast = dayIndex === days.length - 1;

// 			const type = isFirst
// 				? 'start'
// 				: isLast
// 				? 'end'
// 				: city.title.toLowerCase().includes('self guided') || city.title.toLowerCase().includes('optional')
// 				? 'optional'
// 				: 'visited';

// 			// Look for transport
// 			let transport = undefined;
// 			for (const ex of excursions) {
// 				if (ex.excursionType === 'intercity' && ex.selected_excursion?.transportDetails?.[0]?.transferMode) {
// 					transport = ex.selected_excursion.transportDetails[0].transferMode.toLowerCase();
// 					break;
// 				}
// 			}

// 			result.push({
// 				name,
// 				city_name,
// 				type,
// 				...(transport ? { transport } : {}),
// 			});
// 		});
// 	});
// 	return (
// 		<div className="p-4">
// 			<h2 className="text-lg sm:text-xl font-semibold text-gray-900">Destination Route</h2>
// 			<MapComponent data_to_plot={result}/>
// 			<div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
// 				<LegendItem color="green" label="Start Location" />
// 				<LegendItem color="red" label="End Location" />
// 				<LegendItem color="blue" label="Visited Location" />
// 				<LegendItem color="yellow" label="Optional Location" />
// 				<LegendItem color="#FF0000" label="Plane Route" />
// 				<LegendItem color="#0000FF" label="Train Route" />
// 				<LegendItem color="#00BFFF" label="Cruise Route" />
// 				<LegendItem color="#FFA500" label="Bus Route" />
// 			</div>
// 		</div>
// 	);
// }

// function LegendItem({ color, label }: { color: string; label: string }) {
// 	return (
// 		<div className="flex items-center gap-2">
// 			<div
// 				className="w-4 h-4 rounded-full"
// 				style={{ backgroundColor: color }}
// 			></div>
// 			<span className="text-sm">{label}</span>
// 		</div>
// 	);
// }
