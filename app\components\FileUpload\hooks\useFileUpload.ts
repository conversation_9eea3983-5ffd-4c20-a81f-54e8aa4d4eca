import React, { useState } from 'react'

const useFileUpload = (uploadedFileName:any, setUploadedFileName:any,fileUpload:any, setFileUpload:any,file_extensions_allowed:any) => {
	  const [isDragging, setIsDragging] = useState(false);
	 const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);

    const file = event.dataTransfer.files[0];
    // if (file && (file.name.endsWith(".xlsx") || file.name.endsWith(".xls"))) {
    if (file && file_extensions_allowed.includes(file.name.slice(-4))) {
      handleFileUpload(file);
    }
  };

const handleFileUpload = (file: File) => {
    if (file) {
      setUploadedFileName(file.name); // Save file name
      console.log("File uploaded:", file);
      setFileUpload(file);
    } else {
    setFileUpload(null);
    }
  };
	const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>)=>{
    const file = event.target.files?.[0];
     if (file) {
      handleFileUpload(file);
    } else{
      setFileUpload(null);
      setUploadedFileName(null)
    }
  }
	return ({
	fileUpload,
	isDragging,
	uploadedFileName,
	handleDragOver,
	handleDragLeave,
	handleDrop,
	handleFileUpload,
	handleFileInputChange
	})
}

export default useFileUpload