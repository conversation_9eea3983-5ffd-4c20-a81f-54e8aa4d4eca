"use client";
import "react-loading-skeleton/dist/skeleton.css";
import { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import CustomCheckbox from '../utility/checkbox';
import axios from 'axios';
import { useRouter } from 'next/navigation';
import { useCurrencyStore } from '../store/useCurrencyStore';
import ShimmerCardVacation from './ShimmerCardVacation/ShimmerCardVacation';
import Skeleton from 'react-loading-skeleton';

// Define Types for Destination Data
interface Destination {
  id: number;
  image: string;
  title: string;
  tags: string[];
  country: string[];
  price: string;
  nights: string;
  hotels: number;
  transfers: number;
  activities: number;
}

interface CountryFlags {
  [key: string]: string;
}

// Sample data in a JSON object
const destinationsData: Destination[] = [
  {
    id: 1,
    image: '/assets/tours/imageone.png', // Use your actual image path
    title: 'Paris & Berlin in 6 days with included Disneyland visit',
    tags: ['Adventure', 'Culture', 'Honeymoon', 'Western Europe'],
    country: ['France', 'Germany'],
    price: '1,05,000',
    nights: '6 Days 5 Nights',
    hotels: 2,
    transfers: 3,
    activities: 3
  },
  {
    id: 2,
    image: '/assets/tours/imagetwo.png', // Use your actual image path
    title: 'Russian Highlights: A journey through Moscow and Russian history',
    tags: ['Adventure', 'Culture', 'Honeymoon', 'Western Europe'],
    country: ['Russia'],
    price: '86,000',
    nights: '6 Days 5 Nights',
    hotels: 2,
    transfers: 3,
    activities: 3
  },
  // Add more sample data here...
];

// Add new filter state interfaces
interface FilterState {
  tourType: string[];
  theme: string[];
  country: string[];
  start_city: string[];
  end_city: string[];
  availability_month: string[];
  packages_on_offer: string[];
  priceRange: {
    min: number;
    max: number;
  };
  duration: string[];
  duration_range:{
    min: number;
    max: number;
  }
}

interface SearchResult {
  id: number;
  packageName: string;
  packageTitle?: string;
  packageMainImage?: string;
  // destination: string;
  destination: any;
  duration: string;
  priceSummary: number | null;
  imageLink: string;
  tags?: string[];
  country?: string | string[];
  hotels?: number;
  transfers?: number;
  activities?: number;
  noOfDays?: number;
  noOfNights?: number;
  packageType?: string;
  packageTheme?: string;
  region?: string;
  price?:string|number
}

interface DestinationTableProps {
  searchResults: SearchResult[];
  loading: boolean;
  error: string | null;
  searchQuery: string | null;
}

export const countryFlags:any = {
  india: "IN",
  "united states": "US",
  "united kingdom": "GB",
  italy: "IT",
  australia: "AU",
  canada: "CA",
  germany: "DE",
  france: "FR",
  japan: "JP",
  china: "CN",
  russia: "RU",
  brazil: "BR",
  "south korea": "KR",
  mexico: "MX",
  spain: "ES",
  argentina: "AR",
  netherlands: "NL",
  sweden: "SE",
  switzerland: "CH",
  singapore: "SG",
  "south africa": "ZA",
  "new zealand": "NZ",
  "saudi arabia": "SA",
  israel: "IL",
  turkey: "TR",
  uae: "AE",
  egypt: "EG",
  thailand: "TH",
  indonesia: "ID",
  malaysia: "MY",
  philippines: "PH",
  vietnam: "VN",
  nigeria: "NG",
  kenya: "KE",
  iraq: "IQ",
  greece: "GR",
  poland: "PL",
  portugal: "PT",
  hungary: "HU",
  "czech republic": "CZ",
  romania: "RO",
  ukraine: "UA",
  belarus: "BY",
  norway: "NO",
  finland: "FI",
  denmark: "DK",
  iceland: "IS",
  estonia: "EE",
  latvia: "LV",
  lithuania: "LT",
  slovakia: "SK",
  bulgaria: "BG",
  serbia: "RS",
  croatia: "HR",
  "bosnia and herzegovina": "BA",
  albania: "AL",
  kosovo: "XK",
  montenegro: "ME",
  "north macedonia": "MK",
  moldova: "MD",
  georgia: "GE",
  armenia: "AM",
  azerbaijan: "AZ",
  uzbekistan: "UZ",
  turkmenistan: "TM",
  kyrgyzstan: "KG",
  tajikistan: "TJ",
  afghanistan: "AF",
  pakistan: "PK",
  bangladesh: "BD",
  nepal: "NP",
  "sri lanka": "LK",
  myanmar: "MM",
  cambodia: "KH",
  laos: "LS",
  brunei: "BN",
  mongolia: "MN",
  bhutan: "BT",
  maldives: "MV"
};

export function countryCodeToEmoji(countryCode:any) {
  return countryCode
    .toUpperCase()
    .replace(/./g, (char:any) => 
      String.fromCodePoint(char.charCodeAt(0) + 127397)
    );
}

const DestinationTable: React.FC<DestinationTableProps> = ({
  searchResults,
  loading,
  error,
  searchQuery
}) => {
  const router = useRouter();
  const convertFromINR = useCurrencyStore(state => state.convertFromINR);
  const symbols = useCurrencyStore(state => state.symbols);
  const selectedCurrency = useCurrencyStore(state => state.selectedCurrency);
  const [activeTab, setActiveTab] = useState<'price-duration' | 'tour-theme'>('price-duration');
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [searchText, setSearchText] = useState('');
  const [localSearchQuery, setLocalSearchQuery] = useState<string>('');
  const [sortOption, setSortOption] = useState<string>('');
  const [activeCategory, setActiveCategory] = useState<'price' | 'duration' | 'tourType' | 'theme' | 'country' | "start_end_ciity" | "availability_month" | "packages_on_offer" >('price');
  const [countryFilter,setCountryFilter] = useState([]);
  const [cityFilter,setCityFilter] = useState<any>([]);
  const [themesFilter,setThemesFilter] = useState<any>([]);
  const [toursFilter,setToursFilter] = useState<any>([]);
  // Update filter state to use categories
  const [filterState, setFilterState] = useState<FilterState>({
    tourType: [],
    theme: [],
    country: [],
    start_city: [],
    end_city: [],
    availability_month: [],
    packages_on_offer: [],
    priceRange: {
      min: 0,
      max: 400000
    },
    duration: [],
    duration_range:{
      min: 1,
      max: 30
    },
  });

  // Add price range state
  const [priceRange, setPriceRange] = useState({
    min: 0,
    max: 400000
  });
  const [duration_range, set_duration_range] = useState({
    min: 1,
    max: 30
  });

  const [filteredResults,setFilteredResults] = useState<any>([]);
  
  // Handle price range change
  const handlePriceChange = (type: 'min' | 'max', value: number) => {
    setPriceRange(prev => ({
      ...prev,
      [type]: value
    }));
  };
  const handleDurationChange = (type: 'min' | 'max', value: number) => {
    set_duration_range(prev => ({
      ...prev,
      [type]: value
    }));
  };

  // Calculate total active filters
  const totalActiveFilters = Object.entries(filterState).reduce((total, [key, value]) => {
    if (key === 'priceRange') {
      return total + (value.min !== 0 || value.max !== 400000 ? 1 : 0);
    }
    if (key === 'duration_range') {
      return total + (value.min !== 1 || value.max !== 30 ? 1 : 0);
    }
    return total + (Array.isArray(value) ? value.length : 0);
  }, 0);

  // Handle filter selection
  const handleFilterSelect = (category: keyof FilterState, value: string) => {
     
    setFilterState(prev => {
      if (category === 'priceRange') {
        if(value == "remove"){
          setPriceRange({ min: 0, max: 400000 });
          return {
            ...prev,
            priceRange: { min: 0, max: 400000 }
          }
          // priceRange: { min: 0, max: 400000 },
        }
        return prev;
      }
      if (category === 'duration_range') {
        if(value == "remove"){
          set_duration_range({ min:1, max: 30 });
          return {
            ...prev,
            duration_range: { min:1, max:30 }
          }
          // priceRange: { min: 0, max: 400000 },
        }
        return prev;
      }
      const currentFilters = prev[category] as string[];
      const newFilters = currentFilters.includes(value)
        ? currentFilters.filter(f => f !== value)
        : [...currentFilters, value];
      return {
        ...prev,
        [category]: (category!=="start_city"  && category!=="end_city")?newFilters: (value!=="" && value!=="remove")?[value]:[]
      };
    });
  };

  // Reset filters
  const resetFilters = () => {
    setPriceRange({ min: 0, max: 400000 });
    set_duration_range({ min: 0, max: 30 });
    setFilterState({
      tourType: [],
      theme: [],
      country: [],
      start_city: [],
      end_city: [],
      availability_month: [],
      packages_on_offer: [],
      priceRange: { min: 0, max: 400000 },
      duration: [],
      duration_range:{
      min: 1,
      max: 30
    },
    });
  };

  // Apply filters and close modal
  const applyFilters = () => {
    //  
    setFilterState(prev => ({
      ...prev,
      duration_range:duration_range,
      priceRange: priceRange
    }));
    setModalOpen(false);
  };
  useEffect(()=>{
    setSearchText("");
  },[modalOpen])
  useEffect(()=>{
     
    console.log("filterState",filterState);
  },[filterState])
  // Handle search input change
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearchQuery(e.target.value);
  };
  function getMonthsFromNowForTwoYears() {
    const months = [];
    const startDate = new Date();
    const startMonth = startDate.getMonth(); // 0-based
    const startYear = startDate.getFullYear();

    const totalMonths = 24;

    for (let i = 0; i <= totalMonths; i++) {
      const month = (startMonth + i) % 12;
      const year = startYear + Math.floor((startMonth + i) / 12);

      const monthName = new Date(year, month).toLocaleString('default', { month: 'long' });
      const value = `${monthName}-${year}`;  // e.g. May-2025

      months.push({ value, label: `${monthName} ${year}` });
    }

    return months;
  }
  const [availability_month_list,setAavailability_month_list] = useState(()=>{
    const data = getMonthsFromNowForTwoYears();
    return data
  });

  // Handle sort option change
  const handleSort = (e: React.ChangeEvent<HTMLSelectElement>) => setSortOption(e.target.value);

useEffect(() => {
    const fetchAllFilters = async () => {
      try {
        const [
          destinationsRes,
          citiesRes,
          toursRes,
          themesRes
        ] = await Promise.all([
          axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/cities/destination-tree-map`),
          axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/cities`),
          axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/package-extras/package-type`),
          axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/package-extras/package-theme`)
        ]);
        const countries_data = destinationsRes.data.continents.flatMap((continent: any) =>
          continent?.regions?.flatMap((region: any) =>
            region.countries?.map((country: any) => country.countryName)
          ) || []
        );
        setCountryFilter(countries_data);
        const activeCities = citiesRes.data.filter((city: any) => city.status.toLowerCase() === "active");
        setCityFilter(activeCities);
        setToursFilter(toursRes.data);
        setThemesFilter(themesRes.data);
      } catch (error) {
        console.error("Error fetching filters:", error);
      }
    };
    fetchAllFilters();
  }, []);

  useEffect(()=>{
  // Filter results based on local search
  let filteredResults = searchResults.filter((ele:any)=>ele.status.toLowerCase()=="active").filter(result => {
    const searchTerm = localSearchQuery.toLowerCase();
    return (
      result.packageName?.toLowerCase().includes(searchTerm) ||
      result.packageTitle?.toLowerCase().includes(searchTerm) ||
      // result.destination?.destinationName?.toLowerCase().includes(searchTerm) ||
      result.country?.toString().toLowerCase().includes(searchTerm) ||
      result.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  });
  // if(!filteredResults.some((data)=>data?.price == "0.00" || data?.price == 0)){
    filteredResults = filteredResults.filter((element)=>{
      if(!element?.price){
        // return false
        return true;
      }
      return Number(element?.price) <= filterState?.priceRange?.max && Number(element?.price) >= filterState?.priceRange?.min 
    })
    filteredResults = filteredResults.filter((element:any)=>{
      let package_countries = element.country.split(",").map((str:any)=>str.trim());
      return filterState?.country.every((val: string) => package_countries.includes(val));
    })
  // }
  let test:any = {
    "1-3-days":{min:1,max:3},
    "4-6-days":{min:4,max:6},
    "7-plus-days":{min:7,max:null},
  }
    filteredResults = filteredResults.filter((element)=>{
     return element.noOfDays as any >= filterState?.duration_range?.min && element.noOfDays as any <= filterState?.duration_range?.max
    })
  
  if(filterState.theme.length!==0){
    filteredResults = filteredResults.filter((element)=>{
      return filterState.theme.some((theme)=>{
        return theme.toLowerCase() == element.packageTheme?.toLowerCase();
      })
     })
  }
  if(filterState.tourType.length!==0){
    filteredResults = filteredResults.filter((element)=>{
      return filterState.tourType.some((tourType)=>{
        return tourType.toLowerCase() == element.packageType?.toLowerCase();
      })
     })
  }

  if(sortOption!=""){
    console.log("before sort filteredResults",filteredResults)

    filteredResults = filteredResults.sort((a:any,b:any)=>{
      if(sortOption =="duration"){
        // price
        // duration
        return a.noOfDays - b.noOfDays
      } else{
        return a.price - b.price
      }
    })
    console.log("after sort filteredResults",filteredResults)
  }
  setFilteredResults(filteredResults);
  },[localSearchQuery,searchResults,filterState,sortOption])

  if (loading) {
    return (
      <>
        <div className="flex justify-between items-center mb-[35px] flex-wrap gap-4 px-4 lg:px-10">
          <div className="flex items-center">
            <div className="relative">
              <div className="px-4 py-2 border rounded-full border-gray-400 text-black flex items-center">
                <Skeleton circle height={16} width={16} className="mr-2" />
                <Skeleton width={50} height={16} />
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-4 w-full md:w-auto">
            <div className="relative flex-1 md:w-[320px]">
              <Skeleton height={40} borderRadius={9999} />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                <Skeleton circle height={20} width={20} />
              </div>
            </div>
            <div className="relative w-full md:w-[200px]">
              <Skeleton height={40} borderRadius={9999} />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <Skeleton circle height={20} width={20} />
              </div>
            </div>
          </div>
        </div>
        <div>
          <ShimmerCardVacation/>
          <ShimmerCardVacation/>
          <ShimmerCardVacation/>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center text-red-600">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 container mx-auto max-w-7xl pt-0">
      {/* Filter and Search Bar - Responsive Layout */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center mb-[35px] gap-4">
        {/* Filter Section */}
        <div className="w-full lg:w-auto">
          {totalActiveFilters === 0 ? (
            <div className="flex items-center">
              <div className="relative">
                <button
                  onClick={() => setModalOpen(true)}
                  className="px-4 py-2 border rounded-full border-gray-400 text-black flex items-center"
                >
                  <i className="fas fa-filter mr-2 text-black"></i>
                  Filter
                </button>
              </div>
            </div>
          ) : (
            <div className="w-full">
              <div className="flex flex-wrap items-center gap-2 lg:gap-4">
                {Object.entries(filterState).map(([category, filters]) => {
                  return (
                    <>
                      {
                        filters && filters.length !==0 && category !="priceRange" && category !="duration_range" && category !="start_city" && category !="end_city" && filters.map((filter: string, index: number) => 
                          {   
                           return (
                              <div
                              key={`${category}-${index}`}
                              className="text-sm font-semibold leading-[20px] tracking-[0] text-[#344054] font-[Manrope] flex items-center space-x-1 border border-gray-400 rounded-full px-4 py-[11px] max-w-full"
                          >
                            <span className="truncate">{filter}</span>
                            <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleFilterSelect(category as keyof FilterState, filter);
                                }}
                                className="text-black flex-shrink-0"
                            >
                              <img src="/assets/cross_icon.svg" className="ml-[10px]" loading="lazy"/>
                            </button>
                          </div> 
                            )
                          }
                          )
                      }
                      {
                        filters && filters.length !==0 && (category =="start_city" ||	 category =="end_city")&& 
                         <div
                              key={`start-city_end-city`}
                              className="text-sm font-semibold leading-[20px] tracking-[0] text-[#344054] font-[Manrope] flex items-center space-x-1 border border-gray-400 rounded-full px-4 py-[11px] max-w-full"
                          >
                            <span className="truncate">{category =="start_city" && `Start - ${filters[0]}` } {category =="end_city" && `End - ${filters[0]}` }</span>
                            <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleFilterSelect(category as keyof FilterState,"remove");
                                }}
                                className="text-black flex-shrink-0"
                            >
                              <img src="/assets/cross_icon.svg" className="ml-[10px]" loading="lazy"/>
                            </button>
                          </div> 
                      }
                      {
                        filters && filters.length !==0 && category =="priceRange" && (filters?.min!==0 || filters?.max!==400000 ) &&
                              <div
                              key={`${category}-${"key"}`}
                              className="text-sm font-semibold leading-[20px] tracking-[0] text-[#344054] font-[Manrope] flex items-center space-x-1 border border-gray-400 rounded-full px-4 py-[11px] max-w-full"
                          >
                            <span className="truncate">{` ₹ ${filters?.min}- ₹ ${filters?.max}` }</span>
                            <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleFilterSelect(category as keyof FilterState, "remove");
                                }}
                                className="text-black flex-shrink-0"
                            >
                              <img src="/assets/cross_icon.svg" className="ml-[10px]" loading="lazy"/>
                            </button>
                          </div> 
                      }
                      {
                        filters && filters.length !==0 && category =="duration_range" && (filters?.min!==1 || filters?.max!==30 ) &&
                              <div
                              key={`${category}-${"key"}`}
                              className="text-sm font-semibold leading-[20px] tracking-[0] text-[#344054] font-[Manrope] flex items-center space-x-1 border border-gray-400 rounded-full px-4 py-[11px] max-w-full"
                          >
                            <span className="truncate">{`  ${filters?.min}-  ${filters?.max} days` }</span>
                            <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleFilterSelect(category as keyof FilterState, "remove");
                                }}
                                className="text-black flex-shrink-0"
                            >
                              <img src="/assets/cross_icon.svg" className="ml-[10px]" loading="lazy"/>
                            </button>
                          </div> 
                      }
                    </>
                  )
                  
                }
                )}
            {totalActiveFilters > 0 && (
                  <div className="relative">
                <button
                      onClick={() => setModalOpen(true)}
                      className="font-semibold text-[#175CD3] font-[Manrope] px-4 py-2 border rounded-full border-blue-600 flex items-center whitespace-nowrap"
                >
                      <img src ="/assets/Icon.svg" className="mr-[10px]" loading="lazy"/>
                  Filters
                      <div className="absolute top-[-10px] right-[1px] bg-blue-100 border border-blue-600 text-blue-600 text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {totalActiveFilters}
                      </div>
                </button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Search and Sort Section */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4 w-full lg:w-auto">
          {/* Search Bar */}
          <div className="relative flex-1 sm:w-[320px]">
            <input
              type="text"
              placeholder="Search Destinations"
              value={localSearchQuery}
              onChange={handleSearch}
              className="px-4 py-2 pl-10 border-[#D0D5DD] border-[1px] rounded-full bg-white focus:outline-none text-black placeholder:text-gray-400 w-full"
            />
            <img src="/assets/serach_icon.svg" className='absolute left-3 top-1/2 transform -translate-y-1/2' loading="lazy"/>
          </div>

          {/* Sort Dropdown */}
          <div className="relative w-full sm:w-[200px]">
            <select
              value={sortOption}
              onChange={handleSort}
              className="text-base font-normal text-[#667085] font-[Manrope] px-4 py-2 pr-8 border rounded-full w-full bg-white focus:outline-none appearance-none"
            >
              <option value="">Sort By</option>
              <option value="price">Price</option>
              <option value="duration">Duration</option>
            </select>
            <img src="/assets/arrow_down.svg" className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#667085]" loading="lazy"/>
          </div>
        </div>
      </div>

      {/* Table of Destinations */}
      <div className="border-t pt-[35px]">
        <div className="space-y-6 lg:space-y-12">
          {filteredResults.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-16 h-16 text-gray-300 mb-4">
                <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
              </svg>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">No results found</h3>
              <p className="text-gray-500 text-center max-w-md">
                We could not find any destinations matching `{localSearchQuery}`. Try adjusting your search terms or filters.
              </p>
            </div>
          ) : (
            filteredResults.map((result:any,index:any) => {
            const max_discount_rule = result?.applicableDiscountRules ? [...result?.applicableDiscountRules || []].sort((a:any,b:any)=>b?.discountPercentage-a?.discountPercentage)[0] :null;
            let price_to_show = max_discount_rule? result.netSellingPrice - (result.netSellingPrice*(max_discount_rule?.discountPercentage/100)): result.netSellingPrice;
            price_to_show = convertFromINR(price_to_show);
              return (
            <div key={`${result.id}-${index}`} className="bg-white rounded-2xl overflow-hidden cursor-pointer">
              <Link href={`/tours/package-details?query=${result.packageCode}`}>
              <div className="flex flex-col lg:flex-row gap-4 lg:gap-0">
                {/* Left side - Image */}
                <div className="w-full lg:w-[355px] h-[200px] lg:h-[200px] flex-shrink-0">
                  {result.packageMainImage ? (
                    <Image
                      src={result.packageMainImage}
                      width={355}
                      height={200}
                      alt={result.packageName || 'Tour package image'}
                      className="w-full h-full object-cover rounded-3xl lg:rounded-l-3xl"
                      loading="lazy"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center rounded-3xl lg:rounded-l-3xl lg:rounded-r-none">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-16 h-16 text-gray-400">
                        <path strokeLinecap="round" strokeLinejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
                      </svg>
                    </div>
                  )}
                </div>

                {/* Right side - Content */}
                <div className="w-full lg:flex-1 p-4 lg:p-6 lg:pt-1 lg:pl-[20px]">
                  {/* Top Section */}
                  <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
                    {/* Left content */}
                    <div className="flex-1 min-w-0">
                      {/* Package Attributes */}
                      <div className="flex flex-wrap items-center text-sm text-gray-500 mb-2">
                        {[result.packageType, result.packageTheme, ...result.region.split(",")]
                          .filter(Boolean)
                          .map((attribute, index, array) => {
                            return (
                              <span key={`${index}-attribute`} className="flex items-center text-xs text-[#667085] font-[Manrope] font-normal">
                                {attribute}
                                {index < array.length - 1 && <span className="mx-[1px]">•</span>}
                              </span>
                            )
                          })}
                      </div>

                      {/* Tags */}
                      <div className="flex flex-wrap gap-2 mb-2">
                        {result.tags?.flatMap((tag:any) => 
                          tag.split(',').map((subTag:any, subIndex:any) => (
                            <span 
                              key={`${tag}-${subIndex}`} 
                              className="text-sm text-gray-500 bg-gray-50 px-3 py-1 rounded-full"
                            >
                              {subTag.trim()}
                            </span>
                          ))
                        )}
                      </div>

                      {/* Title */}
                      <h2 className="text-lg lg:text-[24px] font-semibold text-[#175CD3] font-[Manrope] mb-4 line-clamp-2">
                        {result.packageTitle || 'Unnamed Package'}
                      </h2>

                      {/* Country Flags */}
                      <div className="flex flex-wrap gap-2 mb-2">
                        {Array.isArray(result.country) ? (
                          result.country.flatMap((country: string) => 
                            country.split(',').map((singleCountry: string, index: number) => (
                              <div key={`${country}-${index}`} className="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full">
                                <span className="text-[12px] text-black flex items-center">
                                  <img 
                                    src={`/assets/flags/${singleCountry.trim().split(" ").join("-").toLowerCase()}.svg`} 
                                    className="w-3 h-2.5 mr-1" 
                                    alt="countryImg"
                                    loading="lazy"
                                  /> 
                                  {singleCountry.trim()}
                                </span>
                              </div>
                            ))
                          )
                        ) : result.country ? (
                          result.country.split(',').map((singleCountry: string, index: number) => (
                            <div key={`${index}-country`} className="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full">
                              <span className="text-[12px] text-black flex items-center">
                                <img 
                                  src={`/assets/flags/${singleCountry.trim().split(" ").join("-").toLowerCase()}.svg`} 
                                  className="w-3 h-2.5 mr-1" 
                                  alt="countryImg"
                                  loading="lazy"
                                />
                                {singleCountry.trim()}
                              </span>
                            </div>
                          ))
                        ) : null}
                      </div>
                    </div>

                    {/* Right content - Price and Duration */}
                    <div className="text-left lg:text-right flex-shrink-0">
                      {convertFromINR(result?.grossSellingPrice) > price_to_show ? (
                        <p className="text-xs text-[#98A2B3] line-through decoration-[#98A2B3] my-1">
                          {symbols[selectedCurrency]}{' '}
                          {Math.round(convertFromINR(result?.grossSellingPrice)).toLocaleString()}
                        </p>
                      ) : (
                        <p></p>
                      )}
                      <div className="flex flex-col lg:items-end">
                        <div className="flex items-baseline gap-1">
                          <span className="font-manrope text-xl lg:text-[30px] text-black font-bold">
                            {symbols[`${selectedCurrency}`]} {Math.round(price_to_show)?.toLocaleString() && Math.round(price_to_show)?.toLocaleString() !== "0" && Math.round(price_to_show)?.toLocaleString() || '10,000'}
                          </span>
                          <span className="font-manrope font-normal text-sm lg:text-base text-black">per person</span>
                        </div>
                        <p className="text-sm text-black pt-2">
                          {result.noOfDays && result.noOfNights ? (
                            <>
                              <span className="font-manrope font-bold text-base">{result.noOfDays}</span> Days{' '}
                              <span className="font-manrope font-bold text-base">{result.noOfNights}</span> Nights
                            </>
                          ) : 'Duration not specified'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Bottom Section */}
                  <div className="flex flex-col lg:flex-row lg:justify-between lg:items-end mt-6 gap-4">
                    {/* What's included */}
                    <div className="flex-1">
                      <p className="text-xs font-bold text-[#667085] mb-2">What is included</p>
                      <div className="flex flex-wrap gap-4 lg:gap-6">
                        <div className="flex items-center gap-2">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-gray-500 flex-shrink-0">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z" />
                          </svg>
                          <span className="font-manrope font-medium text-sm text-[#667085]">Hotels</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-gray-500 flex-shrink-0">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12" />
                          </svg>
                          <span className="font-manrope font-medium text-sm text-[#667085]">Transfers</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-gray-500 flex-shrink-0">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                          </svg>
                          <span className="font-manrope font-medium text-sm text-[#667085]">Activities</span>
                        </div>
                      </div>
                    </div>

                    {/* Explore Now Button */}
                    <Link 
                      href={`/tours/package-details?query=${result.packageCode}`}
                      className="bg-blue-600 text-white px-6 lg:px-8 py-3 rounded-full hover:bg-blue-700 transition font-manrope font-semibold text-base lg:text-lg text-center whitespace-nowrap"
                    >
                      Explore Now
                    </Link>
                  </div>
                </div>
              </div>
            </Link>
            </div>
              )
            })
          )}
        </div>
      </div>

      {/* Filter Modal - Responsive */}
      {modalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
          <div className="bg-white rounded-[20px] shadow-lg w-full max-w-[600px] max-h-[90vh] relative overflow-hidden">
            {/* Close button */}
            <button 
              onClick={() => setModalOpen(false)}
              className="absolute right-4 top-4 text-gray-400 hover:text-gray-600 z-10"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>

            {/* Header */}
            <div className="bg-blue-50 p-4 lg:p-6">
              <h3 className="text-xl font-semibold text-black">Filters</h3>
              <p className="text-[#5E718D] font-[Manrope] text-sm font-normal mt-1">Compare tours based on different filters.</p>
            </div>

            <div className="flex flex-col lg:flex-row h-[calc(90vh-200px)] lg:h-auto">
              {/* Left Side - Categories */}
              <div className="w-full lg:w-48 border-r border-gray-200 max-h-[30vh] lg:max-h-[60vh] overflow-y-auto" style={{
                msOverflowStyle:"none",
                scrollbarWidth:"none"
              }}>
                {[
                  { key: 'price', label: 'Price per person', condition: filterState.priceRange.min !== 0 || filterState.priceRange.max !== 400000 },
                  { key: 'duration', label: 'Tour Duration', condition: filterState.duration.length > 0 },
                  { key: 'tourType', label: 'Tour Type', condition: filterState.tourType.length > 0 },
                  { key: 'theme', label: 'Theme', condition: filterState.theme.length > 0 },
                  { key: 'country', label: 'Country', condition: filterState.country.length > 0 },
                  { key: 'start_end_ciity', label: 'Start City - End City', condition: filterState.start_city.length > 0 || filterState.end_city.length > 0 },
                  { key: 'availability_month', label: 'Availability Month', condition: filterState.availability_month.length > 0 },
                  { key: 'packages_on_offer', label: 'Packages on Offer', condition: filterState.packages_on_offer.length > 0 }
                ].map((item, index) => (
                  <div key={item.key}>
                    <button
                      onClick={() => setActiveCategory(item.key as any)}
                      className={`w-full text-left px-4 lg:px-6 py-3 lg:py-4 text-sm font-medium cursor-pointer ${
                        activeCategory === item.key ? 'text-blue-600' : 'text-gray-500 hover:text-gray-700'
                      }`}
                    >
                      {item.label} {item.condition && <span className="text-blue-600">({
                        item.key === 'price' ? 1 :
                        item.key === 'start_end_ciity' ? 1 :
                        Array.isArray(filterState[item.key as keyof FilterState]) ? (filterState[item.key as keyof FilterState] as string[]).length : 0
                      })</span>}
                    </button>
                    {index < 7 && <hr/>}
                  </div>
                ))}
              </div>

              {/* Right Side - Content */}
              <div className="flex-1 p-4 lg:p-6 max-h-[50vh] lg:max-h-[60vh] overflow-y-auto">
                {activeCategory === 'price' && (
                  <div>
                    <div className="mb-6">
                      <p className="text-sm text-gray-600 mb-2">Selected range</p>
                      <p className="text-black text-lg">
                        ₹ {priceRange.min.toLocaleString('en-IN')} - ₹ {priceRange.max.toLocaleString('en-IN')}
                      </p>
                    </div>
                  
                    {/* Price Range Slider */}
                    <div className="relative pt-6">
                      {/* Bar Chart Background */}
                      <div className="h-16 lg:h-24 flex items-end space-x-1 mb-2">
                        {Array.from({ length: 20 }).map((_, index) => (
                          <div
                            key={`${index}-bar_chart`}
                            className="flex-1 bg-gray-100 rounded-t"
                            style={{ height: `${Math.random() * 100}%` }}
                          ></div>
                        ))}
                      </div>
                  
                      {/* Range Slider Container */}
                      <div className="relative h-6 mb-4">
                        {/* Track Background */}
                        <div className="absolute top-2 w-full h-2 bg-gray-200 rounded-full"></div>

                        {/* Active Track */}
                        <div
                          className="absolute top-2 h-2 bg-blue-600 rounded-full"
                          style={{
                            left: `${((priceRange.min - 0) / (400000 - 0)) * 100}%`,
                            width: `${((priceRange.max - priceRange.min) / (400000 - 0)) * 100}%`,
                          }}
                        ></div>

                        {/* Min Range Input */}
                        <input
                          type="range"
                          min="0"
                          max="400000"
                          step="1000"
                          value={priceRange.min}
                          onChange={(e) => {
                            const value = Number(e.target.value);
                            if (value <= priceRange.max - 1000) {
                              handlePriceChange('min', value);
                            }
                          }}
                          className="absolute w-full h-6 appearance-none bg-transparent cursor-pointer slider-thumb"
                          style={{
                            zIndex: 1,
                          }}
                        />

                        {/* Max Range Input */}
                        <input
                          type="range"
                          min="0"
                          max="400000"
                          step="1000"
                          value={priceRange.max}
                          onChange={(e) => {
                            const value = Number(e.target.value);
                            if (value >= priceRange.min + 1000) {
                              handlePriceChange('max', value);
                            }
                          }}
                          className="absolute w-full h-6 appearance-none bg-transparent cursor-pointer slider-thumb"
                          style={{
                            zIndex: 2,
                          }}
                        />
                      </div>

                      {/* Price Labels */}
                      <div className="flex justify-between text-sm text-gray-500 mt-2">
                        <span>₹0</span>
                        <span>₹4,00,000</span>
                      </div>
                    </div>
                  </div>
                )}

                {activeCategory === 'duration' && (
                  <div>
                    <div className="mb-6">
                      <p className="text-sm text-gray-600 mb-2">Selected duration</p>
                      <p className="text-black text-lg">
                        {duration_range.min} {duration_range.min === 1 ? 'Day' : 'Days'} - {duration_range.max === 30 ? '30+ Days' : duration_range.max + ' Days'}
                      </p>
                    </div>

                    {/* Duration Range Slider */}
                    <div className="relative pt-6">
                      {/* Bar Chart Background */}
                      <div className="h-16 lg:h-24 flex items-end space-x-1 mb-2">
                        {Array.from({ length: 30 }).map((_, index) => (
                          <div
                            key={`${index}-bar_chart_duration`}
                            className="flex-1 bg-gray-100 rounded-t"
                            style={{ height: `${Math.random() * 100}%` }}
                          ></div>
                        ))}
                      </div>

                      {/* Range Slider Container */}
                      <div className="relative h-6 mb-4">
                        {/* Track Background */}
                        <div className="absolute top-2 w-full h-2 bg-gray-200 rounded-full"></div>

                        {/* Active Track */}
                        <div
                          className="absolute top-2 h-2 bg-blue-600 rounded-full"
                          style={{
                            left: `${((duration_range.min - 1) / (30 - 1)) * 100}%`,
                            width: `${((duration_range.max - duration_range.min) / (30 - 1)) * 100}%`,
                          }}
                        ></div>

                        {/* Min Range Input */}
                        <input
                          type="range"
                          min="1"
                          max="30"
                          step="1"
                          value={duration_range.min}
                          onChange={(e) => {
                            const value = Number(e.target.value);
                            if (value <= duration_range.max - 1) {
                              handleDurationChange('min', value);
                            }
                          }}
                          className="absolute w-full h-6 appearance-none bg-transparent cursor-pointer slider-thumb"
                          style={{
                            zIndex: 1,
                          }}
                        />

                        {/* Max Range Input */}
                        <input
                          type="range"
                          min="1"
                          max="30"
                          step="1"
                          value={duration_range.max}
                          onChange={(e) => {
                            const value = Number(e.target.value);
                            if (value >= duration_range.min + 1) {
                              handleDurationChange('max', value);
                            }
                          }}
                          className="absolute w-full h-6 appearance-none bg-transparent cursor-pointer slider-thumb"
                          style={{
                            zIndex: 2,
                          }}
                        />
                      </div>

                      {/* Duration Labels */}
                      <div className="flex justify-between text-sm text-gray-500 mt-2">
                        <span>1 Day</span>
                        <span>30+ Days</span>
                      </div>
                    </div>
                  </div>
                )}

                {activeCategory === 'tourType' && (
                  <div>
                    <div className="space-y-2">
                      {toursFilter.sort((x:any,y:any)=>x.packageTypeName.localeCompare(y.packageTypeName)).filter((ele:any)=>ele?.status == "ACTIVE").map((type:any) => (
                         <CustomCheckbox
                            key={type?.packageTypeName+"_"+"FIlter"}
                            checked={filterState.tourType.includes(type?.packageTypeName)}
                            onChange={() => handleFilterSelect('tourType', type?.packageTypeName)}
                            className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                            label_text={type?.packageTypeName}
                          />
                      ))}
                    </div>
                  </div>
                )}

                {activeCategory === 'theme' && (
                  <div>
                    <div className="space-y-2">
                      {themesFilter.sort((x:any,y:any)=>x.packageThemeName.localeCompare(y.packageThemeName)).filter((ele:any)=>ele?.status == "ACTIVE").map((theme:any) => (
                         <CustomCheckbox
                            key={theme?.packageThemeName+"_"+"FIlter"}
                            checked={filterState.theme.includes(theme?.packageThemeName)}
                            onChange={() => handleFilterSelect('theme', theme?.packageThemeName)}
                            className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                            label_text={theme?.packageThemeName}
                          />
                      ))}
                    </div>
                  </div>
                )}

                {activeCategory === 'country' && (
                  <div>
                    <input
                      type="text"
                      placeholder="Search country..."
                      value={searchText}
                      onChange={(e) => setSearchText(e.target.value)}
                      className="w-full px-2 py-1 mb-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />

                    <div className="space-y-2">
                      {countryFilter
                        .filter((country:any) =>
                          country.toLowerCase().includes(searchText.toLowerCase())
                        ).sort((x:any,y:any)=>x.localeCompare(y)).map((country) => (
                         <CustomCheckbox
                            key={country+"_"+"FIlter"}
                            checked={filterState.country.includes(country)}
                            onChange={() => handleFilterSelect('country', country)}
                            className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                            label_text={country}
                          />
                      ))}
                    </div>
                    {
                      countryFilter
                      .filter((country:any) =>
                        country.toLowerCase().includes(searchText.toLowerCase())
                      ).length==0 &&
                      <p className='text-sm text-center'>No Results found</p>
                    }
                  </div>
                )}

                {activeCategory === 'start_end_ciity' && (
                  <div className='flex flex-col gap-5'>
                    <select 
                      value={filterState.start_city[0]}
                      onChange={(e) => handleFilterSelect('start_city', e.target.value)}
                      className={'w-full px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 hover:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition'}
                    >
                      <option value="">Select Start City</option>
                      {
                        cityFilter.sort((a:any,b:any)=>{
                          return a.cityName.localeCompare(b.cityName);
                        }).map((city:any)=>{
                          return <option key={`${city?.cityName}-key`} value={city?.cityName}>{city?.cityName}</option>
                        })
                      }
                    </select>
                    <select 
                      value={filterState.end_city[0]}
                      onChange={(e) => handleFilterSelect('end_city', e.target.value)}
                      className={'w-full px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700  hover:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition'}
                    >
                      <option value="">Select End City</option>
                      {
                        cityFilter.sort((a:any,b:any)=>{
                          return a.cityName.localeCompare(b.cityName);
                        }).map((city:any)=>{
                          return <option key={`${city?.cityName}-end-key`} value={city?.cityName}>{city?.cityName}</option>
                        })
                      }
                    </select>
                  </div>
                )}

                {activeCategory === 'availability_month' && (
                  <div>
                    <div className="space-y-2">
                      {availability_month_list.map((availability_month:any) => {
                        return (
                          <CustomCheckbox
                            key={availability_month.value+"_"+"FIlter"}
                            checked={filterState.availability_month.includes(availability_month.value)}
                            onChange={() => handleFilterSelect('availability_month', availability_month.value)}
                            className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                            label_text={availability_month.label}
                          />
                        )
                      })}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Footer Actions */}
            <div className="p-4 lg:p-6 border-t border-gray-200 flex flex-col sm:flex-row justify-end gap-3">
              <button
                onClick={resetFilters}
                className="px-6 py-2 bg-transparent border border-gray-300 rounded-full text-gray-600 hover:bg-gray-50 font-medium order-2 sm:order-1"
              >
                Reset Filters
              </button>
              <button
                onClick={applyFilters}
                className="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 font-medium order-1 sm:order-2"
              >
                Apply Filter
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .slider-thumb {
          -webkit-appearance: none;
          background: transparent;
          pointer-events: auto;
        }

        .slider-thumb::-webkit-slider-thumb {
          -webkit-appearance: none;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: #ffffff;
          border: 2px solid #2563eb;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          pointer-events: auto;
        }

        .slider-thumb::-moz-range-thumb {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: #ffffff;
          border: 2px solid #2563eb;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          pointer-events: auto;
        }

        .slider-thumb::-webkit-slider-track {
          background: transparent;
        }

        .slider-thumb::-moz-range-track {
          background: transparent;
        }

        .line-clamp-1 {
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </div>
  );
};

export default DestinationTable;