"use client"
import React, { useState } from 'react';
import { AppContext } from '../context/useAppContext';

const ContextProvider = ({ children }: any) => {
  const [bookingData, setBookingData] = useState({
    travellers:[
      {
        firstName: '',
        lastName: '',
        dateOfBirth: '',
        passportNumber: '',
        passportIssueDate: '',
        passportExpiryDate: '',
        nationality: 'India',
        passportIssueCity: '',
        mealPreference: 'veg'
      }
    ],
    date:"",
    rule:null,
    packageCode: "",
  });

  return (
    <AppContext.Provider value={{ bookingData, setBookingData }}>
      {children}
    </AppContext.Provider>
  );
};

export default ContextProvider;
