"use client";

import { useRef } from 'react';

interface NavigationSidebarProps {
  className?: string;
}

const NavigationSidebar: React.FC<NavigationSidebarProps> = ({ className }) => {
  const sidebarRef = useRef<HTMLDivElement>(null);

  // We're not using the component's internal highlighting logic anymore
  // The highlighting is now handled by the parent component (PackageDetails.tsx)
  // This is just a placeholder to maintain the component structure

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      // Scroll with offset to account for sticky header
      const headerOffset = 100;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  const navItems = [
    { id: 'trip-highlights', label: 'Trip Highlights' },
    { id: 'destinations', label: 'Destinations' },
    { id: 'itinerary', label: 'Itinerary' },
    { id: 'inclusion', label: 'Inclusions' },
    { id: 'exclusion', label: 'Exclusions' },
    { id: 'vouchers', label: 'Vouchers' },
    { id: 'payment-policy', label: 'Payment Policy' },
  ];

  return (
    <nav ref={sidebarRef} className={`navigation-sidebar ${className || ''}`}>
      <ul className="space-y-4">
        {navItems.map((item) => (
          <li key={item.id}>
            <a
              href={`#${item.id}`}
              onClick={(e) => {
                e.preventDefault();
                scrollToSection(item.id);
              }}
              className="text-[#475467] hover:text-[#175CD3] font-medium text-sm"
            >
              {item.label}
            </a>
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default NavigationSidebar;