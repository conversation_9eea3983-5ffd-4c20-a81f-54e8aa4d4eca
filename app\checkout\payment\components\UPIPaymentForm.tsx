import { useState, ChangeEvent } from 'react';

interface UPIPaymentFormProps {
  onSubmit: (upiId: string) => Promise<void>;
  isSubmitting: boolean;
}

export const UPIPaymentForm = ({ onSubmit, isSubmitting }: UPIPaymentFormProps) => {
  const [upiId, setUpiId] = useState('');
  const [error, setError] = useState('');
  const [isValidating, setIsValidating] = useState(false);

  const validateUpiId = (vpa: string): boolean => {
    // Basic UPI ID validation
    const upiRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9]+$/;
    return upiRegex.test(vpa);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (!upiId.trim()) {
      setError('Please enter a UPI ID');
      return;
    }

    if (!validateUpiId(upiId)) {
      setError('Please enter a valid UPI ID (e.g., example@upi)');
      return;
    }

    try {
      setIsValidating(true);
      await onSubmit(upiId);
    } catch (err) {
      setError('Failed to process UPI payment. Please try again.');
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          UPI ID
        </label>
        <input
          type="text"
          value={upiId}
          onChange={(e) => setUpiId(e.target.value)}
          placeholder="yourname@upi"
          className="w-full p-3 border rounded-lg text-sm"
        />
        <p className="mt-1 text-xs text-gray-500">
          Enter your UPI ID (e.g., yourname@upi)
        </p>
        {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
      </div>

      <button
        type="submit"
        disabled={isSubmitting || isValidating}
        className={`w-full bg-purple-600 text-white py-3 px-4 rounded-lg font-medium ${
          isSubmitting || isValidating ? 'opacity-70 cursor-not-allowed' : 'hover:bg-purple-700'
        }`}
      >
        {isValidating ? 'Validating...' : isSubmitting ? 'Processing...' : 'Pay with UPI'}
      </button>
    </form>
  );
};
