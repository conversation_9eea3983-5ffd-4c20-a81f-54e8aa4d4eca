// ✅ app/api/generate-pdf/route.ts
import puppeteer from 'puppeteer';

export async function GET(req: Request) {
  const url = new URL(req.url);
  const query = url.searchParams.get('query');
  const isCustom = url.searchParams.get('isCustom');

  if (!query) {
    return new Response(JSON.stringify({ error: 'Missing query' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const pageUrl = isCustom?`${process.env.NEXT_SITE_URL}/package-detail-pdf?query=${query}&custom=${isCustom}`:`${process.env.NEXT_SITE_URL}/package-detail-pdf?query=${query}`;

  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });

  const page = await browser.newPage();
  await page.goto(pageUrl, { waitUntil: 'networkidle0' });
  // await page.goto(pageUrl, { waitUntil: 'networkidle2' });

  const pdfBuffer = await page.pdf({
    format: 'A4',
    printBackground: true,
    margin: { top: '20px', bottom: '20px', left: '20px', right: '20px' },
  });

  await browser.close();

  return new Response(pdfBuffer, {
    status: 200,
    headers: {
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename=itinerary-${query}.pdf`,
    },
  });
}
