import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  try {
    // Check if the request has a body
    if (!req.body) {
      console.error('No request body received');
      return NextResponse.redirect(new URL('/checkout/success?status=failure&error=no_data', req.url));
    }

    const formData = await req.formData();
    
    // Log all fields for debugging
    console.log("🔁 PayU POST response:");
    const entries = Array.from(formData.entries());
    entries.forEach(([key, value]) => {
      console.log(`${key}: ${value}`);
    });

    // Extract payment details
    const status = formData.get('status');
    const txnid = formData.get('txnid');
    const amount = formData.get('amount');
    const productinfo = formData.get('productinfo');
    const firstname = formData.get('firstname');
    const email = formData.get('email');
    const phone = formData.get('phone');
    const hash = formData.get('hash');
    const error = formData.get('error');
    const error_Message = formData.get('error_Message');
    const mihpayid = formData.get('mihpayid');
    const mode = formData.get('mode');

    // Log the incoming request URL for debugging
    const referer = req.headers.get('referer');
    console.log('Incoming request from:', referer);
    console.log('Request URL:', req.url);

    // Get the base URL for redirection
    let baseUrl = process.env.NEXT_PUBLIC_APP_BASE_URL || 'http://localhost:3000';
    
    // Ensure base URL doesn't end with a slash
    baseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    
    // Create the success URL with all parameters
    const successUrl = new URL(`${baseUrl}/checkout/success`);
    
    // Add all payment parameters to the URL
    const params = new URLSearchParams();
    
    // Set status based on PayU response
    const paymentStatus = status?.toString().toLowerCase() === 'success' ? 'success' : 'failure';
    params.set('status', paymentStatus);
    
    // Add all other parameters
    if (txnid) params.set('txnid', txnid.toString());
    if (amount) params.set('amount', amount.toString());
    if (productinfo) params.set('productinfo', productinfo.toString());
    if (firstname) params.set('firstname', firstname.toString());
    if (email) params.set('email', email.toString());
    if (phone) params.set('phone', phone.toString());
    if (hash) params.set('hash', hash.toString());
    if (error) params.set('error', error.toString());
    if (error_Message) params.set('error_Message', error_Message.toString());
    if (mihpayid) params.set('mihpayid', mihpayid.toString());
    if (mode) params.set('mode', mode.toString());
    
    // Add timestamp to prevent caching
    params.set('t', Date.now().toString());
    
    // Add all parameters to the success URL
    successUrl.search = params.toString();
    
    console.log('Redirecting to:', successUrl.toString());
    
    // Return a 303 See Other response for proper redirection
    return new Response(null, {
      status: 303,
      headers: {
        'Location': successUrl.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Error processing PayU response:', error);
    const env = process.env.NEXT_PUBLIC_APP_ENV || 'development';
    const protocol = env === 'production' ? 'https://' : 'http://';
    let host = process.env.NEXT_PUBLIC_APP_BASE_URL || 'localhost:3000';
    host = host.replace(/^https?:\/\//, '').replace(/\/$/, '');
    const redirectUrl = new URL(`${protocol}${host}/checkout/success?status=failure&error=processing_error`);
    
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta http-equiv="refresh" content="0; url=${redirectUrl.toString()}">
          <title>Redirecting...</title>
        </head>
        <body>
          <p>Redirecting to payment status page... <a href="${redirectUrl.toString()}">Click here if not redirected</a></p>
        </body>
      </html>
    `;
    
    return new Response(html, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
      },
    });
  }
}

// Add GET handler to handle direct visits to the API route
export async function GET(req: Request) {
  const env = process.env.NEXT_PUBLIC_APP_ENV || 'development';
  const protocol = env === 'production' ? 'https://' : 'http://';
  let host = process.env.NEXT_PUBLIC_APP_BASE_URL || 'localhost:3000';
  host = host.replace(/^https?:\/\//, '').replace(/\/$/, '');
  const redirectUrl = new URL(`${protocol}${host}/checkout/success?status=failure&error=invalid_request`);
  
  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta http-equiv="refresh" content="0; url=${redirectUrl.toString()}">
        <title>Redirecting...</title>
      </head>
      <body>
        <p>Redirecting to payment status page... <a href="${redirectUrl.toString()}">Click here if not redirected</a></p>
      </body>
    </html>
  `;
  
  return new Response(html, {
    status: 200,
    headers: {
      'Content-Type': 'text/html',
    },
  });
}
